-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
 	
  G = ADekoLib
	minimum = 150
	limit = 300

  
  a = 50          -- Kenardan Vbite
  aa = 30		  -- Dar kapak için Kenardan mesafe
  cT = 6          -- Açılı V üzerinde kanal bıçak çapı
  gaps = 50
  sbt = 10		
  --cR = 5		  -- Radus Veren form bıcak düz uç dar çapı
  -- local aV = 120        -- Vbit Açısı
  --local bd = 3    -- Acili V Ustu kanal derinliği
  -- local d = 10          -- Acili V den iç kanala
  --dd = 2          -- iç kanal derinliği
    
  intGrvExist  = 2	 --iç kanal var mı (Var: Derinlik / Yok: 0)
  grooveVert_Hor = 1 	--intGrvExist varsa çalışır--1 vertical 2 horizontal
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar Pah işlemi var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  topGrooveExist    		= 2   -- Açılı V üzerinde kanal var mı? derinlik/0:yok
  
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
	
	local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit

  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  local tooloffset = cT/2
  if topGrooveExist == 0 then
    a=0
    ust=0
    tooloffset = 0
  end

  local D = edgeCornerRExist
      
	local B = math.tan(math.pi/8)
	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist >0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	

	local vWideDiameter = 60
	-- local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
	local sunkenWidth = 0
	
	local gobekGenislik = X-2*a-2*sunkenWidth-2*sbt
	local gobekGenislik2 = Y-2*ust-2*sunkenWidth-2*sbt
	
	--dikey çizgiler
	local stepX = math.floor(gobekGenislik/gaps)
	
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	local aprxGaps = gobekGenislik/(stepX+1)
	local e = a+sunkenWidth+sbt+aprxGaps/2
	--yatay çizgiler
	local stepY = math.floor(gobekGenislik/gaps)
	
	if ((gobekGenislik2/gaps)-stepY)  >= 0.5 then
		stepY = stepY + 1
	end
	
	local aprxGaps2 = gobekGenislik2/(stepY+1)
	local e2 = ust+sunkenWidth+sbt+aprxGaps2/2
  --burası ortadaki çizgiler^
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
  
	if topGrooveExist > 0 then
		--G.setLayer("K_Ballnose") 
		G.setLayer("K_Kanal"..cT.."mm")  -- 
		G.setThickness(-topGrooveExist)
		distance = a + cT/2
		distance2 = ust + cT/2
		G.line({distance, 0}, {distance, Y})
		G.line({X-distance, 0}, {X-distance, Y})
		G.line({distance, distance2}, {X-distance, distance2})
		G.line({distance, Y-distance2}, {X-distance, Y-distance2})
	end

	if extEdgeVtoolExist > 0 then
		G.setLayer("K_AciliV_Pah")	
		G.setThickness(-extEdgeVtoolExist)
		G.rectangle({0,0},{X,Y})
	end
	if intGrvExist > 0 then      ------İç kanal varsa
			local checkX = 2*a + 2*sunkenWidth
			local checkY = 2*ust + 2*sunkenWidth
			if X > checkX and Y > checkY then
				if grooveVert_Hor == 1 then
					G.setLayer("K_Kanal"..cT.."mm")  -- DEEP cleanup
					G.setThickness(-intGrvExist)
					-- distance = a + sunkenWidth + h + d
					-- point1 = {distance, distance}
					-- point2 = {X-distance, Y-distance}
					-- G.rectangle(point1,point2)
					
					--burası ortadaki çizgiler
					for i=0,stepX do
						local x = e+i*aprxGaps
						local offset = ust + sunkenWidth+ tooloffset
						point3 = {x,offset}
						--point3 = {x,0}
						point4 = {x,Y-offset}  
						--point4 = {x,Y}  
						G.line(point3,point4,0)
						i = i+1
					end 
				elseif grooveVert_Hor == 0 then
					G.setLayer("K_Kanal"..cT.."mm")  -- DEEP cleanup
					G.setThickness(-intGrvExist)
					-- distance = a + sunkenWidth + h + d
					-- point1 = {distance, distance}
					-- point2 = {X-distance, Y-distance}
					-- G.rectangle(point1,point2)
					
					--burası ortadaki çizgiler
					for i=0,stepY do
						local y = e2+i*aprxGaps2
						local offset2 = a + sunkenWidth+ tooloffset/2
						point3 = {offset2,y}
						--point3 = {x,0}
						point4 = {X-offset2,y}  
						--point4 = {x,Y}  
						G.line(point3,point4,0)
						i = i+1
					end 
				end
			end
	end
	
  return true
end

------------------------------------------------

require "ADekoDebugMode"
