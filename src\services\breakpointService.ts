import * as monaco from 'monaco-editor'

export interface Breakpoint {
  id: string
  filePath: string
  lineNumber: number
  enabled: boolean
  condition?: string
  hitCount?: number
}

export interface BreakpointDecoration {
  id: string
  decorationId: string
  lineNumber: number
}

class BreakpointService {
  private breakpoints = new Map<string, Breakpoint>()
  private decorations = new Map<string, BreakpointDecoration[]>()
  private editors = new Map<string, monaco.editor.IStandaloneCodeEditor>()
  private listeners: Array<(breakpoints: Breakpoint[]) => void> = []

  // Register an editor for breakpoint management
  registerEditor(filePath: string, editor: monaco.editor.IStandaloneCodeEditor): void {
    this.editors.set(filePath, editor)
    
    // Add click handler for gutter
    editor.onMouseDown((e) => {
      if (e.target.type === monaco.editor.MouseTargetType.GUTTER_LINE_NUMBERS) {
        const lineNumber = e.target.position?.lineNumber
        if (lineNumber) {
          this.toggleBreakpoint(filePath, lineNumber)
        }
      }
    })

    // Restore existing breakpoints for this file
    this.updateDecorations(filePath)
  }

  // Unregister an editor
  unregisterEditor(filePath: string): void {
    this.editors.delete(filePath)
    this.decorations.delete(filePath)
  }

  // Toggle breakpoint at a specific line
  toggleBreakpoint(filePath: string, lineNumber: number): void {
    const breakpointId = `${filePath}:${lineNumber}`
    
    if (this.breakpoints.has(breakpointId)) {
      // Remove existing breakpoint
      this.breakpoints.delete(breakpointId)
    } else {
      // Add new breakpoint
      const breakpoint: Breakpoint = {
        id: breakpointId,
        filePath,
        lineNumber,
        enabled: true
      }
      this.breakpoints.set(breakpointId, breakpoint)
    }

    this.updateDecorations(filePath)
    this.notifyListeners()
  }

  // Enable/disable a breakpoint
  setBreakpointEnabled(breakpointId: string, enabled: boolean): void {
    const breakpoint = this.breakpoints.get(breakpointId)
    if (breakpoint) {
      breakpoint.enabled = enabled
      this.updateDecorations(breakpoint.filePath)
      this.notifyListeners()
    }
  }

  // Set breakpoint condition
  setBreakpointCondition(breakpointId: string, condition?: string): void {
    const breakpoint = this.breakpoints.get(breakpointId)
    if (breakpoint) {
      breakpoint.condition = condition
      this.notifyListeners()
    }
  }

  // Remove a specific breakpoint
  removeBreakpoint(breakpointId: string): void {
    const breakpoint = this.breakpoints.get(breakpointId)
    if (breakpoint) {
      this.breakpoints.delete(breakpointId)
      this.updateDecorations(breakpoint.filePath)
      this.notifyListeners()
    }
  }

  // Remove all breakpoints
  removeAllBreakpoints(): void {
    const filePaths = new Set<string>()
    this.breakpoints.forEach(bp => filePaths.add(bp.filePath))
    
    this.breakpoints.clear()
    
    filePaths.forEach(filePath => {
      this.updateDecorations(filePath)
    })
    
    this.notifyListeners()
  }

  // Get all breakpoints
  getAllBreakpoints(): Breakpoint[] {
    return Array.from(this.breakpoints.values())
  }

  // Get breakpoints for a specific file
  getBreakpointsForFile(filePath: string): Breakpoint[] {
    return Array.from(this.breakpoints.values()).filter(bp => bp.filePath === filePath)
  }

  // Check if a line has a breakpoint
  hasBreakpoint(filePath: string, lineNumber: number): boolean {
    const breakpointId = `${filePath}:${lineNumber}`
    return this.breakpoints.has(breakpointId)
  }

  // Update decorations for a file
  private updateDecorations(filePath: string): void {
    const editor = this.editors.get(filePath)
    if (!editor) return

    const model = editor.getModel()
    if (!model) return

    // Remove existing decorations
    const existingDecorations = this.decorations.get(filePath) || []
    const decorationIds = existingDecorations.map(d => d.decorationId)
    
    // Get breakpoints for this file
    const fileBreakpoints = this.getBreakpointsForFile(filePath)
    
    // Create new decorations
    const newDecorations: monaco.editor.IModelDeltaDecoration[] = fileBreakpoints.map(bp => ({
      range: new monaco.Range(bp.lineNumber, 1, bp.lineNumber, 1),
      options: {
        isWholeLine: false,
        glyphMarginClassName: bp.enabled ? 'breakpoint-enabled' : 'breakpoint-disabled',
        glyphMarginHoverMessage: {
          value: bp.enabled 
            ? `Breakpoint - Line ${bp.lineNumber}${bp.condition ? ` (Condition: ${bp.condition})` : ''}`
            : `Disabled Breakpoint - Line ${bp.lineNumber}`
        },
        stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges
      }
    }))

    // Apply decorations
    const newDecorationIds = editor.deltaDecorations(decorationIds, newDecorations)
    
    // Store new decoration mappings
    const newDecorationMappings: BreakpointDecoration[] = fileBreakpoints.map((bp, index) => ({
      id: bp.id,
      decorationId: newDecorationIds[index],
      lineNumber: bp.lineNumber
    }))
    
    this.decorations.set(filePath, newDecorationMappings)
  }

  // Add listener for breakpoint changes
  addListener(listener: (breakpoints: Breakpoint[]) => void): void {
    this.listeners.push(listener)
  }

  // Remove listener
  removeListener(listener: (breakpoints: Breakpoint[]) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  // Notify all listeners
  private notifyListeners(): void {
    const breakpoints = this.getAllBreakpoints()
    this.listeners.forEach(listener => listener(breakpoints))
  }

  // Export breakpoints to JSON
  exportBreakpoints(): string {
    const breakpoints = this.getAllBreakpoints()
    return JSON.stringify(breakpoints, null, 2)
  }

  // Import breakpoints from JSON
  importBreakpoints(json: string): void {
    try {
      const breakpoints: Breakpoint[] = JSON.parse(json)
      
      // Clear existing breakpoints
      this.removeAllBreakpoints()
      
      // Add imported breakpoints
      breakpoints.forEach(bp => {
        this.breakpoints.set(bp.id, bp)
      })
      
      // Update decorations for all affected files
      const filePaths = new Set(breakpoints.map(bp => bp.filePath))
      filePaths.forEach(filePath => {
        this.updateDecorations(filePath)
      })
      
      this.notifyListeners()
    } catch (error) {
      console.error('Failed to import breakpoints:', error)
      throw new Error('Invalid breakpoint data')
    }
  }
}

export const breakpointService = new BreakpointService()
