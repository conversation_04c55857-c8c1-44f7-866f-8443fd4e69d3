-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 70
  aa = 30
  ad = 8
  aV = 60
  spacing = 70
  
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kena<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kö<PERSON>e radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
      
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  
  local vNarrowDiameter = 30
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
	
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("K_AciliV"..aV)
  G.setThickness(-ad)
  
  local point1 = {X-a-sunkenWidth, Y-ust*1.5-sunkenWidth, 0, 0}
  local bulge = G.bulge(point1, {X/2, Y-ust-sunkenWidth}, {a+sunkenWidth, Y-ust*1.5-sunkenWidth})
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
	point1[4] = bulge
	local point2 = {a+sunkenWidth, Y-ust*1.5-sunkenWidth, 0, 0}
	point3 = {a+sunkenWidth, ust*1.5+sunkenWidth}
	point4 = {X-a-sunkenWidth, ust*1.5+sunkenWidth}
	G.line(point1, point2, bulge)		-- make the arcs
	G.line(point3, point4, bulge)
	local radius = G.radius(point1, point2, bulge)
	local sunkenDepth = G.distance(point1, point2)
	local nLines = math.floor((X-2*a-2*sunkenWidth)/spacing)
	local modifiedSpacing = (X-2*a-2*sunkenWidth)/nLines
	for i=0, nLines, 1		-- loop for the vertical lines
	do
		local Lp1 = {a+sunkenWidth+i*modifiedSpacing, 0}		-- imaginary lines to intersect the above arc
		local Lp2 = {a+sunkenWidth+i*modifiedSpacing, Y}
		comment, pc1, pc2 = G.circleCircleIntersection(point1, radius, point2, radius)		-- find center of the arc
		if (comment=='tangent' or comment=='intersection') then
			comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		-- find line-arc intersection point
			if (comment2=='tangent' or comment2=='secant') then
        G.line({intersection1[1], intersection1[2]}, {intersection1[1], Y-intersection1[2]})
			end
		else
			G.error()
      return false
		end
	end
  
  return true
end

require "ADekoDebugMode"