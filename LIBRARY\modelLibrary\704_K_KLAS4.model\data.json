{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nVOyuk_AciliV_aV \t\t acili V bicagi \nK_Ballnose\t\t Kure uclu kanal bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar kapakda acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 5, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 150, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 4, "description": "number of lines on the corners", "parameterName": "how<PERSON><PERSON>"}, {"defaultValue": 100, "description": "<PERSON><PERSON>", "parameterName": "pah"}, {"defaultValue": 2, "description": "Vbit ustu kanal var mi? (Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 2, "description": "DIS kanal var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}