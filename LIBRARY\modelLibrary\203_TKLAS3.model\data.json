{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nCep_Acma\t\t Tarama duz Freze bicagi \nK_AciliV_aV \t\tAcili V bicagi \nH_Raduslu_Pah_DIS\tRaduslu pah bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-minimum kenar u<PERSON>", "parameterName": "xMin"}, {"defaultValue": 150, "description": "Y-minimum kenar u<PERSON>u", "parameterName": "yMin"}, {"defaultValue": 250, "description": "X icin kucuk kapak deseni limit degeri", "parameterName": "xLimit"}, {"defaultValue": 250, "description": "Y icin kucuk kapak deseni limit degeri", "parameterName": "yLimit"}, {"defaultValue": 40, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 20, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 135, "description": "Acili vbit acisi", "parameterName": "aV"}, {"defaultValue": 6, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 6, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeRtoolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}