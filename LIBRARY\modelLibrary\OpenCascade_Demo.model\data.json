{"modelNote": "OpenCascade.js Worker Demo\n\nThis model demonstrates the new OpenCascade.js worker-based 3D geometry generation system.\n\nFeatures:\n- Non-blocking 3D geometry creation\n- Real BRep operations using OpenCascade.js\n- Proper memory management in Web Worker\n- Triangulated mesh output for Three.js\n- Export capabilities (STEP/BREP)\n\nLayers:\n- K_Freze10mm: 10mm end mill groove\n- K_Ballnose6mm: 6mm ball nose pocket\n- K_AciliV90: 90° V-bit decorative groove\n- Drill5mm: 5mm drill holes\n- H_Freze20mm_SF: 20mm end mill mortises (bottom)\n- H_Freze8mm: 8mm end mill routing groove (bottom)", "modelParameters": [{"defaultValue": 600, "description": "Door width (mm)", "parameterName": "doorWidth"}, {"defaultValue": 800, "description": "Door height (mm)", "parameterName": "doorHeight"}, {"defaultValue": 18, "description": "Door thickness (mm)", "parameterName": "doorThickness"}, {"defaultValue": 3, "description": "Top groove depth (mm)", "parameterName": "<PERSON><PERSON><PERSON><PERSON>"}, {"defaultValue": 2, "description": "Pocket depth (mm)", "parameterName": "<PERSON><PERSON><PERSON><PERSON>"}, {"defaultValue": 8, "description": "Hole depth (mm)", "parameterName": "<PERSON><PERSON><PERSON><PERSON>"}, {"defaultValue": 3, "description": "Mortise depth (mm)", "parameterName": "mort<PERSON><PERSON><PERSON><PERSON>"}], "tags": ["opencascade", "worker", "demo", "3d", "geometry", "brep", "door", "cnc", "machining", "non-blocking", "web-worker", "triangulation", "export", "step", "memory-management"], "version": 1, "category": "Examples", "subcategory": "OpenCascade.js", "difficulty": "intermediate", "estimatedProcessingTime": "5-10 seconds", "features": ["Web Worker processing", "OpenCascade.js BRep operations", "Triangulated mesh generation", "Memory leak prevention", "Export capabilities"], "requirements": ["OpenCascade.js WASM files", "Web Worker support", "Three.js for rendering"]}