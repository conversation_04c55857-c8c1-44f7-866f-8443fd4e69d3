-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
 	G = ADekoLib
	minimum = 150
	limit = 350
	
  a = 60          -- Kenardan Vbite
  aa = 20		  -- Dar kapak için Kenardan mesafe
  ad = 6          -- Vbit derinliği (sunkenDepth)
  b = 0			--Vbit Ustu Kanal Kaydirma Mesafesi
  d = 0          -- A<PERSON>li V den iç kanala
  aV = 120        -- Vbit Açısı
  gaps = 50
  cT = 6          -- Açılı V üzerinde kanal bıçak çapı
  sbt = 0		 	-- göbek desen bıçağına ve kanala göre içteki kanallar için kaydırma mesafesi-yoksa gaps/2
    
  intGrooveExist  			= 2   -- iç kanal var mı? derinlik/ 0:yok
  extGrooveExist    	= 2   -- Açılı V üzerinde kanal var mı? derinlik/0:yok
  extEdgeRtoolExist   		= 0   -- D<PERSON><PERSON> kenar Pah işlemi var mı? derinlik/0:yok
    	
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
   local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
   --cR = 5		  		-- Radus Veren form bıcak düz uç dar çapı
  --local bd = 3    -- Acili V Ustu kanal derinliği
  --dd = 2          -- iç kanal derinliği
 	
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
  if d == 0 then
	d = 0.1
  end

  local edgeCornerRExist          = 0  -- Kapak köşe Radüsü var mı? derinlik/0:yok
  local D = edgeCornerRExist
  
  kontrolListe = {a,d,ad,aV} 
  for i, val in ipairs(kontrolListe) do
    if val <= 0 then
      print("Yanlıs Deger Girilmis")
      return true
    end
  end
    
	local B = math.tan(math.pi/8)

	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist >0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	
	local vWideDiameter = 60
	local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
	
	--burası ortadaki çizgiler
	local gobekGenislik = X-2*a-2*sunkenWidth-2*sbt-2*d
	
	local stepX = math.floor(gobekGenislik/gaps)
	
	if ((gobekGenislik/(gaps))-stepX)  >= 0.5 then
	--if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local aprxGaps = gobekGenislik/(stepX+1)
	
	if sbt == 0 then
		e = a+sunkenWidth+d+aprxGaps
	else
		e = a+sunkenWidth+sbt+d+aprxGaps/2
		stepX = stepX+1
	end
	--burası ortadaki çizgiler^
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
  
	if extGrooveExist > 0 then
		--G.setLayer("K_Ballnose") 
		G.setLayer("K_Freze"..cT.."mm")  -- 
		G.setThickness(-extGrooveExist)
		distance = a + cT/2 - b
		distance2 = ust + cT/2 - b
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	end
  
	G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	G.setThickness(0)
	local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	corner1[3] = 0
	corner2[3] = 0
  
	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
  
	if intGrooveExist > 0 then      ------İç kanal varsa
		-- local check = 2*a + 2*sunkenWidth +2*d
		-- if X < check or Y < check then
		-- print("Part dimension too small, check a + h + d value")
		-- return true
		-- end
		G.setLayer("K_Kanal")  -- iç vbit üstü kanal
		G.setThickness(-intGrooveExist)
		distance = a + 2*sunkenWidth + d
		distance2 = ust + 2*sunkenWidth + d
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	
	--burası ortadaki çizgi 1 ler
		for i=0,stepX-1 do	
			local x = e+i*aprxGaps
			local offset = ust + 2*sunkenWidth + d 
			point3 = {x,offset}
			--point3 = {x,0}
			point4 = {x,Y-offset}  
			--point4 = {x,Y}  
			G.line(point3,point4,0)
			i = i+1
		end 
	--burası ortadaki çizgi 1 ler
	end
    
  return true
end

------------------------------------------------

require "ADekoDebugMode"
