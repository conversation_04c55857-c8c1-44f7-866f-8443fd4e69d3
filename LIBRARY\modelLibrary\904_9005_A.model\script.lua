-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
	G = ADekoLib
    minimum = 150
  
	a= 25
	ad = 3
	cT = 5 --kanal bicak capi
	howMany = 3
	
	extEdgeVtoolExist       	= 6  -- <PERSON><PERSON><PERSON> kenar <PERSON> var mı? derinlik/0:yok
	edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kö<PERSON>e radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
	
	if (a*howMany > Y-a or a*howMany > X-a) then
		G.error()
		return true
	end
	
	if (howMany<2) then
		G.error()
		return true
	end
  	
  G.setLayer("K_Freze".. cT .."mm")
  G.setThickness(-ad)
	
	for i=1, howMany, 1
	do
		G.line({0, i*a, 0}, {i*a, 0, 0}, 0)
		G.line({X-i*a, Y, 0}, {X, Y-i*a, 0}, 0)
	end
	
	G.line({a, howMany*a, 0}, {howMany*a, a, 0}, 0)
	G.line({X-howMany*a, Y-a, 0}, {X-a, Y-howMany*a, 0}, 0)
	G.line({X-howMany*a, Y-a, 0}, {a, Y-a, 0}, 0)
	G.line({a, Y-a, 0}, {a, howMany*a,0}, 0)
	G.line({howMany*a, a, 0}, {X-a, a, 0}, 0)
	G.line({X-a, a, 0}, {X-a, Y-howMany*a, 0}, 0)
  
	return true
end

require "ADekoDebugMode"
