-- Coordinate Alignment Test
-- This script tests if the door schema coordinates align properly with the model origin

function modelMain()
    print("=== Coordinate Alignment Test ===")
    
    -- First, let's draw the face layout guides to trigger ADekoLib coordinate mode
    -- These are the exact coordinates from ADekoDebugMode.lua
    local offset = 20
    local materialThickness = 18
    local X = 500  -- Cabinet width
    local Y = 700  -- Cabinet height
    
    -- Draw face labels (this will trigger ADekoLib layout detection)
    text("Left", 0, 20 + offset + 90, offset - 20)
    text("Rear", 0, 20 + offset + 90, 2*offset + materialThickness - 20)
    text("Front", 0, 20 + 3*offset + X + materialThickness, 3*offset + 2*materialThickness + X/2)
    text("Right", 0, 20 + 2*offset + X + offset/2, 3*offset + 2*materialThickness + Y/2)
    text("Top", 0, 20 + offset + 90, 3*offset + 2*materialThickness + Y/2)
    text("Bottom", 0, 20 + 4*offset + X + 2*materialThickness, 3*offset + 2*materialThickness + Y/2)
    
    -- Now draw some geometry that should align with the face layout
    pnsz(3)
    pncl("red")
    pndn()
    
    -- Draw a rectangle on the "top" face area
    zero(20 + offset, 3*offset + 2*materialThickness)
    
    -- Draw the top face outline
    move(X)
    turn(90)
    move(Y)
    turn(90)
    move(X)
    turn(90)
    move(Y)
    turn(90)
    
    -- Draw a smaller rectangle inside to show the coordinate system is working
    pnup()
    posn(50, 50)
    pndn()
    pncl("blue")
    pnsz(2)
    
    move(100)
    turn(90)
    move(80)
    turn(90)
    move(100)
    turn(90)
    move(80)
    turn(90)
    
    -- Add a circle to mark the origin
    pnup()
    posn(0, 0)
    pndn()
    pncl("green")
    crcl(0, 0, 5)
    
    -- Add text to show coordinates
    pnup()
    posn(10, 10)
    text("Origin (0,0)", 0, 0, 0)
    
    print("Coordinate alignment test completed!")
end
