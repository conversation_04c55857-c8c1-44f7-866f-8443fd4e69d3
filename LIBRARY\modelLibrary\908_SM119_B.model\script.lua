-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  
  a = 30
  ad = 3
  b = 100
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 5
    
  windowDepthFront = 14
  windowDepthBack = 6
  
  extEdgeVtoolExist       	= 6  -- <PERSON><PERSON><PERSON> kenar <PERSON> i<PERSON>lem<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> radü<PERSON>ü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
	
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
 
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  G.setLayer("K_Ballnose")
  G.setThickness(-ad)
  local p = {}
  p[1]  = {a, a}
  p[2]  = {2*a, a}
  p[3]  = {2*a, Y-a}
  p[4]  = {a, Y-a}
  p[5]  = {a, Y-2*a}
  p[6]  = {X-a, Y-2*a}
  p[7]  = {X-a, Y-a}
  p[8]  = {X-2*a, Y-a}
  p[9]  = {X-2*a, a}
  p[10] = {X-a, a}
  p[11] = {X-a, 2*a}
  p[12] = {a, 2*a}
  p[13] = G.deepcopy(p[1])
  G.polylineimp(p)
  
  G.setLayer("K_Ballnose")
  G.setThickness(-ad)
  G.rectangle({b, b}, {X-b, Y-b})
  
  G.setLayer("H_Freze"..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.rectangle({b, b}, {X-b, Y-b})
  
  G.setFace("bottom")
  G.setThickness(-windowDepthBack)
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  local glassMargin = cW/2
  G.rectangle({b-glassMargin, b-glassMargin}, {X-b+glassMargin, Y-b+glassMargin})
  
  return true
end

require "ADekoDebugMode"