-- Check if AdekoLib is available

print("=== AdekoLib Availability Test ===")

-- Check if AdekoLib exists
if AdekoLib then
    print("✓ AdekoLib is available")
    print("✓ AdekoLib type: " .. type(AdekoLib))
    
    -- Check some basic functions
    if AdekoLib.setFace then
        print("✓ AdekoLib.setFace exists")
    else
        print("✗ AdekoLib.setFace missing")
    end
    
    if AdekoLib.makePartShape then
        print("✓ AdekoLib.makePartShape exists")
    else
        print("✗ AdekoLib.makePartShape missing")
    end
    
else
    print("✗ AdekoLib is NOT available")
    print("Available globals:")
    for k, v in pairs(_G) do
        if type(v) == "table" and k ~= "_G" then
            print("  " .. k .. " (table)")
        elseif type(v) == "function" and not string.match(k, "^_") then
            print("  " .. k .. " (function)")
        end
    end
end

-- Global variables
X = 200
Y = 150
materialThickness = 18

function modelMain()
    print("modelMain() started")
    
    if AdekoLib then
        print("✓ AdekoLib available in modelMain")
        G = AdekoLib
        print("✓ G assigned successfully")
        return true
    else
        print("✗ AdekoLib NOT available in modelMain")
        return false
    end
end

print("=== Loading AdekoDebugMode ===")
require "ADekoDebugMode"
