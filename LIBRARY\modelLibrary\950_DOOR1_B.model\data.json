{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV* \t\t*aV* Acili V bicagi \nK_Freze*cW*mm\t\t*cW*mm Freze Bicagi \nK_Freze*cT*mm\t\t*cT*mm Ic kanal freze Bicagi  \nH_Freze*cT*mm_Ic\t*cT*mm Cam yeri kesim bicagi \nH_Freze*cW*mm_Ic_SF\t*cW*mm cam yeri tarama bicagi \nK_AciliV-K_Ballnose-K_Freze \tDis kanal1 ve 2 bicagi  \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 0, "description": "Kenardan acili V kenara mesafe (mesafe girilirse sol,sag,ust,ara: a olur)", "parameterName": "a"}, {"defaultValue": 100, "description": "Alt kenardan ic kenara mesafe", "parameterName": "alt"}, {"defaultValue": 75, "description": "Ust kenardan ic kenara mesafe", "parameterName": "ust"}, {"defaultValue": 75, "description": "Sol kenardan ic kenara mesafe", "parameterName": "sol"}, {"defaultValue": 75, "description": "Sol kenardan ic kenara mesafe", "parameterName": "sag"}, {"defaultValue": 100, "description": "Ust bolum alt bolum arasi mesafe", "parameterName": "ara"}, {"defaultValue": 100, "description": "Alt bolum yuksekligi (KD varsa iptal olur)", "parameterName": "altH"}, {"defaultValue": 880, "description": "Alttan kol deligine olan mesafe", "parameterName": "KD"}, {"defaultValue": 10, "description": "IC kenardan DIS Kanal_1 e Mesafe", "parameterName": "b"}, {"defaultValue": 20, "description": "IC kenardan DIS Kanal_2 e mesafe", "parameterName": "c"}, {"defaultValue": 35, "description": "Gobekten IC Kanala Mesafe", "parameterName": "d"}, {"defaultValue": 30, "description": "Acili V Kenardan Gobege Mesafe", "parameterName": "h"}, {"defaultValue": 120, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "Tarama bicak vapi", "parameterName": "cW"}, {"defaultValue": 8, "description": "ic Kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 40, "description": "Ic kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 20, "description": "<PERSON><PERSON><PERSON> ile ic kenar arasi kaydirma miktari", "parameterName": "sbt"}, {"defaultValue": 0, "description": "----------------------DIS KANAL ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 1.5, "description": "DIS kanal1 islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 1, "description": "DIS kanal1 :Ballnose mu? Flat mi? V kanal mi?(B:0/F:1/V:2)", "parameterName": "extGrvBorForV"}, {"defaultValue": 0, "description": "DIS kanal2 islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGroove2Exist"}, {"defaultValue": 1, "description": "DIS kanal2 :Ballnose mu? Flat mi? V kanal mi?(B:0/F:1/V:2)", "parameterName": "extGrv2BorForV"}, {"defaultValue": 0, "description": "----------------------IC <PERSON><PERSON>AR ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 1.5, "description": "Ic kenar ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 6, "description": "Kanal islemi bicak capi", "parameterName": "dT"}, {"defaultValue": 0, "description": "Ic kenar ustu kanal kenar uzatma islemi var mi?(Var :1/ Yok:0)", "parameterName": "topGrvExtExist"}, {"defaultValue": 1, "description": "Ic kenar ustu kanal islemi Ballnose mu? Flat-Duz mu? (B:0/ F:1)", "parameterName": "topGrooveBorF"}, {"defaultValue": 5, "description": "Ic kenarl<PERSON>a pah var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intEdgeToolExist"}, {"defaultValue": 1, "description": "Ic kenarda pah:V mi?SivrikoseV mi?Raduslu mu?(V:0/SV:1/R:2)", "parameterName": "intEdgeVorSVorR"}, {"defaultValue": 0, "description": "Alt bolumde cam var mi?(Var:1/ Yok:0)", "parameterName": "bottomWindowExist"}, {"defaultValue": 0, "description": "----------------------ALT <PERSON><PERSON><PERSON> KANAL ISLEMLERI-------------------------", "parameterName": "-----"}, {"defaultValue": 1, "description": "Gobek var mi?(Var:1/ Yok:0)", "parameterName": "cabCoreExist"}, {"defaultValue": 2, "description": "Ic dikdortgen kanal islemi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "intRectGrvExist"}, {"defaultValue": 2, "description": "Ic yatay-dikey kanal islemi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "intGrvExist"}, {"defaultValue": 1, "description": "Ic kanallar yatay mi?-dikey mi? (Dikey-Vert:1/Yatay-<PERSON>r:0) ", "parameterName": "<PERSON><PERSON><PERSON>_<PERSON>"}, {"defaultValue": 3.5, "description": "Gobek desen bicagi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "shapeToolExist"}, {"defaultValue": 0, "description": "----------------------DIS KENAR ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeToolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi V mi? Raduslu mu? (V :0/ R:1)", "parameterName": "extEdgeVorR"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}