{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV* \t\t*aV* Acili V bicagi \nCep_Acma\t\tH den kucuk tarama bicagi \nK_Freze*cT*mm\t\t*cT*mm Freze Bicagi \nH_Freze*cT*mm_Ic\t\t*cT*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 40, "description": "Kenardan Acili V Kenara Mesafe", "parameterName": "a"}, {"defaultValue": 35, "description": "Gobekten IC Kanala Mesafe", "parameterName": "d"}, {"defaultValue": 5, "description": "Acili V Derinligi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili V Uc Acisi", "parameterName": "aV"}, {"defaultValue": 6, "description": "Kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 50, "description": "<PERSON><PERSON>", "parameterName": "yy"}, {"defaultValue": 100, "description": "<PERSON><PERSON><PERSON> yaya mesafe", "parameterName": "m"}, {"defaultValue": 1.5, "description": "Ic kenar ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 1, "description": "Ic kenar ustu kanal kenar uzatma islemi var mi?(Var :1/ Yok:0)", "parameterName": "topGrvExtExist"}, {"defaultValue": 1.5, "description": "Ic kanal islemi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "intGrvExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}