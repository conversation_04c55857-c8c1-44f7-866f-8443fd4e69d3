-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
	minimum = 150
	limit = 250
	
  a = 60          -- Kenardan Vbite
  aa = 30					-- Dar kapak için Kenardan mesafe
  ad = 5          -- Vbit Derinliği
  aV = 120        -- Vbit açısı
  howMany = 2
  pah = 100		
  
  extEdgeVtoolExist       	= 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
  topGrooveExist  			= 2   -- iç kanal var mı? derinlik/ 0:yok
  extGrooveExist			= 2		--dış köşe açılı kanallar var mı? derinlik/ 0:yok
  edgeCornerRExist          = 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit

  
  local kanaloffset = 0	--  bit üstü kanal merkez pozisyonu "-" değer kadar dışarıda mı? "+" değer kadar içeride mi?
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
  local D = edgeCornerRExist
  
  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  	function ChamferPoly(distance1, distance2, pah)        --- distance1 soldan mesafe, distance2 6numaranın yukarıdan mesafesi, ust-ust den yaya mesafe, m- 2 ile 3 numara arası mesafe
		local points = {
			{X-distance1, Y-distance2, 0, 0},  --1                	 	 	  		2---1-7  
			{distance1+pah, Y-distance2, 0, 0},       --2               		3--------
			{distance1, Y-distance2-pah, 0, 0},       --3                  		---------  
			{distance1, ust, 0, 0},         --4                 				---------
			{X-distance1-pah, ust, 0},             --5              			--------6  
			{X-distance1, ust+pah, 0, 0},      --6                				4-----5
			{X-distance1, Y-distance2, 0, 0} --7
			}  
			return points
	end
	
	local distance1 = a
	local distance2 = ust
	local vWideDiameter = 40
	
	local points = {
			{X-distance1, Y-distance2, 0, 0},  --1                	 	 	  		2---1-7  
			{distance1+pah, Y-distance2, 0, 0},       --2               		3--------
			{distance1, Y-distance2-pah, 0, 0},       --3                  		---------  
			{distance1, ust, 0, 0},         --4                 				---------
			{X-distance1-pah, ust, 0},             --5              			--------6  
			{X-distance1, ust+pah, 0, 0},      --6                				4-----5
			{X-distance1, Y-distance2, 0, 0} --7
			}  
		
	if (a*howMany > Y-a or a*howMany > X-a) then
		G.error()
		return true
	end
	
	if (howMany<2) then
		G.error()
		return true
	end
  
	G.setLayer("VOyuk_AciliV".. aV)  -- V shape deep
	G.setThickness(-ad)
	G.polylineimp(points)
	--G.sunkenFrameAny(points,6,ad,aV,vWideDiameter)

	
	if extEdgeVtoolExist > 0 then
		G.setLayer("K_AciliV_Pah")	
		G.setThickness(-extEdgeVtoolExist)
		G.rectangle({0,0},{X,Y})
	end
  
	
	if topGrooveExist > 0 then
		G.setLayer("K_Ballnose")
		G.setThickness(-topGrooveExist)
		G.polylineimp (G.offSet(points, -kanaloffset))

		if extGrooveExist > 0 then
			G.setThickness(-extGrooveExist)
			--G.rectangle({X-a-kanaloffset,ust+kanaloffset},{a+kanaloffset,Y-ust-kanaloffset})
			points1 = {{X-a-kanaloffset,ust+pah-kanaloffset},{X-a-kanaloffset,ust+kanaloffset},{X-a-pah,ust+kanaloffset}}
			points2 = {{a+kanaloffset,Y-ust-pah+kanaloffset},{a+kanaloffset,Y-ust-kanaloffset},{a+pah,Y-ust-kanaloffset}}
			G.polylineimp(points1)
			G.polylineimp(points2)
			
			local gaps = (pah+kanaloffset)/(howMany+1)
			
			for i=1, howMany, 1
			do
				G.line({X-a-kanaloffset, ust+i*gaps, 0}, {X-a-i*gaps, ust+kanaloffset, 0}, 0)
				G.line({a+i*gaps, Y-ust-kanaloffset, 0}, {a+kanaloffset, Y-ust-i*gaps, 0}, 0)
			end
			
		end
	end
	return true
end

require "ADekoDebugMode"
