-- ADekoCAM, Model Script - Soft Rectangle Floating Frame Test
-- Converted from C# azCAM macro: Soft Rectangle Floating Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Soft Rectangle Floating Frame parameters (from original C# macro)
  A = 50    -- X position (left offset)
  B = 50    -- Y position (bottom offset)
  frameX = 200  -- Frame width (X parameter from C#)
  frameY = 200  -- Frame height (Y parameter from C#)
  RA = 22   -- Corner radius A (bottom-left to bottom-right)
  RB = 22   -- Corner radius B (bottom-right to top-right)
  RC = 22   -- Corner radius C (top-right to top-left)
  RD = 22   -- Corner radius D (top-left to bottom-left)
  Z = 8     -- Thickness/depth
  
  -- Reference corner (from original parameters.json: reference = 3)
  -- 1=top-left, 2=top-center, 3=top-right, 4=middle-left, 5=center, 6=middle-right, 7=bottom-left, 8=bottom-center, 9=bottom-right
  referenceCorner = 1  -- top-right (original default)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + frameX + 10   -- minimum required width
  local minHeight = B + frameY + 10  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Soft Rectangle Floating Frame function (converted from C# macro)
  local function soft_rectangle_floating_frame(A, B, frameX, frameY, RA, RB, RC, RD, Z, refCorner)
    -- Use default values if parameters not provided
    A = A or 50
    B = B or 50
    frameX = frameX or 100
    frameY = frameY or 100
    RA = RA or 22
    RB = RB or 22
    RC = RC or 22
    RD = RD or 22
    Z = Z or -8
    refCorner = refCorner or 3
    
    print(string.format("Creating soft rectangle floating frame"))
    print(string.format("Position: A=%d, B=%d", A, B))
    print(string.format("Size: %dx%d mm", frameX, frameY))
    print(string.format("Corner radii: RA=%d, RB=%d, RC=%d, RD=%d", RA, RB, RC, RD))
    print(string.format("Reference corner: %d", refCorner))
    
    -- Calculate frame position based on reference corner
    local frameLeft, frameBottom
    
    if refCorner == 1 then      -- top-left
      frameLeft = A
      frameBottom = height - B - frameY
    elseif refCorner == 2 then  -- top-center
      frameLeft = (width - frameX) / 2 + A
      frameBottom = height - B - frameY
    elseif refCorner == 3 then  -- top-right
      frameLeft = width - A - frameX
      frameBottom = height - B - frameY
    elseif refCorner == 4 then  -- middle-left
      frameLeft = A
      frameBottom = (height - frameY) / 2 + B
    elseif refCorner == 5 then  -- center
      frameLeft = (width - frameX) / 2 + A
      frameBottom = (height - frameY) / 2 + B
    elseif refCorner == 6 then  -- middle-right
      frameLeft = width - A - frameX
      frameBottom = (height - frameY) / 2 + B
    elseif refCorner == 7 then  -- bottom-left
      frameLeft = A
      frameBottom = B
    elseif refCorner == 8 then  -- bottom-center
      frameLeft = (width - frameX) / 2 + A
      frameBottom = B
    elseif refCorner == 9 then  -- bottom-right
      frameLeft = width - A - frameX
      frameBottom = B
    else
      -- Default to top-right if invalid reference
      frameLeft = width - A - frameX
      frameBottom = height - B - frameY
    end
    
    -- Calculate frame corner points (following C# logic exactly)
    -- Basic rectangle corners before filleting
    local left = frameLeft
    local right = frameLeft + frameX
    local bottom = frameBottom
    local top = frameBottom + frameY
    
    -- Ensure radii don't exceed frame dimensions
    local maxRadius = math.min(frameX, frameY) / 2
    RA = math.min(RA, maxRadius)
    RB = math.min(RB, maxRadius)
    RC = math.min(RC, maxRadius)
    RD = math.min(RD, maxRadius)
    
    -- Calculate filleted corner points
    -- Each corner gets cut by the radius distance
    
    -- Bottom edge points (with RA and RB radii)
    local p1 = {left + RD, bottom}        -- Bottom line start (after RD radius)
    local p2 = {right - RA, bottom}       -- Bottom line end (before RA radius)
    
    -- Right edge points (with RA and RB radii)
    local p3 = {right, bottom + RA}       -- Right line start (after RA radius)
    local p4 = {right, top - RB}          -- Right line end (before RB radius)
    
    -- Top edge points (with RB and RC radii)
    local p5 = {right - RB, top}          -- Top line start (after RB radius)
    local p6 = {left + RC, top}           -- Top line end (before RC radius)
    
    -- Left edge points (with RC and RD radii)
    local p7 = {left, top - RC}           -- Left line start (after RC radius)
    local p8 = {left, bottom + RD}        -- Left line end (before RD radius)
    
    -- Set layer and thickness for frame
    G.setLayer("Default")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the soft rectangle using polyline with bulges for rounded corners
    -- Calculate bulge for 90-degree arc: bulge = tan(angle/4) = tan(90°/4) = tan(22.5°) ≈ 0.414
    local bulge = math.tan(math.pi / 8)  -- ≈ 0.414
    
    -- Create the frame as a continuous polyline with bulges at corners
    local framePoints = {
      {p1[1], p1[2], 0, 0},      -- Bottom line start
      {p2[1], p2[2], 0, RA > 0 and bulge or 0},  -- Bottom line end, bulge for RA corner
      {p3[1], p3[2], 0, 0},      -- Right line start
      {p4[1], p4[2], 0, RB > 0 and bulge or 0},  -- Right line end, bulge for RB corner
      {p5[1], p5[2], 0, 0},      -- Top line start
      {p6[1], p6[2], 0, RC > 0 and bulge or 0},  -- Top line end, bulge for RC corner
      {p7[1], p7[2], 0, 0},      -- Left line start
      {p8[1], p8[2], 0, RD > 0 and bulge or 0},  -- Left line end, bulge for RD corner
      {p1[1], p1[2], 0, 0}       -- Close the frame
    }
    
    -- Create the soft rectangle frame
    G.polyline(table.unpack(framePoints))
    
    print(string.format("Soft rectangle frame created:"))
    print(string.format("  Position: (%.1f, %.1f)", frameLeft, frameBottom))
    print(string.format("  Size: %.1f x %.1f mm", frameX, frameY))
    print(string.format("  Effective radii: RA=%.1f, RB=%.1f, RC=%.1f, RD=%.1f", RA, RB, RC, RD))
    print(string.format("  Frame corners:"))
    print(string.format("    Bottom: (%.1f,%.1f) to (%.1f,%.1f)", p1[1], p1[2], p2[1], p2[2]))
    print(string.format("    Right: (%.1f,%.1f) to (%.1f,%.1f)", p3[1], p3[2], p4[1], p4[2]))
    print(string.format("    Top: (%.1f,%.1f) to (%.1f,%.1f)", p5[1], p5[2], p6[1], p6[2]))
    print(string.format("    Left: (%.1f,%.1f) to (%.1f,%.1f)", p7[1], p7[2], p8[1], p8[2]))
    
    return true
  end
  
  -- Call the soft rectangle floating frame function
  local success = soft_rectangle_floating_frame(A, B, frameX, frameY, RA, RB, RC, RD, -Z, referenceCorner)
  
  if success then
    local refNames = {
      [1] = "top-left", [2] = "top-center", [3] = "top-right",
      [4] = "middle-left", [5] = "center", [6] = "middle-right", 
      [7] = "bottom-left", [8] = "bottom-center", [9] = "bottom-right"
    }
    
    print(string.format("Soft rectangle floating frame created with parameters:"))
    print(string.format("  Position: A=%d, B=%d mm", A, B))
    print(string.format("  Size: %dx%d mm", frameX, frameY))
    print(string.format("  Corner radii: RA=%d, RB=%d, RC=%d, RD=%d mm", RA, RB, RC, RD))
    print(string.format("  Reference corner: %d (%s)", referenceCorner, refNames[referenceCorner] or "unknown"))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a floating rectangle with independent corner radii")
    print("  - Each corner can have different radius (RA, RB, RC, RD)")
    print("  - Frame has fixed dimensions regardless of panel size")
    print("  - Position is relative to the specified reference corner")
    print("")
    print("Corner mapping:")
    print("  RA: Bottom-left to bottom-right transition")
    print("  RB: Bottom-right to top-right transition")
    print("  RC: Top-right to top-left transition")
    print("  RD: Top-left to bottom-left transition")
    print("")
    print("Applications:")
    print("  - Custom rounded rectangles")
    print("  - Asymmetric corner designs")
    print("  - Decorative elements with varied corner styles")
    print("  - Precise corner radius control")
  end
  
  return true
end

require "ADekoDebugMode"
