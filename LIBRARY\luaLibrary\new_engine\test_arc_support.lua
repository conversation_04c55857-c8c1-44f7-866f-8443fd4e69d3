#!/usr/bin/env lua

--[[
Test script for Arc and Elliptical Arc support in ADekoLib
This script demonstrates the new true arc functionality that replaces
the simplified circle approach used previously.
--]]

-- Load the engine and ADekoLib
local engine = require('makerjs_engine')
local ADekoLib = require('ADekoLib')

print("=== Arc Support Test ===")
print("Testing new arc and elliptical arc functionality...")

-- Initialize ADekoLib with the engine
ADekoLib.engine = engine

-- Create a test model
engine.model_def("arc_test", function()
    
    -- Test 1: Basic Arc Support
    print("  Testing basic arc creation...")
    engine.layer("arcs")
    
    -- Create a simple arc
    ADekoLib.arc({100, 100}, 50, 0, 90, false, "quarter_arc")
    
    -- Create an arc with different angles
    ADekoLib.arc({200, 100}, 30, 45, 180, false, "half_arc")
    
    -- Create a clockwise arc
    ADekoLib.arc({300, 100}, 40, 0, 270, true, "clockwise_arc")
    
    -- Test 2: Elliptical Arc Support
    print("  Testing elliptical arc creation...")
    engine.layer("elliptical_arcs")
    
    -- Create a simple elliptical arc
    ADekoLib.ellipticalArc({100, 250}, 60, 30, 0, 180, 0, false, "ellipse_half")
    
    -- Create a rotated elliptical arc
    ADekoLib.ellipticalArc({250, 250}, 50, 25, 45, 225, 30, false, "rotated_ellipse")
    
    -- Create a full elliptical arc (almost full circle)
    ADekoLib.ellipticalArc({400, 250}, 40, 40, 0, 350, 0, false, "near_circle")
    
    -- Test 3: Enhanced circular arc function
    print("  Testing enhanced circular arc function...")
    engine.layer("enhanced_arcs")
    
    -- Use true arc
    ADekoLib.circularArcTrue({100, 400}, 80, 30, 150, true, "true_arc")
    
    -- Use polygon approximation for comparison
    local poly_points = ADekoLib.circularArcTrue({250, 400}, 80, 30, 150, false)
    if poly_points then
        engine.polyline("poly_arc", poly_points)
    end
    
    -- Test 4: Enhanced elliptical arc function
    print("  Testing enhanced elliptical arc function...")
    engine.layer("enhanced_ellipses")
    
    -- Use true elliptical arc
    ADekoLib.ellipticArcTrue({100, 550}, 100, 50, 0, 120, true, 15, "true_ellipse")
    
    -- Use polygon approximation for comparison
    local ellipse_points = ADekoLib.ellipticArcTrue({300, 550}, 100, 50, 0, 120, false, 15)
    if ellipse_points then
        engine.polyline("poly_ellipse", ellipse_points)
    end
    
    -- Test 5: Polyline with arcs (using the updated polylineimp function)
    print("  Testing polyline with arc segments...")
    engine.layer("polyline_arcs")
    
    -- Create a polyline with bulge values (arcs)
    local polyline_with_arcs = {
        {50, 700, 0, 0},      -- Start point, no bulge
        {150, 700, 0, 0.5},   -- Point with positive bulge (arc)
        {250, 700, 0, -0.3},  -- Point with negative bulge (arc)
        {350, 700, 0, 0},     -- End point, no bulge
    }
    
    ADekoLib.polylineimp(polyline_with_arcs)
    
    -- Test 6: Line function with bulge (using the updated line function)
    print("  Testing line function with bulge...")
    engine.layer("line_arcs")
    
    -- Create lines with bulge values
    ADekoLib.line({50, 850}, {150, 850}, 0.7)   -- Positive bulge
    ADekoLib.line({200, 850}, {300, 850}, -0.4) -- Negative bulge
    ADekoLib.line({350, 850}, {450, 850}, 0)    -- No bulge (straight line)
    
end)

-- Export the model
local json_output = engine.export_model("arc_test")

-- Save to file
local file = io.open("arc_test_output.json", "w")
if file then
    file:write(json_output)
    file:close()
    print("✓ Arc test model exported to arc_test_output.json")
else
    print("✗ Failed to save output file")
end

-- Print a summary
print("\n=== Arc Support Summary ===")
print("✓ ADekoLib.arc - Creates true arcs instead of circles")
print("✓ ADekoLib.ellipticalArc - Creates true elliptical arcs")
print("✓ ADekoLib.circularArcTrue - Enhanced circular arc with true arc option")
print("✓ ADekoLib.ellipticArcTrue - Enhanced elliptical arc with true arc option")
print("✓ ADekoLib.polylineimp - Updated to use true arcs for bulge segments")
print("✓ ADekoLib.line - Updated to use true arcs for bulge values")
print("\nAll arc simplifications have been replaced with proper arc support!")
print("The output JSON now contains proper Maker.js arc and elliptical arc definitions.")
