-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  
  a = 70
  b = 45
  cThickDiameter = 20
  cThinDiameter = 5
  startingDepth = 0
  finalDepth = 5
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  if X < 200 or Y < 200 then
    print("Part dimension too small")
    return true
  end
  
  local point1, point2, point3, point4, point5 = {}, {}, {}, {}, {}
  local step = cThickDiameter/10
  local step2 = cThinDiameter/10
  local howMany = math.floor((Y-2*a)/b)
  local mB = (Y-2*a)/howMany
  local correctedDepth = ((finalDepth-startingDepth)*(cThickDiameter-cThinDiameter))/(mB-cThickDiameter)+finalDepth
  for i=0, howMany-1, 1
  do
    point1 = {a, a+i*mB}
    point2 = {X-a, a+(i+1)*mB}
    
    G.setLayer("K_Freze20mm")		-- Rough pass
    G.setThickness(-finalDepth)
    G.inclinedPocketPrim(point1, point2, startingDepth, finalDepth, step, cThickDiameter, "h")
    
    if (cThinDiameter~=0) then
      point3 = G.ptSubtract(point2, {0, mB-cThickDiameter})
      
      G.setLayer("K_Freze5mm")		-- Fine pass
      G.setThickness(-finalDepth-correctedDepth)
      G.inclinedPocketPrim(point1, point3, finalDepth, correctedDepth, step2, cThinDiameter, "h")
      
      G.setThickness(-startingDepth)		-- Fine side passes
      point4 = G.ptAdd(point1, {3*cThickDiameter/4, mB})
      G.inclinedPocketPrim(point1, point4, -correctedDepth, startingDepth, step2, cThinDiameter, "h")
      point5 = G.ptSubtract(point2, {3*cThickDiameter/4, mB})
      G.inclinedPocketPrim(point5, point2, -correctedDepth, startingDepth, step2, cThinDiameter, "h")
    end
  end
  
  return true
end

require "ADekoDebugMode"