<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="32"
   height="32"
   viewBox="0 0 32.000002 32"
   version="1.1"
   id="svg31"
   sodipodi:docname="logo.svg"
   inkscape:version="1.4 (86a8ad7, 2024-10-11)"
   inkscape:export-filename="G:\Git\luaeditor\src-tauri\icons\StoreLogo.png"
   inkscape:export-xdpi="150"
   inkscape:export-ydpi="150"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs35">
    <linearGradient
       id="linearGradient1"
       inkscape:collect="always">
      <stop
         style="stop-color:#0000ff;stop-opacity:1;"
         offset="0"
         id="stop1" />
      <stop
         style="stop-color:#0000ff;stop-opacity:0;"
         offset="1"
         id="stop2" />
    </linearGradient>
    <rect
       x="40.850674"
       y="3.2129743"
       width="90.789474"
       height="23.133415"
       id="rect1" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient1"
       id="linearGradient2"
       x1="-15.128658"
       y1="34.393921"
       x2="20.408908"
       y2="34.393921"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.2765958,0,0,1.2,21.339792,-4.0226606)" />
  </defs>
  <sodipodi:namedview
     id="namedview33"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     showgrid="false"
     inkscape:zoom="3.8513752"
     inkscape:cx="64.132936"
     inkscape:cy="10.256077"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="surface1"
     inkscape:export-bgcolor="#ffffff00" />
  <g
     id="surface1"
     transform="translate(-8.612018,-21.151896)">
    <ellipse
       style="opacity:0.973868;fill:url(#linearGradient2);stroke:#797979;stroke-width:0;stroke-miterlimit:3;stroke-dasharray:none"
       id="path1"
       cx="24.710167"
       cy="37.250042"
       rx="15.57885"
       ry="15.578852" />
    <path
       style="fill:#f82e00;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.167502"
       d="m 23.511386,26.344846 c 0.12671,0.04593 0.25774,0.07099 0.36417,0.142668 0.18577,0.12527 0.37278,0.259587 0.53088,0.424525 2.16473,2.256245 4.32517,4.518058 6.48683,6.778478 1.30967,1.369615 2.61934,2.739926 3.92963,4.108844 0.28358,0.296471 0.5727,0.584591 0.8526,0.885239 0.10335,0.110654 0.19377,0.240099 0.27621,0.370937 0.14149,0.224093 0.19254,0.478111 0.13902,0.748832 -0.0375,0.185818 -0.0892,0.368154 -0.14333,0.551187 0.0123,-0.199734 -0.0203,-0.383464 -0.11011,-0.55536 -0.13349,-0.255412 -0.30143,-0.479506 -0.4909,-0.680634 -0.63115,-0.666711 -1.26538,-1.329248 -1.89898,-1.991785 -1.59327,-1.666086 -3.18653,-3.331476 -4.78039,-4.997563 -0.95719,-1.000764 -1.91437,-2.002225 -2.87341,-3.000902 -0.19007,-0.197648 -0.39554,-0.370938 -0.65021,-0.452363 -0.25591,-0.08212 -0.48844,-0.02158 -0.70375,0.145451 -0.203,0.157284 -0.36478,0.361195 -0.49889,0.591552 -2.124134,3.649522 -4.248888,7.298347 -6.36626,10.952739 -0.226992,0.391818 -0.420767,0.809383 -0.60839,1.226949 -0.161171,0.358408 -0.220226,0.750921 -0.212845,1.154566 6.14e-4,0.02714 0,0.05498 0,0.09256 -0.136564,-0.02088 -0.267593,-0.04037 -0.398621,-0.05985 -0.501969,-0.07586 -1.003937,-0.151016 -1.505905,-0.226874 -0.445989,-0.06681 -0.891978,-0.130836 -1.337352,-0.20252 -0.144563,-0.02366 -0.287279,-0.07238 -0.385088,-0.208782 -0.126108,-0.176769 -0.123032,-0.384856 -0.04737,-0.571368 0.08981,-0.222007 0.203617,-0.434964 0.330339,-0.632613 1.276452,-1.982739 2.557209,-3.961303 3.837966,-5.941259 1.675074,-2.58821 3.350149,-5.176419 5.026456,-7.764629 0.15625,-0.242188 0.32542,-0.471849 0.54257,-0.649313 0.14272,-0.116224 0.29773,-0.197648 0.47552,-0.233838 0.0738,-0.0048 0.14333,-0.0048 0.21961,-0.0048 z m 0,0"
       id="path4" />
    <path
       style="fill:#f82e00;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.167502"
       d="m 32.126036,37.683138 c 0.95165,1.00842 1.90022,2.01197 2.84634,3.018302 0.19746,0.210175 0.36663,0.445404 0.47429,0.732132 0.083,0.223397 0.0689,0.446098 0.0203,0.673673 -0.15871,0.744659 -0.33156,1.484444 -0.56594,2.202657 -0.11996,0.368154 -0.31558,0.677152 -0.64284,0.851136 -0.24853,0.132927 -0.51489,0.159371 -0.78248,0.132927 -0.39371,-0.03897 -0.78617,-0.09187 -1.17864,-0.144756 -0.45891,-0.06125 -0.9172,-0.128054 -1.37549,-0.192776 -0.48844,-0.06959 -0.97626,-0.138494 -1.46347,-0.208784 -0.4466,-0.06403 -0.89259,-0.12875 -1.33858,-0.194167 -0.47243,-0.06959 -0.94549,-0.140581 -1.41793,-0.210871 -0.4706,-0.06959 -0.94058,-0.139884 -1.41117,-0.209479 -0.50935,-0.07586 -1.0187,-0.152411 -1.52806,-0.228269 -0.0615,-0.0091 -0.12241,-0.01809 -0.19623,-0.02923 -0.006,-0.206694 -0.0184,-0.412694 -0.0197,-0.618692 -0.002,-0.349363 0.0972,-0.677152 0.17101,-1.009813 0.14457,-0.659752 0.3894,-1.275662 0.63546,-1.891572 0.73819,-1.847029 1.4733,-3.696149 2.20841,-5.544571 0.15625,-0.391816 0.35495,-0.74953 0.6453,-1.032083 0.36541,-0.356322 0.7628,-0.377201 1.14973,-0.04802 0.19869,0.16981 0.38201,0.363978 0.5641,0.556058 1.06853,1.129515 2.1346,2.261117 3.20559,3.396198 z m 0,0"
       id="path22" />
    <path
       style="fill:#f82e00;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.167502"
       d="m 22.600326,42.490013 c -0.11196,0.400167 -0.15932,0.801726 -0.10211,1.224162 -1.617864,-0.245668 -3.22773,-0.489944 -4.840058,-0.734915 -0.03629,-0.441923 0.01846,-0.862272 0.169168,-1.267311 0.163632,-0.439835 0.387549,-0.84209 0.606545,-1.246433 1.909445,-3.521468 3.819505,-7.041545 5.731425,-10.560925 0.13041,-0.2401 0.29958,-0.445403 0.50873,-0.60199 0.31989,-0.239405 0.64284,-0.209478 0.95226,0.01809 0.16241,0.119702 0.31496,0.260979 0.45829,0.410607 2.75652,2.879809 5.51243,5.76101 8.26587,8.644995 0.34818,0.364673 0.69206,0.736307 1.02424,1.119772 0.20423,0.235923 0.37033,0.509429 0.45337,0.835131 0.037,0.144756 0.0443,0.292294 0.007,0.438444 -0.0689,0.270024 -0.14087,0.539354 -0.21531,0.808684 -0.0246,-0.558145 -0.30757,-0.958313 -0.6416,-1.313939 -0.84154,-0.895679 -1.69292,-1.778829 -2.54061,-2.666156 -1.34781,-1.409978 -2.695,-2.819957 -4.04281,-4.229241 -0.15625,-0.163546 -0.3168,-0.320133 -0.5075,-0.432876 -0.39678,-0.234532 -0.78125,-0.208783 -1.14788,0.07586 -0.36664,0.284641 -0.6453,0.665321 -0.83047,1.117683 -0.54749,1.337601 -1.08513,2.680771 -1.62032,4.024635 -0.53027,1.331336 -1.05376,2.66546 -1.57911,3.999581 -0.0418,0.107175 -0.072,0.219918 -0.1095,0.33614 z m 0,0"
       id="path24" />
  </g>
</svg>
