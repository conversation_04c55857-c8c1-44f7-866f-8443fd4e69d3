-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script - Rectangular Floating Frame Test
-- Converted from C# azCAM macro: Rectangular Floating Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Rectangular Floating Frame parameters (from original C# macro)
  A = 5     -- X position (left offset)
  B = 5     -- Y position (bottom offset)
  frameX = 20   -- Frame width (X parameter from C#)
  frameY = 20   -- Frame height (Y parameter from C#)
  Z = 10    -- Thickness/depth
  
  -- Reference corner (from original parameters.json: reference = 9)
  -- 1=top-left, 2=top-center, 3=top-right, 4=middle-left, 5=center, 6=middle-right, 7=bottom-left, 8=bottom-center, 9=bottom-right
  referenceCorner = 9  -- bottom-right (original default)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + frameX + 10   -- minimum required width
  local minHeight = B + frameY + 10  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Rectangular Floating Frame function (converted from C# macro)
  local function rectangular_floating_frame(A, B, frameX, frameY, Z, refCorner)
    -- Use default values if parameters not provided
    A = A or 5
    B = B or 5
    frameX = frameX or 20
    frameY = frameY or 20
    Z = Z or -10
    refCorner = refCorner or 9
    
    print(string.format("Creating rectangular floating frame"))
    print(string.format("Position: A=%d, B=%d", A, B))
    print(string.format("Size: %dx%d mm", frameX, frameY))
    print(string.format("Reference corner: %d", refCorner))
    
    -- Calculate frame position based on reference corner
    local frameLeft, frameBottom
    
    if refCorner == 1 then      -- top-left
      frameLeft = A
      frameBottom = height - B - frameY
    elseif refCorner == 2 then  -- top-center
      frameLeft = (width - frameX) / 2 + A
      frameBottom = height - B - frameY
    elseif refCorner == 3 then  -- top-right
      frameLeft = width - A - frameX
      frameBottom = height - B - frameY
    elseif refCorner == 4 then  -- middle-left
      frameLeft = A
      frameBottom = (height - frameY) / 2 + B
    elseif refCorner == 5 then  -- center
      frameLeft = (width - frameX) / 2 + A
      frameBottom = (height - frameY) / 2 + B
    elseif refCorner == 6 then  -- middle-right
      frameLeft = width - A - frameX
      frameBottom = (height - frameY) / 2 + B
    elseif refCorner == 7 then  -- bottom-left
      frameLeft = A
      frameBottom = B
    elseif refCorner == 8 then  -- bottom-center
      frameLeft = (width - frameX) / 2 + A
      frameBottom = B
    elseif refCorner == 9 then  -- bottom-right
      frameLeft = width - A - frameX
      frameBottom = B
    else
      -- Default to bottom-right if invalid reference
      frameLeft = width - A - frameX
      frameBottom = B
    end
    
    -- Calculate frame corner points (following C# logic exactly)
    -- p1 = (A, B), p2 = (A+X, B), p3 = (A+X, B+Y), p4 = (A, B+Y)
    local p1 = {frameLeft, frameBottom}                    -- Bottom-left
    local p2 = {frameLeft + frameX, frameBottom}           -- Bottom-right
    local p3 = {frameLeft + frameX, frameBottom + frameY}  -- Top-right
    local p4 = {frameLeft, frameBottom + frameY}           -- Top-left
    
    -- Validate that frame fits within panel bounds
    if frameLeft < 0 or frameBottom < 0 or 
       frameLeft + frameX > width or frameBottom + frameY > height then
      print("Warning: Frame extends beyond panel boundaries")
      print(string.format("Frame bounds: (%.1f,%.1f) to (%.1f,%.1f)", 
            frameLeft, frameBottom, frameLeft + frameX, frameBottom + frameY))
      print(string.format("Panel bounds: (0,0) to (%.1f,%.1f)", width, height))
    end
    
    -- Set layer and thickness for frame
    G.setLayer("G_V90")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the floating rectangular frame
    G.polyline(p1, p2, p3, p4, p1)  -- Close the rectangle
    
    print(string.format("Floating frame created:"))
    print(string.format("  Position: (%.1f, %.1f)", frameLeft, frameBottom))
    print(string.format("  Size: %.1f x %.1f mm", frameX, frameY))
    print(string.format("  Frame corners:"))
    print(string.format("    Bottom-left: (%.1f, %.1f)", p1[1], p1[2]))
    print(string.format("    Bottom-right: (%.1f, %.1f)", p2[1], p2[2]))
    print(string.format("    Top-right: (%.1f, %.1f)", p3[1], p3[2]))
    print(string.format("    Top-left: (%.1f, %.1f)", p4[1], p4[2]))
    
    return true
  end
  
  -- Call the rectangular floating frame function
  local success = rectangular_floating_frame(A, B, frameX, frameY, -Z, referenceCorner)
  
  if success then
    local refNames = {
      [1] = "top-left", [2] = "top-center", [3] = "top-right",
      [4] = "middle-left", [5] = "center", [6] = "middle-right", 
      [7] = "bottom-left", [8] = "bottom-center", [9] = "bottom-right"
    }
    
    print(string.format("Rectangular floating frame created with parameters:"))
    print(string.format("  Position: A=%d, B=%d mm", A, B))
    print(string.format("  Size: %dx%d mm", frameX, frameY))
    print(string.format("  Reference corner: %d (%s)", referenceCorner, refNames[referenceCorner] or "unknown"))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a floating rectangular frame at specified position")
    print("  - Frame has fixed dimensions regardless of panel size")
    print("  - Position is relative to the specified reference corner")
    print("  - A, B parameters control offset from reference corner")
    print("")
    print("Applications:")
    print("  - Fixed-size decorative elements")
    print("  - Precisely positioned openings")
    print("  - Independent frame elements")
    print("  - Custom positioned rectangular features")
  end
  
  return true
end

require "ADekoDebugMode"
