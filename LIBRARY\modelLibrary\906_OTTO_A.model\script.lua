-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  a = 30
  b = 30
  scale = 70
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  cell(a,b,scale)
  
  return true
end

function cell(a,b, scale)
    local howManyX = math.floor((X-2*a) / scale) 
    local howManyY = math.floor((Y-2*b) / scale)
    local originalScaleX = (X-2*a) / howManyX
    local originalScaleY = (Y-2*b) / howManyY
    
    for i=0,howManyY-1 do
    <PERSON><PERSON>setLayer("K_AciliV60")                         
    G.setThickness(-3)
    newOriginY = b + (i)*originalScaleY
    for j=0,howManyX-1 do
      <PERSON><PERSON>set<PERSON>ay<PERSON>("K_AciliV60")
      G.setThickness(-3)
      local newOriginX = a + j*originalScaleX
      
      pts = {
        {0.207107 , 0},
        {0.09467 , 0.271447},
        {0.375 , 0.551777},
        {0.375 , 0.948223},
        {0.5 , 1},
        {0.792893 , 0},
        {0.90533 , 0.271447},
        {0.625 , 0.551777},
        {0.625 , 0.948223},
        {0.5 , 1},
        {0.5 , 0},
        {0.375 , 0.051777},
        {0.375 , 0.448223},
        {0.09467 , 0.728553},
        {0.207107 , 1},
        {0.5 , 0},
        {0.625 , 0.051777},
        {0.625 , 0.448223},
        {0.90533 , 0.728553},
        {0.792893 , 1},
        {0 , 0.792893},
        {0.271447 , 0.90533},
        {0.551777 , 0.625},
        {0.948223 , 0.625},
        {1 , 0.5},
        {0 , 0.207107},
        {0.271447 , 0.09467},
        {0.551777 , 0.375},
        {0.948223 , 0.375},
        {1 , 0.5},
        {-0 , 0.5},
        {0.051777 , 0.625},
        {0.448223 , 0.625},
        {0.728553 , 0.90533},
        {1 , 0.792893},
        {-0 , 0.5},
        {0.051777 , 0.375},
        {0.448223 , 0.375},
        {0.728553 , 0.09467},
        {1 , 0.207107}
      }
      for i = 1 , 40 do
        pts[i][1] = newOriginX + pts[i][1] * originalScaleX
        pts[i][2] = newOriginY + pts[i][2] * originalScaleY
      end
      
      G.polyline(pts[1], pts[2], pts[3], pts[4], pts[5])
      G.polyline(pts[6], pts[7], pts[8], pts[9], pts[10])
      G.polyline(pts[11], pts[12], pts[13], pts[14], pts[15])
      G.polyline(pts[16], pts[17], pts[18], pts[19], pts[20])
      G.polyline(pts[21], pts[22], pts[23], pts[24], pts[25])
      G.polyline(pts[26], pts[27], pts[28], pts[29], pts[30])
      G.polyline(pts[31], pts[32], pts[33], pts[34], pts[35])
      G.polyline(pts[36], pts[37], pts[38], pts[39], pts[40])
      
    end
  end
    
    local frameX = a
    local frameY = b
    G.rectangle({frameX,frameY},{X-frameX,Y-frameY})
end

------------------------------------------------

require "ADekoDebugMode"
