-- Diagnostic script to test arc functionality
-- This will help identify why circles are appearing instead of arcs

-- Set up the engine properly (this is normally done by ADekoDebugMode.lua)
local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
  G = ADekoLib
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end

  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  print("=== Arc Diagnosis Test ===")
  
  -- Test 1: Simple arc with known parameters
  print("Test 1: Simple arc with direct arc() function")
  G.setLayer("TEST_direct_arc")
  G.setThickness(-5)
  
  local center = {100, 100}
  local radius = 50
  local start_angle = 0
  local end_angle = 90
  local clockwise = false
  
  print("Creating arc: center=", center[1], center[2], "radius=", radius, "start=", start_angle, "end=", end_angle, "clockwise=", clockwise)
  G.arc(center, radius, start_angle, end_angle, clockwise, "test_arc_1")
  
  -- Test 2: Arc using line function with bulge
  print("Test 2: Arc using line() function with bulge")
  <PERSON><PERSON>setLayer("TEST_line_arc")
  G.setThickness(-5)
  
  local p1 = {50, 200}
  local p2 = {150, 200}
  local bulge_val = 0.5
  
  print("Creating line arc: p1=", p1[1], p1[2], "p2=", p2[1], p2[2], "bulge=", bulge_val)
  G.line(p1, p2, bulge_val)
  
  -- Test 3: Calculate and verify arc parameters manually
  print("Test 3: Manual arc parameter calculation")
  G.setLayer("TEST_manual_calc")
  G.setThickness(-5)
  
  local test_p1 = {50, 300}
  local test_p2 = {150, 300}
  local test_bulge = 0.3
  
  -- Calculate radius and center manually
  local calc_radius = G.radius(test_p1, test_p2, test_bulge)
  local comment, center1, center2 = G.circleCircleIntersection(test_p1, calc_radius, test_p2, calc_radius)
  
  local arc_center = center1
  if test_bulge < 0 then
    arc_center = center2
  end
  
  print("Calculated radius:", calc_radius)
  print("Arc center:", arc_center[1], arc_center[2])
  
  -- Calculate angles
  local calc_start_angle = G.angle(arc_center, test_p1)
  local calc_end_angle = G.angle(arc_center, test_p2)
  local calc_clockwise = test_bulge < 0
  
  print("Start angle:", calc_start_angle)
  print("End angle:", calc_end_angle)
  print("Clockwise:", calc_clockwise)
  
  -- Create the arc using calculated parameters
  G.arc(arc_center, calc_radius, calc_start_angle, calc_end_angle, calc_clockwise, "manual_arc")
  
  -- Test 4: Polyline with single arc segment
  print("Test 4: Polyline with single arc segment")
  G.setLayer("TEST_polyline_single_arc")
  G.setThickness(-5)
  
  local poly_p1 = {50, 400, 0, test_bulge}  -- bulge from this point to next
  local poly_p2 = {150, 400, 0, 0}          -- no bulge from this point
  
  print("Polyline arc: p1=", poly_p1[1], poly_p1[2], "bulge=", poly_p1[4])
  print("              p2=", poly_p2[1], poly_p2[2], "bulge=", poly_p2[4])
  
  G.polyline(poly_p1, poly_p2)
  
  -- Test 5: Check if the issue is with angle normalization
  print("Test 5: Angle normalization test")
  G.setLayer("TEST_angle_norm")
  G.setThickness(-5)
  
  -- Test different angle ranges
  local norm_center = {300, 100}
  local norm_radius = 30
  
  -- Arc from 0 to 90 degrees
  G.arc(norm_center, norm_radius, 0, 90, false, "arc_0_90")
  
  -- Arc from 45 to 135 degrees  
  local norm_center2 = {300, 200}
  G.arc(norm_center2, norm_radius, 45, 135, false, "arc_45_135")
  
  -- Arc from 270 to 360 degrees
  local norm_center3 = {300, 300}
  G.arc(norm_center3, norm_radius, 270, 360, false, "arc_270_360")
  
  -- Test 6: Very small arc to see if it's a threshold issue
  print("Test 6: Small arc test")
  G.setLayer("TEST_small_arc")
  G.setThickness(-5)
  
  local small_center = {400, 100}
  local small_radius = 40
  G.arc(small_center, small_radius, 0, 30, false, "small_arc_30deg")
  
  -- Test 7: Large arc to see if it's a size issue
  print("Test 7: Large arc test")
  G.setLayer("TEST_large_arc")
  G.setThickness(-5)
  
  local large_center = {500, 200}
  local large_radius = 80
  G.arc(large_center, large_radius, 0, 120, false, "large_arc_120deg")
  
  print("=== Arc Diagnosis Complete ===")
  
  return true
end

require "ADekoDebugMode"
