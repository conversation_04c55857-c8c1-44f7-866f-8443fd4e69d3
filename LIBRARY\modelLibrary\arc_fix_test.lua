-- Arc Fix Test Script
-- This script creates a simple arc to test the angle fix

print("=== Arc Fix Test ===")

-- Load the new engine
local makerjs_engine = require("LIBRARY.luaLibrary.new_engine.makerjs_engine")

-- Create a test model
makerjs_engine.model_def("arc_fix_test", function()
    
    -- Create a simple quarter arc from 0° to 90°
    -- This should appear in the first quadrant (top-right from center)
    makerjs_engine.layer("test_arc")
    makerjs_engine.arc("quarter_arc", 200, 200, 100, 0, 90, false)
    
    -- Add a reference circle to see the full circle
    makerjs_engine.layer("reference")
    makerjs_engine.circle("ref_circle", 200, 200, 100)
    
    -- Add some reference lines to show the coordinate system
    makerjs_engine.layer("axes")
    makerjs_engine.line("x_axis", 100, 200, 300, 200)  -- Horizontal line
    makerjs_engine.line("y_axis", 200, 100, 200, 300)  -- Vertical line
    
end)

-- Export the model
local json_output = makerjs_engine.export_model("arc_fix_test")

-- Save to file
local file = io.open("arc_fix_test_output.json", "w")
if file then
    file:write(json_output)
    file:close()
    print("✓ Arc fix test model exported to arc_fix_test_output.json")
else
    print("✗ Failed to save output file")
end

print("=== Arc Fix Test Complete ===")
print("Expected: Quarter arc should appear from (300,200) to (200,100) - top-right quadrant")
