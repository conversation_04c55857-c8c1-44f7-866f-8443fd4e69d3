-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  limit = 350
 
  a = 50          -- Kenardan Vbite
  aa = 20					-- Dar kapak için Kenardan mesafe
  ad = 6          -- Vbit derinliği (sunkenDepth)
  aV = 120        -- Vbit Açısı
  
  topGrooveExist    		= 2   -- Açılı V üzerinde kanal var mı? derinlik/0:yok
  extEdgeVtoolExist       	= 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
   local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  --gaps = 30
  --local bd = 3    -- <PERSON><PERSON>li V Ustu kanal derinliği
  --dd = 2          -- iç kanal derinliği
  local cT = 6          -- Açılı V üzerinde kanal bıçak çapı
	
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  
  local intGrooveExist  			= 1   -- iç kanal var mı? derinlik/ 0:yok
  local D = edgeCornerRExist
  
  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  local sunkenDepth = ad
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
  
  --burası ortadaki çizgiler
  local gaps = sunkenWidth*2    --derinlik ve takım çapına göre aralık belirlendi
  local gobekGenislik = Y-2*ust-2*sunkenWidth
  
  local stepX = math.floor(gobekGenislik/gaps)
  
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
  
  local aprxGaps = gobekGenislik/(stepX+1)
  local e = ust+sunkenWidth+aprxGaps
  --burası ortadaki çizgiler^
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
  
  if topGrooveExist > 0 then
      --G.setLayer("K_Ballnose") 
      G.setLayer("K_Freze"..cT.."mm")  -- 
      G.setThickness(-topGrooveExist)
      distance = a + cT/2
      distance2 = ust + cT/2
      point1 = {distance, distance2}
	  point2 = {X-distance, Y-distance2}
	  G.rectangle(point1,point2)
  end
  
  G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, sunkenDepth, aV, vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  if extEdgeVtoolExist > 0 then
  	G.setLayer("K_AciliV_Pah")
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if intGrooveExist > 0 then      ------İç kanal varsa
    -- local check = 2*a + 2*sunkenWidth +2*d
    -- if X < check or Y < check then
      -- print("Part dimension too small, check a + h + d value")
      -- return true
    -- end
    G.setLayer("K_AciliV" .. aV)  -- 
    G.setThickness(-ad)
    -- distance = a + 2*sunkenWidth + d
    -- point1 = {distance, distance}
    -- point2 = {X-distance, Y-distance}
    -- G.rectangle(point1,point2)
  
  --burası ortadaki çizgiler
	for i=0,stepX-1 do
		local y = e+i*aprxGaps
		local offset = a + sunkenWidth
		point3 = {offset,y}
		--point3 = {x,0}
		point4 = {X-offset,y}  
		--point4 = {x,Y}  
		G.line(point3,point4,0)
		i = i+1
	end 
  --burası ortadaki çizgiler
  
  end
    
  return true
end

------------------------------------------------

require "ADekoDebugMode"
