--[[
OpenCascade.js Worker De<PERSON>

This script demonstrates the new OpenCascade.js worker-based 3D geometry generation
by creating a simple door with holes and cuts that will be processed by the worker.
--]]

function modelMain()
  G = ADekoLib
  
  -- Door dimensions
  local doorWidth = 600
  local doorHeight = 800
  local doorThickness = 18
  
  G.setThickness(-doorThickness)
  
  -- Create door outline (PANEL layer)
  G<PERSON>setLayer("PANEL")
  G.make<PERSON>art<PERSON>ha<PERSON>({0, 0}, {doorWidth, 0}, {doorWidth, doorHeight}, {0, doorHeight}, {0, 0})
  
  -- TOP SURFACE OPERATIONS
  G.setFace("top")
  
  -- Simple rectangular groove
  G.setLayer("K_Freze10mm")
  G.setThickness(-3)
  G.rectangle({50, 50}, {550, 150})
  
  -- Circular pocket
  G.setLayer("K_Ballnose6mm")
  G.setThickness(-2)
  G.circle({300, 400}, 80)
  
  -- Decorative V-groove
  G.setLayer("K_AciliV90")
  G.setThickness(-1.5)
  <PERSON><PERSON>rectangle({100, 600}, {500, 650})
  
  -- Holes for hardware
  <PERSON><PERSON>setLayer("Drill5mm")
  G.setThickness(-8)
  
  -- Hinge holes
  G.circle({30, 150}, 2.5)  -- Top hinge
  G.circle({30, 400}, 2.5)  -- Middle hinge
  G.circle({30, 650}, 2.5)  -- Bottom hinge
  
  -- Handle hole
  G.circle({570, 400}, 10)
  
  -- BOTTOM SURFACE OPERATIONS
  G.setFace("bottom")
  
  -- Hinge mortises
  G.setLayer("H_Freze20mm_SF")
  G.setThickness(-3)
  G.rectangle({10, 100}, {50, 200})  -- Top hinge mortise
  G.rectangle({10, 350}, {50, 450})  -- Middle hinge mortise
  G.rectangle({10, 600}, {50, 700})  -- Bottom hinge mortise
  
  -- Cable routing groove
  G.setLayer("H_Freze8mm")
  G.setThickness(-5)
  G.line({100, 750}, {500, 750})
  G.line({500, 750}, {500, 50})
  
  print("OpenCascade Demo: Door model with " .. doorWidth .. "x" .. doorHeight .. "mm dimensions")
  print("Features: Grooves, pockets, V-carves, holes, and mortises")
  print("This model will be processed by the OpenCascade.js worker for 3D visualization")
end
