-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
	minimum = 150
	limit = 350
	
  a = 60  
  aa = 30
  ad = 6                -- kanal derinliği (sunkenDepth)
 
  cT = 5                -- İnce bıçak çapı üst kanal ve Köşe Temizleme
  yaricap = 50
  gaps = 50
  
  extEdgeVtoolExist      			= 0   -- <PERSON><PERSON><PERSON> kenar Pah işlemi var mı? derinlik/0:yok
  edgeCornerRExist          	  	= 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  topGrooveExist   		    		= 2   -- Dış kanal var mı? derinlik/0:yok
  intGrooveExist   		    		= 2   -- Dı<PERSON> kanal var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  local aV = 120              -- PAh V Uç Açısı
  ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
    

  local extGrooveOffset				= 0   -- istenirse Vbit üstü kanal kaydırma mesafesi
  local tarama						= true 
	
	  local Dd = edgeCornerRExist
  local Bb = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape(        ----chamfered part shape
      {Dd,0},		
      {X-Dd,0,0,Bb},
      {X,Dd},
      {X,Y-Dd,0,Bb},
      {X-Dd,Y},
      {Dd,Y,0,Bb},
      {0,Y-Dd},
      {0,Dd,0,Bb},
      {Dd,0}
      )
  else
    G.makePartShape()
  end

	if X < xMin or Y < yMin then
		print("Part dimension too small")
		return true
	end
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
  local vWideDiameter = 100
  local gobekGenislik = X-2*a-2*sunkenWidth-2*yaricap
    
  local stepX = math.floor(gobekGenislik/gaps)
  
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
  local aprxGaps = gobekGenislik/(stepX+1)
  local e = a+sunkenWidth+aprxGaps/2 + yaricap


  
  --G.setLayer("H_Freze10mm_Dis")
  --G.setThickness(-notchDepth)
  --G.rectangle ({notchWidth,notchWidth},{X-notchWidth,Y-notchWidth})

	
	local bulge1 = -math.tan(math.pi/8)
	
	--		2------1-9
	--	3--------------8
	--	----------------
	--	----------------
	--	----------------
	--	----------------
	--	4--------------7
	--		5-------6
		
	local points = {                                    
    	{X-yaricap-a, Y-ust},  					--1			
    	{yaricap+a, Y-ust, 0, bulge1},       	--2			
    	{a, Y- yaricap - ust},         			--3			
    	{a, yaricap + ust, 0, bulge1},         	--4			
    	{a+yaricap, ust},         				--5			
    	{X-yaricap-a, ust,0,bulge1},            --6			
    	{X-a, ust+yaricap},           			--7			
    	{X-a, Y-yaricap-ust, 0, bulge1},   		--8			
    	{X-yaricap-a, Y-ust}		  			--9		
    	}

		G.setLayer("VOyuk_AciliV" .. aV)  -- 
		G.setThickness(-ad)
		G.polylineimp(points)
		-- if extGrooveOffset ~= 0 then
			-- G.polylineimp(G.offSet(points, -extGrooveOffset))
		-- else
			-- G.polylineimp(points)
		-- end	
	if extEdgeVtoolExist > 0 then
		-- G.setLayer("K_AciliV" .. aV)  -- 
		G.setLayer("K_AciliV_Pah")		
		G.setThickness(-extEdgeVtoolExist)
		G.rectangle({0,0},{X,Y})
	end
	
	
	if topGrooveExist > 0 then              --dışarda kanal işlemi var mı?
		
		G.setLayer("K_Freze"..cT.."mm")
		G.setThickness(-topGrooveExist)
		
		if extGrooveOffset >= 0 then
			-- G.polylineimp(points)
			G.polylineimp (G.offSet(points, -extGrooveOffset-cT/2))
		else
			G.polylineimp(points)
		end
		
	end
	
	if tarama then              --Göbekte tarama var mı?
		G.setLayer("Cep_Acma")
		G.setThickness(-ad)
		G.polylineimp (G.offSet(points, -sunkenWidth))
		G.setLayer("K_Freze"..cT.."mm")
		G.setThickness(-ad)
		G.polylineimp (G.offSet(points, -(sunkenWidth+cT/2)))
	end
		
  if intGrooveExist > 0 then      ------İç kanal varsa
    if gobekGenislik > 100  then
      -- print("Part dimension too small, check a + h + d value")
      -- return true
      G.setLayer("K_Ballnose")  -- DEEP cleanup
      G.setThickness(-ad-intGrooveExist)
      -- distance = a + sunkenWidth + h + d
      -- point1 = {distance, distance}
      -- point2 = {X-distance, Y-distance}
      -- G.rectangle(point1,point2)
      
      --burası ortadaki çizgiler
      for i=0,stepX do
        local x = e+i*aprxGaps
        local offset = ust + sunkenWidth+10
        point3 = {x,offset}
        --point3 = {x,0}
        point4 = {x,Y-offset}  
        --point4 = {x,Y}  
        G.line(point3,point4,0)
        i = i+1
      end 
      --burası ortadaki çizgiler
    end
  end
  
	


  return true
end


------------------------------------------------
require "ADekoDebugMode"