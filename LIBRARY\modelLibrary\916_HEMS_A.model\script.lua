-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 350
  limit2 = 300 --daha kucuk kapaklarda gobekte sadece tarama
  
  a = 50 --Kenardan Vbite
  aa = 30					-- Dar kapak için Kenardan mesafe
  b = 10 --Vbitten Dıs kanala
  h = 25 -- Vbitten göbeğe
  d = 15 --Göbekten iç kanala
  ad = 6 -- Vbit derinliği (sunkenDepth)
  aV = 120    --Vbit Açısı
  cW = 20     -- Tarama Bıçak Çapı
  cT = 6      -- İnce bıçak çapı (Köşe Temizleme)
  gaps = 50
  sbt = 0		
  
  extGrooveExist    = 0  --Dış kanal var mı (Var: Derinlik / Yok: 0)
  extGrvBorForV	= 1		--<PERSON><PERSON><PERSON> kanal1 Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)  
  extGrvExtExist	= 0 --dış kanal var mı
  
  cabCoreExist     = 1   --Göbek var mı
  intRectGrvExist = 0  --iç dikdörtgen kanal var mı (Var: Derinlik / Yok: 0)
  intGrvExist  = 2	 --iç kanal var mı (Var: Derinlik / Yok: 0)
  grooveVert_Hor = 1 	--intGrvExist varsa çalışır--1 vertical 0 horizontal
  shapeToolExist = 0   -- Göbek Desen bıçağı var mı?(Var: Derinlik / Yok: 0)
  
  extEdgeToolExist = 0  --Dış kenar Pah işlemi var mı
  extEdgeVorR 		= 0  --Dış kenar Pah işlemi V mi? Raduslu mu? (V:0/R:1)  
  edgeCornerRExist = 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
   
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local D = edgeCornerRExist
  local B = math.tan(math.pi/8)
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist > 0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  
   local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end

  -- if noCabCoreBLimit2 == 1 then
	if X<limit2 or Y<limit2 then
		ust = a
		cabCoreExist = 0
	end
  -- end
  local sunkendepth = ad
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
  
  local gKanalMesafesi = 0
  
  if intRectGrvExist > 0 and intGrvExist > 0 then
    gKanalMesafesi = d
  end
  
  --burası ortadaki çizgiler
  local gobekGenislik = X-2*a-2*sunkenWidth-2*h-2*sbt-2*gKanalMesafesi
  local gobekGenislik2 = Y-2*ust-2*sunkenWidth-2*h-2*sbt-2*gKanalMesafesi
  
  --dikey çizgiler
  local stepX = math.floor(gobekGenislik/gaps)
  
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
  local aprxGaps = gobekGenislik/(stepX+1)
  local e = a+sunkenWidth+h+sbt+aprxGaps/2+gKanalMesafesi
  --yatay çizgiler
  local stepY = math.floor(gobekGenislik2/gaps)
  
	if ((gobekGenislik2/gaps)-stepY)  >= 0.5 then
		stepY = stepY + 1
	end
  
  local aprxGaps2 = gobekGenislik2/(stepY+1)
  local e2 = ust+sunkenWidth+h+sbt+aprxGaps2/2+gKanalMesafesi
  --burası ortadaki çizgiler^
    
	
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
      
  G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
	if extEdgeToolExist  > 0 then
		if extEdgeVorR == 0 then --  (V:0/R:1)
			G.setLayer("K_AciliV_Pah")	
		elseif extEdgeVorR ==1 then
			G.setLayer("H_Raduslu_Pah_" .. "DIS")	
		end
		G.setThickness(-extEdgeToolExist)
		G.rectangle({0,0},{X,Y})
	end
  
	if extGrooveExist  > 0 then
		    
		if extGrvBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			G.setLayer("K_Ballnose")
		elseif extGrvBorForV == 1 then
			G.setLayer("K_Freze")  
		elseif extGrvBorForV == 2 then
			G.setLayer("K_AciliV")  
		end	
		G.setThickness(-extGrooveExist)
		distance = a-b
		distance1 = ust-b
	
		if extGrvExtExist == 1 then
			G.line({distance, 0}, {distance, Y})
			G.line({X-distance, 0}, {X-distance, Y})
			G.line({distance, distance1}, {X-distance, distance1})
			G.line({distance, Y-distance1}, {X-distance, Y-distance1})
		else
			point1 = {distance, distance1}
			point2 = {X-distance, Y-distance1}
			G.rectangle(point1,point2)
		end
		
	end
  
  if cabCoreExist == 1 then
      
      -- if h<cT and h<cW then
        -- print("Tool too large")
        -- return true
	  -- elseif h<cW and h>cT then
	if h<cW and h>cT then
		cW = cT
    end
	  
	if h > cT then
    -- if X>xLimit and Y>yLimit then
      G.setLayer("K_Freze" .. cW .. "mm")  -- DEEP cleanup
      G.setThickness(-ad)
      distance = a+sunkenWidth+cW/2
      distance1 = ust+sunkenWidth+cW/2
      point1 = {distance, distance1}
      point2 = {X-distance, Y-distance1}
      G.rectangle(point1,point2)
      
      if h>cW then
        distance = a+sunkenWidth+h-cW/2
        distance1 = ust+sunkenWidth+h-cW/2
        point3 = {distance, distance1}
        point4 = {X-distance, Y-distance1}
        --G.rectangle(point3,point4)
        k = (h-cW)/(cW/2)
        for i=1, k, 1 do
          point1 = G.ptAdd(point1,{cW/2,cW/2})
          point2 = G.ptSubtract(point2, {cW/2,cW/2})
          if point1[1]>point3[1]-cW/2 then
            break
          end
          G.rectangle(point1,point2)
        end
      end
      G.setThickness(-(ad))
      distance = a+sunkenWidth+h-cW/2
      distance1 = ust+sunkenWidth+h-cW/2
      point1 = {distance, distance1}
      point2 = {X-distance, Y-distance1}
      G.rectangle(point1,point2)
      
      if shapeToolExist  > 0 then  ----Göbekte desen bıçağı varsa
        G.setLayer("K_Desen")  -- DEEP cleanup
        G.setThickness(-shapeToolExist)
        distance = a + sunkenWidth + h
        distance1 = ust + sunkenWidth + h
        point1 = {distance, distance1}
        point2 = {X-distance, Y-distance1}
        G.rectangle(point1,point2)
      else 
        G.setLayer("K_AciliV" .. aV)  --- Gobek
        G.setThickness(-ad)
        distance = a + sunkenWidth + h
        distance1 = ust + sunkenWidth + h
        point1 = {distance, distance1}
        point2 = {X-distance, Y-distance1}
        G.rectangle(point1,point2)
      end
	end
    if h >= 0 then
	
		if intRectGrvExist  > 0 then      ------İç kanal varsa
			local check = 2*a + 2*h + 2*d
			if X < check or Y < check then
			print("Part dimension too small, check a + h + d value")
			return true
			end
			G.setLayer("K_I_Kanal")  -- DEEP cleanup
			G.setThickness(-intRectGrvExist)
			distance = a + sunkenWidth + h + d
			distance1 = ust + sunkenWidth + h + d
			point1 = {distance, distance1}
			point2 = {X-distance, Y-distance1}
			G.rectangle(point1,point2)
		end
	  
		if intGrvExist > 0 then      ------İç kanal varsa
			local checkX = 2*a + 2*h + 2*sunkenWidth
			local checkY = 2*ust + 2*h + 2*sunkenWidth
			if X > checkX and Y > checkY then
				if grooveVert_Hor == 1 then
					G.setLayer("K_I_Kanal")  -- DEEP cleanup
					G.setThickness(-intGrvExist)
					-- distance = a + sunkenWidth + h + d
					-- point1 = {distance, distance}
					-- point2 = {X-distance, Y-distance}
					-- G.rectangle(point1,point2)
					
					--burası ortadaki çizgiler
					for i=0,stepX do
						local x = e+i*aprxGaps
						local offset = ust + sunkenWidth + h + gKanalMesafesi
						point3 = {x,offset}
						--point3 = {x,0}
						point4 = {x,Y-offset}  
						--point4 = {x,Y}  
						G.line(point3,point4,0)
						i = i+1
					end 
				elseif grooveVert_Hor == 0 then
					G.setLayer("K_I_Kanal")  -- DEEP cleanup
					G.setThickness(-intGrvExist)
					-- distance = a + sunkenWidth + h + d
					-- point1 = {distance, distance}
					-- point2 = {X-distance, Y-distance}
					-- G.rectangle(point1,point2)
					
					--burası ortadaki çizgiler
					for i=0,stepY do
						local y = e2+i*aprxGaps2
						local offset2 = a + sunkenWidth + h + gKanalMesafesi
						point3 = {offset2,y}
						--point3 = {x,0}
						point4 = {X-offset2,y}  
						--point4 = {x,Y}  
						G.line(point3,point4,0)
						i = i+1
					end 
				end
			end
		end
	end
      
    -- else ------------- bu araya 250 den küçükler için tarama gelebilir
      -- G.setLayer("Cep_Acma".. cW .. "mm")  -- DEEP cleanup
      -- G.setThickness(-ad)
      -- distance = a+sunkenWidth
      -- distance1 = ust+sunkenWidth
      -- point6 = {distance, distance1}
      -- point7 = {X-distance, Y-distance1}
      -- G.rectangle(point6,point7)
    -- end  
  else    -------------- Göbek yoksa tüm kapaklarda
    G.setLayer("Cep_Acma".. cW .. "mm")  -- DEEP cleanup
    G.setThickness(-ad)
    distance = a+sunkenWidth
    distance1 = ust+sunkenWidth
    point1 = {distance, distance1}
    point2 = {X-distance, Y-distance1}
    G.rectangle(point1,point2)
  end
  
  G.setLayer("K_TarKoseTemizl" .. cT .. "mm")
  
  if h >= cT and h < 3*cT and cabCoreExist == 1 then
    G.setThickness(-ad)
    distance = a+sunkenWidth+cT/2
    distance1 = ust+sunkenWidth+cT/2
    point1 = {distance, distance1}
    point2 = {X-distance, Y-distance1}
    G.rectangle(point1,point2)
  elseif h >= 3*cT or cabCoreExist == 0 then
    G.setThickness(0)
    G.cleanCorners(corner1,corner2,ad,cT)
  end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
