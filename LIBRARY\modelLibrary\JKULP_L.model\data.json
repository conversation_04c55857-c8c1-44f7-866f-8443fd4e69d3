{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 0, "description": "----------JKULP HANGİ KENARLARDA----------", "parameterName": "-------"}, {"defaultValue": 1, "description": "<PERSON><PERSON> kena<PERSON>a?(<PERSON><PERSON>1,<PERSON><PERSON>2,<PERSON>g-3,<PERSON><PERSON>-4,solsag-13,altust-24)", "parameterName": "jk_side"}, {"defaultValue": 10, "description": "<PERSON><PERSON><PERSON> me<PERSON>", "parameterName": "a"}, {"defaultValue": 10, "description": "<PERSON><PERSON><PERSON> me<PERSON>", "parameterName": "b"}, {"defaultValue": 10, "description": "<PERSON><PERSON><PERSON> olan yandan buyutme miktarı", "parameterName": "m"}, {"defaultValue": 1, "description": "Panel olculerini m kadar <PERSON>(0-<PERSON><PERSON>, 1-<PERSON><PERSON>)", "parameterName": "widIncrExist"}, {"defaultValue": 6, "description": "Kapak olcusune eklenen-fazlalık olan kenari kesen takim capi", "parameterName": "jNotchToolDia"}, {"defaultValue": 11111, "description": "----------K1Opr Parametsesi 0 Degilse Aktif Olur---------", "parameterName": "--1--"}, {"defaultValue": 50, "description": "<PERSON><PERSON><PERSON> kanala mesafe", "parameterName": "K1Off"}, {"defaultValue": 1, "description": "Islem tipi(1-K_Kanal_TN, 2-K_Form)", "parameterName": "K1Opr"}, {"defaultValue": 2, "description": "<PERSON><PERSON> der<PERSON>", "parameterName": "K1Depth"}, {"defaultValue": 41, "description": "<PERSON><PERSON>(TN)", "parameterName": "K1ToolNumber"}, {"defaultValue": 22222, "description": "----------K2Opr Parametsesi 0 Degilse Aktif Olur---------", "parameterName": "--2--"}, {"defaultValue": 10, "description": "Kanal1den kanal2ye mesafe", "parameterName": "K2Off"}, {"defaultValue": 0, "description": "<PERSON>m tipi(0-<PERSON><PERSON>,1-<PERSON>_<PERSON><PERSON>_TN, 2-K_<PERSON>)", "parameterName": "K2Opr"}, {"defaultValue": 5, "description": "<PERSON><PERSON> der<PERSON>", "parameterName": "K2Depth"}, {"defaultValue": 90, "description": "<PERSON><PERSON>(TN)", "parameterName": "K2ToolNumber"}, {"defaultValue": 33333, "description": "----------K3Opr Parametsesi 0 Degilse Aktif Olur---------", "parameterName": "--3--"}, {"defaultValue": 5, "description": "Kanal2den Kanal3e mesafe", "parameterName": "K3Off"}, {"defaultValue": 0, "description": "<PERSON>m tipi(0-<PERSON><PERSON>,1-<PERSON>_<PERSON><PERSON>_TN, 2-K_<PERSON>)", "parameterName": "K3Opr"}, {"defaultValue": 5, "description": "<PERSON><PERSON> der<PERSON>", "parameterName": "K3Depth"}, {"defaultValue": 42, "description": "<PERSON><PERSON>(TN)", "parameterName": "K3ToolNumber"}], "tags": [], "version": 1}