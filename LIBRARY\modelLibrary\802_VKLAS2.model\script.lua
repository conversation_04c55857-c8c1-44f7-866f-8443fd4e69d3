-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
	minimum = 150
	limit = 350
	
  a = 60  
  aa = 30
  b = 0					--Vbit Ustu Kanal Kaydirma Mesafesi
  ad = 6                -- Vbit derinliği (sunkenDepth)
  aV = 120              -- Vbit Açısı
  yy = 20				-- yay kısmının yüksekliği
  cT = 6                -- İnce bıçak çapı (Köşe Temizleme)
    
  intVerticalGrooveExist 			= 0   -- iç kanal var mı? dik çizgiler  derinlik/ 0:yok
  extEdgeRtoolExist      			= 0   -- Dı<PERSON> kenar Pah işlemi var mı? derinlik/0:yok
  edgeCornerRExist          	  	= 3   -- Ka<PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  
  --cR = 5		  		-- Radus Veren form bıcak düz uç dar çapı
  local sbt = 0		 		-- Göbeğe 1 kanal daha eklemek için ve ayrıca kaydırma mesafesi ekler - yoksa-"0"-sa gaps/2
  local gaps = 50				-- kanallar arası mesafe

  local extGrooveExist   		    = 2   -- Dış kanal var mı? derinlik/0:yok
  local intGrooveExist 			    = 0   -- iç kanal var mı? derinlik/ 0:yok
  
  local notchWidth = 0
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)

  local Dd = edgeCornerRExist
  local Bb = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape(        ----chamfered part shape
      {Dd,0},		
      {X-Dd,0,0,Bb},
      {X,Dd},
      {X,Y-Dd,0,Bb},
      {X-Dd,Y},
      {Dd,Y,0,Bb},
      {0,Y-Dd},
      {0,Dd,0,Bb},
      {Dd,0}
      )
  else
    G.makePartShape()
  end

	if X < xMin or Y < yMin then
	print("Part dimension too small")
	return true
	end
  
  --G.setLayer("H_Freze10mm_Dis")
  --G.setThickness(-notchDepth)
  --G.rectangle ({notchWidth,notchWidth},{X-notchWidth,Y-notchWidth})
	
	
  ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  
	yh = ust+yy
	
	local kenardan_mesafe1 = a + sunkenWidth
	local kenardan_mesafe2 = ust + sunkenWidth				--
	local kenardan_mesafe3 = yh + sunkenWidth				--içteki kanalların  üst yay başlangıç noktası mesafesi
	
	local gobekGenislik = X-2*a-2*sunkenWidth-2*sbt
	local stepX = math.floor(gobekGenislik/gaps)
	  
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local aprxGaps = gobekGenislik/(stepX-1)
	-- local e = a+sunkenWidth*2+aprxGaps
	if sbt == 0 then
		e = a+sunkenWidth+aprxGaps
		stepX = stepX-2
	else
		e = a+sunkenWidth+sbt+aprxGaps/2
		stepX = stepX-1
	end
	
	local bulge1 = G.bulge(
		{X-notchWidth-a, Y-notchWidth-yh, 0, 0},
		{X/2, Y-notchWidth-ust}, {a, Y-notchWidth-yh}
		)
	local points = {
    	{X-notchWidth-a, Y-notchWidth-yh, 0, bulge1},  --1
    	{notchWidth+a, Y-notchWidth-yh, 0, 0},       --2
    	{notchWidth+a, notchWidth+ust, 0, 0},         --3
    	{X-notchWidth-a, notchWidth+ust},             --4
    	{X-notchWidth-a, Y-notchWidth-yh, 0, bulge1}   --5
    	}
  
	G.setLayer("K_AciliV"..aV)					                ----Dış Sunken
	G.setThickness(-ad)
	G.sunkenFrameAny(points, 30,ad,aV,vWideDiameter)

	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
    
	--burası ortadaki çizgiler
	--çizgiler
	if intVerticalGrooveExist > 0 then            --içerde dik kanallar var mı?
	
		local point1 = {X-kenardan_mesafe1, Y-kenardan_mesafe3, 0, 0}		--
		local bulge = G.bulge(point1, {X/2, Y-kenardan_mesafe2}, {kenardan_mesafe1, Y-kenardan_mesafe3})
		if (bulge>1) then
			print("Bulge too large")
			return true
		end
		point1[4] = bulge
		
		local point2 = {kenardan_mesafe1, Y-kenardan_mesafe3, 0, 0}
		local radius = G.radius(point1, point2, bulge)
		
		
		for i=0, (stepX-1), 1		-- loop for the vertical lines --i=0, stepX di
		do
			local Lp1 = {e + i*aprxGaps, kenardan_mesafe2}		-- imaginary lines to intersect the above arc
			local Lp2 = {e + i*aprxGaps, Y}
			-- local Lp1 = {kenardan_mesafe1 + sbt + i*aprxGaps, kenardan_mesafe2}		-- imaginary lines to intersect the above arc
			-- local Lp2 = {kenardan_mesafe1 + sbt + i*aprxGaps, Y}
			comment, pc1, pc2 = G.circleCircleIntersection(point1, radius, point2, radius)		--find center of the arc
			if (comment=='tangent' or comment=='intersection') then
				comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		--find line-arc intersection point
				if (comment2=='tangent' or comment2=='secant') then
				if (i==0 or i==stepX) then 
				--G.setLayer("K_Freze10mm")      -- GELMEYECEK AMA KALSIN
				else
				G.setLayer("K_Kanal")
				end
				G.setThickness(-intVerticalGrooveExist)
					G.line(intersection1, Lp1)
				end
			else
				G.error()
			return false
			end
		end
	end

  --çizgiler bitti
  
  if intGrooveExist > 0 then              --içerde kanal işlemi var mi?
    
	G.setLayer("K_Freze"..cT.."mm")
    G.setThickness(-intGrooveExist)
    G.polylineimp (G.offSet(points, -(2*sunkenWidth)))
    
  end
   
  if extGrooveExist > 0 then              --dışarda kanal işlemi var mı?
    
    G.setLayer("K_Freze"..cT.."mm")
    G.setThickness(-extGrooveExist)
    
    if extGrooveOffset ~= 0 then
      G.polylineimp (G.offSet(points, b - cT/2))
    else
      G.polylineimp(points)
    end
    
  end
  
  return true
end


------------------------------------------------
require "ADekoDebugMode"