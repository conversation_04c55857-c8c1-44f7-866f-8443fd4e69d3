{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Desen\t\t*formTool varsa* Desen Bicagi \nK_AciliV*aV*\t\t*aV* Derece V Bicak \nH_Freze*cT*mm_Ic\t\t*cT*mm Freze Bicagi \nH_Freze*cW*mm_Ic_SF\t*cW*mm Freze Bicagi \nK_Freze*cW*mm_SF\t*cW*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 50, "description": "width of the frame", "parameterName": "g"}, {"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 350, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 0, "description": "DIS Kenar form takim var mi?(Var :1/ Yok:0)", "parameterName": "formTool"}, {"defaultValue": 90, "description": "Acili vbit acisi", "parameterName": "aV"}, {"defaultValue": 7, "description": "<PERSON><PERSON> derin<PERSON>gi", "parameterName": "ad"}, {"defaultValue": 10, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 5, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 14, "description": "mm, window depth from top", "parameterName": "windowDepthFront"}, {"defaultValue": 6, "description": "mm, window depth from bottom", "parameterName": "windowDepthBack"}, {"defaultValue": 2.5, "description": "Kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}