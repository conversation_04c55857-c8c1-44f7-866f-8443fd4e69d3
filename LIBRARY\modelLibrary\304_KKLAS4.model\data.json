{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV\t\t aV Derece V bicak \nK_Kanal_cT_mm\t\t cT capli kanal islemi icin kullanilacak bir bicak \nK_Desen\t\tDesen-motif bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 0, "description": "Acili V kenardan ust kanala mesafe", "parameterName": "b"}, {"defaultValue": 25, "description": "Acili V kenardan IC desen-kanala mesafe", "parameterName": "d"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 6, "description": "Acili V ustu kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 2, "description": "Vbit ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 5, "description": "Ic desen islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}