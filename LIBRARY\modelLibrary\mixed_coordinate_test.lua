-- Mixed Coordinate System Test
-- This script demonstrates both coordinate systems in one test

function modelMain()
    print("=== Mixed Coordinate System Test ===")
    
    -- First, draw the ADekoLib face layout (this will trigger ADekoLib coordinate mode)
    -- Draw face labels to trigger the ADekoLib layout detection
    text("Left", 0, 40, 20)
    text("Right", 0, 540, 796)
    text("Top", 0, 40, 796)
    text("Bottom", 0, 638, 796)
    text("Front", 0, 598, 796)
    text("Rear", 0, 40, 758)
    
    -- Now draw some simple geometry that should align with the face layout
    -- Draw a rectangle on the "top" face area
    pnsz(2)
    pncl("blue")
    pndn()
    
    -- Position at the top face area (based on ADekoDebugMode.lua coordinates)
    zero(40 + 20, 3*20 + 2*18)  -- offset=20, materialThickness=18
    
    -- Draw a simple rectangle
    move(100)
    turn(90)
    move(80)
    turn(90)
    move(100)
    turn(90)
    move(80)
    turn(90)
    
    -- Add a circle
    pnup()
    posn(50, 40)
    pndn()
    pncl("red")
    crcl(0, 0, 15)
    
    -- Add descriptive text
    pnup()
    posn(120, 40)
    text("ADekoLib Layout", 0, 0, 0)
    
    print("Mixed coordinate system test completed!")
end
