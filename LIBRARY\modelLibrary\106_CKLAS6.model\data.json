{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Freze_cW_mm\t\t cW Capli Freze bicagi \nK_Freze_cT_mm\t\t cT Capli Freze bicagi \nK_Raduslu_cD_mm\t Raduslu form bicagi \nH_Raduslu_Pah_DIS\tRaduslu pah bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 25, "description": "Acili V kenardan gobege mesafe", "parameterName": "h"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 15, "description": "Radus veren bicak genis cap", "parameterName": "cD"}, {"defaultValue": 5, "description": "Radus veren bicak duz uc capi", "parameterName": "cR"}, {"defaultValue": 20, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 6, "description": "Ic kose finis ve Avici V ustu kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 2, "description": "Acili vbit ustu kanal islemi var mi?(Var: Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 5, "description": "Gobek desen bicagi var mi?(Var: Derinlik/ Yok:0)", "parameterName": "shapeToolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}