-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 70
  aa = 30
  ad = 8
  aV = 60
  
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 10
    
  windowDepthFront = 14
  windowDepthBack = 6
  
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> i<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> rad<PERSON><PERSON> yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
      
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  
  local vNarrowDiameter = 30
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("K_AciliV"..aV)
  G.setThickness(-ad)
  
  -- local point1 = {X-a-sunkenWidth, Y-a-sunkenWidth, 0, 0}
  -- local bulge = G.bulge(point1, {X/2, Y-a/2-sunkenWidth}, {a, Y-a-sunkenWidth})
  local point1 = {X-a-sunkenWidth, Y-ust*1.5-sunkenWidth, 0, 0}
  local bulge = G.bulge(point1, {X/2, Y-ust-sunkenWidth}, {a+sunkenWidth, Y-ust*1.5-sunkenWidth})
 
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
  point1[4] = bulge
  local point2 = {a+sunkenWidth, Y-ust*1.5-sunkenWidth, 0, 0}
  point3 = {a+sunkenWidth, ust*1.5+sunkenWidth}
  point3[4] = bulge
  point4 = {X-a-sunkenWidth, ust*1.5+sunkenWidth}
  points = {point1,point2,point3,point4,point1}
  G.polylineimp(points)
  
  G.setLayer("H_Freze".. cT .."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(points)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze".. cW .."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points,cW/2))
  
  G.setLayer("K_Freze".. cW .."mm_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(points)
  
  return true
end

require "ADekoDebugMode"