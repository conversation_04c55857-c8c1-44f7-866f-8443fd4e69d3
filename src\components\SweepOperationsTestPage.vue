<template>
  <div class="sweep-test-page fixed inset-0 z-50 bg-gray-50 flex flex-col">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Sweep Operations Test Suite</h1>
        <p class="text-gray-600 mt-1">Comprehensive testing for OpenCascade.js sweep operations and boolean geometry</p>
      </div>
      <button
        @click="$emit('close')"
        class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded transition-colors"
      >
        Close
      </button>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex overflow-hidden">
      <!-- Left Panel - Test Controls -->
      <div class="w-1/3 bg-white border-r border-gray-200 flex flex-col">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Test Categories</h2>
        </div>
        
        <div class="flex-1 overflow-y-auto p-4 space-y-4">
          <!-- Basic Tests -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Basic Sweep Tests</h3>
            <div class="space-y-2">
              <button 
                @click="runTest('basic-door-body')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('basic-door-body')"
              >
                <div class="font-medium">Door Body Creation</div>
                <div class="text-sm text-gray-600">Test basic door panel generation from PANEL layer</div>
              </button>
              
              <button 
                @click="runTest('basic-tool-brep')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('basic-tool-brep')"
              >
                <div class="font-medium">Tool BRep Generation</div>
                <div class="text-sm text-gray-600">Create 3D tool geometries for all tool types</div>
              </button>
              
              <button 
                @click="runTest('basic-sweep')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('basic-sweep')"
              >
                <div class="font-medium">Simple Sweep Operation</div>
                <div class="text-sm text-gray-600">Basic material removal with single tool</div>
              </button>
            </div>
          </div>

          <!-- Advanced Tests -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Advanced Sweep Tests</h3>
            <div class="space-y-2">
              <button 
                @click="runTest('multi-tool-sweep')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('multi-tool-sweep')"
              >
                <div class="font-medium">Multi-Tool Operations</div>
                <div class="text-sm text-gray-600">Multiple tools with different operations</div>
              </button>
              
              <button 
                @click="runTest('complex-geometry')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('complex-geometry')"
              >
                <div class="font-medium">Complex Geometry</div>
                <div class="text-sm text-gray-600">Advanced shapes with curves and fillets</div>
              </button>
              
              <button 
                @click="runTest('boolean-operations')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('boolean-operations')"
              >
                <div class="font-medium">Boolean Operations</div>
                <div class="text-sm text-gray-600">Union, subtract, intersect operations</div>
              </button>
            </div>
          </div>

          <!-- Performance Tests -->
          <div class="test-category">
            <h3 class="font-medium text-gray-900 mb-2">Performance Tests</h3>
            <div class="space-y-2">
              <button 
                @click="runTest('large-model')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('large-model')"
              >
                <div class="font-medium">Large Model Test</div>
                <div class="text-sm text-gray-600">Test with complex door model and many tools</div>
              </button>
              
              <button 
                @click="runTest('stress-test')"
                :disabled="isRunning"
                class="test-button w-full text-left p-3 rounded border hover:bg-blue-50 disabled:opacity-50"
                :class="getTestButtonClass('stress-test')"
              >
                <div class="font-medium">Stress Test</div>
                <div class="text-sm text-gray-600">Maximum geometry complexity test</div>
              </button>
            </div>
          </div>

          <!-- Utility Actions -->
          <div class="test-category border-t pt-4">
            <h3 class="font-medium text-gray-900 mb-2">Utilities</h3>
            <div class="space-y-2">
              <button 
                @click="clearResults"
                class="w-full p-2 text-sm bg-gray-100 hover:bg-gray-200 rounded border"
              >
                Clear All Results
              </button>
              
              <button 
                @click="runAllTests"
                :disabled="isRunning"
                class="w-full p-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded disabled:opacity-50"
              >
                Run All Tests
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Results and Visualization -->
      <div class="flex-1 flex flex-col">
        <!-- Results Header -->
        <div class="bg-white border-b border-gray-200 p-4">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">Test Results</h2>
            <div class="flex items-center space-x-2">
              <span v-if="isRunning" class="text-sm text-blue-600">
                <span class="inline-block animate-spin mr-1">⚙️</span>
                {{ currentTest }}
              </span>
              <span v-else-if="lastTestResult" class="text-sm" :class="lastTestResult.success ? 'text-green-600' : 'text-red-600'">
                {{ lastTestResult.success ? '✅ Success' : '❌ Failed' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Results Content -->
        <div class="flex-1 flex">
          <!-- Console Output -->
          <div class="w-1/2 border-r border-gray-200 flex flex-col">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-200">
              <h3 class="font-medium text-gray-900">Console Output</h3>
            </div>
            <div class="flex-1 overflow-y-auto p-4 bg-gray-900 text-green-400 font-mono text-sm">
              <div v-for="(log, index) in consoleOutput" :key="index" class="mb-1">
                <span class="text-gray-500">[{{ log.timestamp }}]</span>
                <span :class="getLogClass(log.level)">{{ log.message }}</span>
              </div>
              <div v-if="consoleOutput.length === 0" class="text-gray-500">
                No output yet. Run a test to see results.
              </div>
            </div>
          </div>

          <!-- 3D Visualization -->
          <div class="w-1/2 flex flex-col">
            <div class="bg-gray-100 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
              <h3 class="font-medium text-gray-900">3D Visualization</h3>
              <div class="flex items-center space-x-2">
                <label class="flex items-center space-x-1 text-sm">
                  <input
                    type="checkbox"
                    v-model="wireframeMode"
                    @change="toggleWireframe"
                    class="rounded"
                  >
                  <span>Wireframe</span>
                </label>
                <label class="flex items-center space-x-1 text-sm">
                  <input
                    type="checkbox"
                    v-model="debugMode"
                    @change="toggleDebugMarkers"
                    class="rounded"
                  >
                  <span>Debug Markers</span>
                </label>
                <button
                  @click="resetCamera"
                  class="px-2 py-1 text-xs bg-gray-200 hover:bg-gray-300 rounded"
                >
                  Reset View
                </button>
              </div>
            </div>
            <div class="flex-1 relative">
              <div 
                ref="threeContainer" 
                class="w-full h-full bg-gray-800"
                :class="{ 'opacity-50': isRunning }"
              ></div>
              
              <!-- Loading Overlay -->
              <div v-if="isRunning" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                <div class="text-white text-center">
                  <div class="animate-spin text-4xl mb-2">⚙️</div>
                  <div>{{ processingStep || 'Processing...' }}</div>
                </div>
              </div>
              
              <!-- Empty State -->
              <div v-if="!modelUrl && !isRunning" class="absolute inset-0 flex items-center justify-center text-gray-500">
                <div class="text-center">
                  <div class="text-6xl mb-4">🔧</div>
                  <div>Run a test to see 3D results</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from '../utils/OrbitControls'

// Define emits
const emit = defineEmits<{
  close: []
}>()

// Test state
const isRunning = ref(false)
const currentTest = ref('')
const lastTestResult = ref<{ success: boolean; message: string } | null>(null)
const testResults = ref<Map<string, { success: boolean; message: string; timestamp: string }>>(new Map())

// Console output
interface LogEntry {
  timestamp: string
  level: 'info' | 'success' | 'error' | 'warning'
  message: string
}
const consoleOutput = ref<LogEntry[]>([])

// 3D Visualization
const threeContainer = ref<HTMLElement | null>(null)
const modelUrl = ref<string | null>(null)
const processingStep = ref<string>('')
const wireframeMode = ref<boolean>(false)
const debugMode = ref<boolean>(false)

// Three.js objects
let scene: THREE.Scene | null = null
let camera: THREE.PerspectiveCamera | null = null
let renderer: THREE.WebGLRenderer | null = null
let controls: OrbitControls | null = null
let model: THREE.Object3D | null = null
let debugMarkers: THREE.Object3D[] = []

// OCJS Worker
let worker: Worker | null = null
const pendingMessages = new Map<string, { resolve: Function; reject: Function }>()

// Test definitions
const testDefinitions = {
  'basic-door-body': {
    name: 'Door Body Creation',
    description: 'Creates a basic door panel from PANEL layer geometry',
    luaScript: `
-- Basic Door Body Test
X = 200
Y = 150
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✅ Door body test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'basic-tool-brep': {
    name: 'Tool BRep Generation',
    description: 'Tests creation of 3D tool geometries for different tool types',
    luaScript: `
-- Tool BRep Test
X = 100
Y = 100
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    G.setLayer("8MM")
    G.setThickness(-3)
    G.circle({50, 50}, 4)

    print("✅ Tool BRep test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'basic-sweep': {
    name: 'Simple Sweep Operation',
    description: 'Basic material removal with a single cylindrical tool',
    luaScript: `
-- Basic Sweep Test
X = 150
Y = 100
materialThickness = 18

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    G.setLayer("6MM")
    G.setThickness(-3)
    G.rectangle({25, 25}, {125, 75})

    print("✅ Basic sweep test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'multi-tool-sweep': {
    name: 'Multi-Tool Types Test',
    description: 'Tests different tool types: cylindrical drill, ballnose groove, V-bit pocket & groove operations',
    luaScript: `
-- Multi-Tool Sweep Test
X = 200
Y = 150
materialThickness = 20

function modelMain()
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    G.setLayer("8MM")
    G.setThickness(-5)
    G.rectangle({20, 20}, {100, 60})

    G.setLayer("6MM")
    G.setThickness(-3)
    G.circle({50, 100}, 10)

    G.setLayer("4MM")
    G.setThickness(-2)
    G.line({120, 30}, {180, 30})

    print("✅ Multi-tool test geometry created")
    return true
end

require "ADekoDebugMode"
    `
  },
  'complex-geometry': {
    name: 'Complex Geometry',
    description: 'Advanced shapes with curves, fillets, and complex profiles',
    luaScript: `
-- Complex Geometry Test
G.start()
G.makePart(250, 200)
G.setLayer("PANEL")
G.rectangle({0, 0}, {250, 200})
G.setLayer("V120")
G.polyline({10,10}, {240,10}, {240,190}, {10,190}, {10,10})
G.setLayer("12MM")
G.circle({125, 100}, 30)
G.setLayer("BALLNOSE6")
G.rectangle({50, 50}, {200, 150})
G.finish()
print("✅ Complex geometry test created")
    `
  },
  'boolean-operations': {
    name: 'Boolean Operations',
    description: 'Tests union, subtract, and intersect operations',
    luaScript: `
-- Boolean Operations Test
G.start()
G.makePart(180, 120)
G.setLayer("PANEL")
G.rectangle({0, 0}, {180, 120})
G.setLayer("10MM")
G.circle({45, 60}, 20)
G.setLayer("8MM")
G.rectangle({90, 30}, {150, 90})
G.setLayer("6MM")
G.circle({135, 60}, 15)
G.finish()
print("✅ Boolean operations test geometry created")
    `
  },
  'large-model': {
    name: 'Large Model Test',
    description: 'Performance test with complex door model and many tools',
    luaScript: `
-- Large Model Performance Test
G.start()
G.makePart(400, 300)
G.setLayer("PANEL")
G.rectangle({0, 0}, {400, 300})
-- Create multiple tool operations
for i = 1, 10 do
  G.setLayer("8MM")
  G.circle({50 + i * 30, 50}, 8)
  G.setLayer("6MM")
  G.rectangle({20 + i * 35, 150}, {45 + i * 35, 250})
end
G.finish()
print("✅ Large model test geometry created")
    `
  },
  'stress-test': {
    name: 'Stress Test',
    description: 'Maximum complexity test with numerous operations',
    luaScript: `
-- Stress Test - Maximum Complexity
G.start()
G.makePart(500, 400)
G.setLayer("PANEL")
G.rectangle({0, 0}, {500, 400})
-- Create many different tool types and operations
local tools = {"20MM", "16MM", "12MM", "10MM", "8MM", "6MM", "4MM", "3MM", "V120", "V90", "BALLNOSE8", "BALLNOSE6"}
for i, tool in ipairs(tools) do
  G.setLayer(tool)
  local x = (i % 5) * 90 + 50
  local y = math.floor((i-1) / 5) * 80 + 50
  if tool:find("V") then
    G.polyline({x-20,y-20}, {x+20,y-20}, {x+20,y+20}, {x-20,y+20}, {x-20,y-20})
  elseif tool:find("BALLNOSE") then
    G.circle({x, y}, 15)
  else
    G.rectangle({x-15, y-15}, {x+15, y+15})
  end
end
G.finish()
print("✅ Stress test geometry created")
    `
  }
}

onMounted(() => {
  initializeWorker()
  initThreeJS()
})

onUnmounted(() => {
  cleanup()
})

function initializeWorker() {
  try {
    worker = new Worker('/src/workers/ocjsWorker.ts', { type: 'module' })
    
    worker.onmessage = (event) => {
      const { id, type, data, error } = event.data
      const pending = pendingMessages.get(id)
      
      if (pending) {
        pendingMessages.delete(id)
        if (type === 'success') {
          pending.resolve(data)
        } else {
          pending.reject(new Error(error || 'Unknown worker error'))
        }
      }
    }

    worker.onerror = (error) => {
      addLog('error', `Worker error: ${error.message}`)
    }
    
    addLog('success', 'OCJS Worker initialized successfully')
  } catch (error) {
    addLog('error', `Failed to initialize worker: ${error}`)
  }
}

function initThreeJS(): boolean {
  if (!threeContainer.value) return false

  try {
    // Scene
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0xffffff) // White background

    // Camera
    camera = new THREE.PerspectiveCamera(75, threeContainer.value.clientWidth / threeContainer.value.clientHeight, 0.1, 1000)
    camera.position.set(150, 150, 150)

    // Renderer
    renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(threeContainer.value.clientWidth, threeContainer.value.clientHeight)
    renderer.shadowMap.enabled = false // Disabled due to Three.js proxy issues
    renderer.setClearColor(0xffffff) // White background
    threeContainer.value.appendChild(renderer.domElement)

    // Controls
    controls = new OrbitControls(camera, renderer.domElement)
    // Note: enableDamping and dampingFactor are private in this version

    // Lighting setup for better model visibility
    const ambientLight = new THREE.AmbientLight(0x404040, 0.8)
    scene.add(ambientLight)

    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0)
    directionalLight1.position.set(100, 100, 50)
    scene.add(directionalLight1)

    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.5)
    directionalLight2.position.set(-100, 50, -50)
    scene.add(directionalLight2)

    // Grid helper for reference
    const gridHelper = new THREE.GridHelper(200, 20, 0x444444, 0x444444)
    scene.add(gridHelper)

    // Add coordinate axes for reference
    const axesHelper = new THREE.AxesHelper(50)
    scene.add(axesHelper)

    // Start render loop
    animate()
    
    addLog('success', 'Three.js initialized successfully')
    return true
  } catch (error) {
    addLog('error', `Failed to initialize Three.js: ${error}`)
    return false
  }
}

function animate() {
  if (!renderer || !scene || !camera || !controls) return
  
  requestAnimationFrame(animate)
  controls.update()
  renderer.render(scene, camera)
}

async function sendMessage(type: string, data: any): Promise<any> {
  if (!worker) throw new Error('Worker not initialized')
  
  const id = Math.random().toString(36).substring(2, 11)
  
  return new Promise((resolve, reject) => {
    pendingMessages.set(id, { resolve, reject })
    worker!.postMessage({ id, type, data })
  })
}

function addLog(level: LogEntry['level'], message: string) {
  const timestamp = new Date().toLocaleTimeString()
  consoleOutput.value.push({ timestamp, level, message })
  
  // Auto-scroll to bottom
  setTimeout(() => {
    const consoleEl = document.querySelector('.overflow-y-auto')
    if (consoleEl) {
      consoleEl.scrollTop = consoleEl.scrollHeight
    }
  }, 10)
}

function getLogClass(level: string): string {
  switch (level) {
    case 'success': return 'text-green-400'
    case 'error': return 'text-red-400'
    case 'warning': return 'text-yellow-400'
    default: return 'text-green-400'
  }
}

function getTestButtonClass(testId: string): string {
  const result = testResults.value.get(testId)
  if (!result) return 'border-gray-300'
  return result.success ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'
}

async function runTest(testId: string) {
  if (isRunning.value) return
  
  const testDef = testDefinitions[testId as keyof typeof testDefinitions]
  if (!testDef) {
    addLog('error', `Unknown test: ${testId}`)
    return
  }

  isRunning.value = true
  currentTest.value = testDef.name
  
  addLog('info', `🧪 Starting test: ${testDef.name}`)
  addLog('info', testDef.description)
  
  try {
    // Execute Lua script (simulated for now)
    processingStep.value = 'Executing Lua script...'
    addLog('info', `📝 Executing Lua script for ${testDef.name}`)
    await new Promise(resolve => setTimeout(resolve, 500))

    // Process with OCJS worker based on test type
    processingStep.value = 'Processing with OCJS worker...'

    let finalShapeId: string

    if (testId === 'basic-door-body') {
      // Just create door body (no operations)
      const doorResult = await sendMessage('createDoorBody', {
        width: 200,
        height: 150,
        thickness: 18
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)
      addLog('info', '📋 This test shows just the door body without any cuts')
      finalShapeId = doorResult.shapeId

    } else if (testId === 'basic-tool-brep') {
      // Create door body + simple tool BRep (at center for now)
      const doorResult = await sendMessage('createDoorBody', {
        width: 100,
        height: 100,
        thickness: 18
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)

      // Create simple tool BRep (this should work)
      processingStep.value = 'Creating tool BRep...'
      const toolResult = await sendMessage('createToolBRep', {
        tool: { name: '8MM', type: 'cylindrical', diameter: 8 },
        height: 25 // Make it taller than door thickness
      })
      addLog('success', `✅ Tool BRep created: ${toolResult.shapeId}`)

      // Perform sweep operation using the tool shape
      processingStep.value = 'Performing sweep operation...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: [toolResult.shapeId],
        operation: 'subtract'
      })
      addLog('success', `✅ Sweep operation completed: ${sweepResult.toolsProcessed} tools processed`)
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'basic-sweep') {
      // Create door body + simple tool cut
      const doorResult = await sendMessage('createDoorBody', {
        width: 150,
        height: 100,
        thickness: 18
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)

      // Create simple tool BRep
      processingStep.value = 'Creating tool BRep...'
      const toolResult = await sendMessage('createToolBRep', {
        tool: { name: '6MM', type: 'cylindrical', diameter: 6 },
        height: 25 // Through-hole depth
      })
      addLog('success', `✅ Tool BRep created: ${toolResult.shapeId}`)

      // Perform sweep operation
      processingStep.value = 'Performing sweep operation...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: [toolResult.shapeId],
        operation: 'subtract'
      })
      addLog('success', `✅ Sweep operation completed: ${sweepResult.toolsProcessed} tools processed`)
      finalShapeId = sweepResult.shapeId

    } else if (testId === 'multi-tool-sweep') {
      // Create door body + multiple positioned tool operations
      // FIXED: Use consistent millimeter units throughout
      const doorWidth = 200  // mm
      const doorHeight = 150 // mm
      const doorThickness = 20 // mm

      const doorResult = await sendMessage('createDoorBody', {
        width: doorWidth / 1000,  // Convert mm to meters for OCJS
        height: doorHeight / 1000, // Convert mm to meters for OCJS
        thickness: doorThickness / 1000 // Convert mm to meters for OCJS
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId} (${doorWidth}×${doorHeight}×${doorThickness}mm)`)

      // Create positioned tool shapes for different locations
      processingStep.value = 'Creating positioned tool shapes...'

      // Tool 1: 8MM cylindrical drilling operation - left side hole
      const tool1Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '8MM_DRILL', type: 'cylindrical', diameter: 8, shape: 'cylindrical' },
        commands: [{
          command_type: 'circle',
          x1: 60,
          y1: 40,
          radius: 0, // No radius = drilling operation
          layer_name: '8MM_DRILL' // Layer name determines operation
        }],
        depth: 10 / 1000, // 10mm depth
        isBottomFace: true, // Cut from bottom surface
        doorWidth: doorWidth / 1000, // Convert mm to meters
        doorHeight: doorHeight / 1000 // Convert mm to meters
      })
      addLog('success', `✅ 8MM cylindrical drill created at (60,40)mm`)

      // Tool 2: 6MM ballnose groove operation - right side circular groove
      const tool2Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '6MM_BALLNOSE', type: 'ballnose', diameter: 6, shape: 'ballnose', ballRadius: 3 },
        commands: [{
          command_type: 'circle',
          x1: 140,
          y1: 40,
          radius: 10, // 10mm radius for groove operation
          layer_name: 'K_6MM_GROOVE' // K_ prefix indicates groove operation
        }],
        depth: 3 / 1000, // 3mm depth for groove
        isBottomFace: true, // Cut from bottom surface
        doorWidth: doorWidth / 1000, // Convert mm to meters
        doorHeight: doorHeight / 1000 // Convert mm to meters
      })
      addLog('success', `✅ 6MM ballnose groove created at (140,40)mm with 10mm radius`)

      // Tool 3: 90° V-bit pocket operation - center circular pocket
      const tool3Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '90DEG_VBIT', type: 'conical', diameter: 12, shape: 'conical', tipAngle: 90, tipDiameter: 0.2 },
        commands: [{
          command_type: 'circle',
          x1: 100,
          y1: 100,
          radius: 1.5, // 15mm radius for pocket operation
          layer_name: 'CEP_VBIT_POCKET' // CEP prefix indicates pocket operation
        }],
        depth: 4 / 1000, // 4mm depth for pocket
        isBottomFace: true, // Cut from bottom surface
        doorWidth: doorWidth / 1000, // Convert mm to meters
        doorHeight: doorHeight / 1000 // Convert mm to meters
      })
      addLog('success', `✅ 90° V-bit pocket created at (100,100)mm with 15mm radius`)

      // Tool 4: 120° V-bit groove operation - additional test
      const tool4Result = await sendMessage('createPositionedToolShapes', {
        tool: { name: '120DEG_VBIT', type: 'conical', diameter: 10, shape: 'conical', tipAngle: 90, tipDiameter: 0.1 },
        commands: [{
          command_type: 'circle',
          x1: 50,
          y1: 120,
          radius: 8, // 8mm radius for groove operation
          layer_name: 'K_90DEG_GROOVE' // K_ prefix indicates groove operation
        }],
        depth: 2 / 1000, // 2mm depth for groove
        isBottomFace: true, // Cut from bottom surface
        doorWidth: doorWidth / 1000, // Convert mm to meters
        doorHeight: doorHeight / 1000 // Convert mm to meters
      })
      addLog('success', `✅ 120° V-bit groove created at (50,120)mm with 8mm radius`)

      // Collect all positioned tool shape IDs
      const allToolShapeIds = [
      //  ...tool1Result.shapeIds,
      //  ...tool2Result.shapeIds,
        ...tool3Result.shapeIds,
      //  ...tool4Result.shapeIds
      ]

      // Perform sweep operation with positioned tools
      processingStep.value = 'Performing multi-tool sweep operation...'
      const sweepResult = await sendMessage('performSweepOperation', {
        doorBodyShape: doorResult.shapeId,
        toolGeometries: allToolShapeIds,
        operation: 'subtract'
      })
      addLog('success', `✅ Multi-tool sweep completed: ${sweepResult.toolsProcessed} tools processed (cylindrical + ballnose + V-bits)`)
      addLog('info', '📋 This test demonstrates different tool types: cylindrical drill, ballnose groove, V-bit pocket & groove')
      finalShapeId = sweepResult.shapeId

    } else {
      // For other tests, create door body as default
      const doorResult = await sendMessage('createDoorBody', {
        width: 200,
        height: 150,
        thickness: 20
      })
      addLog('success', `✅ Door body created: ${doorResult.shapeId}`)
      finalShapeId = doorResult.shapeId
    }

    // Export the result to GLB for visualization
    processingStep.value = 'Exporting to GLB for visualization...'
    addLog('info', '📦 Exporting final 3D model to GLB format...')
    const glbData = await sendMessage('exportGLB', finalShapeId)
    addLog('info', `📊 GLB file size: ${(glbData.byteLength / 1024).toFixed(1)} KB`)

    // Load the 3D model
    processingStep.value = 'Loading 3D visualization...'
    await loadModel(glbData)
    
    const result = { success: true, message: 'Test completed successfully', timestamp: new Date().toLocaleTimeString() }
    testResults.value.set(testId, result)
    lastTestResult.value = result
    
    addLog('success', `✅ Test completed: ${testDef.name}`)
    
  } catch (error) {
    const result = { success: false, message: `Test failed: ${error}`, timestamp: new Date().toLocaleTimeString() }
    testResults.value.set(testId, result)
    lastTestResult.value = result
    
    addLog('error', `❌ Test failed: ${error}`)
  } finally {
    isRunning.value = false
    currentTest.value = ''
    processingStep.value = ''
  }
}

async function loadModel(glbData: ArrayBuffer) {
  if (!scene) return

  addLog('info', `Loading 3D model (${glbData.byteLength} bytes)...`)

  // Clean up previous model
  if (model) {
    scene.remove(model)
  }

  // Create blob URL
  const blob = new Blob([glbData], { type: 'model/gltf-binary' })
  const url = URL.createObjectURL(blob)

  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }
  modelUrl.value = url

  // Load with GLTFLoader
  const loader = new GLTFLoader()
  return new Promise((resolve, reject) => {
    loader.load(
      url,
      (gltf) => {
        model = gltf.scene
        scene!.add(model)

        // Get model statistics
        let meshCount = 0
        let vertexCount = 0
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            meshCount++
            if (child.geometry) {
              const positions = child.geometry.attributes.position
              if (positions) {
                vertexCount += positions.count
              }
            }
          }
        })

        // Center the model
        const box = new THREE.Box3().setFromObject(model)
        const center = box.getCenter(new THREE.Vector3())
        model.position.sub(center)

        // Scale the model to fit the view
        const size = box.getSize(new THREE.Vector3())
        const maxDim = Math.max(size.x, size.y, size.z)
        if (maxDim > 0) {
          const scale = 200 / maxDim // Scale to fit in 200 units (larger scale)
          model.scale.setScalar(scale)
          addLog('info', `🔍 Model scaled by factor: ${scale.toFixed(3)}`)
        }

        // Add a bright material to make it more visible
        model.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.material = new THREE.MeshLambertMaterial({
              color: 0x8B4513, // Brown color for wood
              wireframe: wireframeMode.value
            })
          }
        })

        addLog('success', `✅ 3D model loaded successfully`)
        addLog('info', `📊 Model stats: ${meshCount} meshes, ${vertexCount} vertices`)
        addLog('info', `📏 Model size: ${size.x.toFixed(1)} x ${size.y.toFixed(1)} x ${size.z.toFixed(1)} units`)
        resolve(gltf)
      },
      (progress) => {
        // Loading progress
        const percent = (progress.loaded / progress.total) * 100
        addLog('info', `Loading progress: ${percent.toFixed(1)}%`)
      },
      (error) => {
        addLog('error', `❌ Failed to load 3D model: ${error}`)
        reject(error)
      }
    )
  })
}

function clearResults() {
  consoleOutput.value = []
  testResults.value.clear()
  lastTestResult.value = null
  
  if (model && scene) {
    scene.remove(model)
    model = null
  }
  
  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
    modelUrl.value = null
  }
}

async function runAllTests() {
  const testIds = Object.keys(testDefinitions)
  
  for (const testId of testIds) {
    if (!isRunning.value) break
    await runTest(testId)
    await new Promise(resolve => setTimeout(resolve, 1000)) // Pause between tests
  }
}

function toggleWireframe() {
  if (!model) return

  model.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      if (child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach(mat => {
            mat.wireframe = wireframeMode.value
          })
        } else {
          child.material.wireframe = wireframeMode.value
        }
      }
    }
  })

  addLog('info', `🔧 Wireframe mode: ${wireframeMode.value ? 'ON' : 'OFF'}`)
}

function resetCamera() {
  if (!camera || !controls) return

  // Reset camera position
  camera.position.set(150, 150, 150)
  camera.lookAt(0, 0, 0)

  // Reset controls
  controls.reset()

  addLog('info', '📷 Camera view reset')
}

function toggleDebugMarkers() {
  if (!scene) return

  if (debugMode.value) {
    // Add debug markers at tool positions
    addDebugMarkers()
  } else {
    // Remove debug markers
    clearDebugMarkers()
  }

  addLog('info', `🔍 Debug markers: ${debugMode.value ? 'ON' : 'OFF'}`)
}

function addDebugMarkers() {
  if (!scene) return

  // Clear existing markers
  clearDebugMarkers()

  // Tool positions from the console logs (converted to Three.js coordinates)
  const toolPositions = [
    { x: -0.04, y: 0.0125, z: -0.035, color: 0xff0000, size: 20, name: '8MM' }, // Red for 8MM - LARGE
    { x: 0.04, y: 0.0125, z: -0.035, color: 0x00ff00, size: 20, name: '6MM' },  // Green for 6MM - LARGE
    { x: 0.0, y: 0.0125, z: 0.025, color: 0x0000ff, size: 20, name: '4MM' },    // Blue for 4MM - LARGE
    // Add some reference markers that should definitely be visible
    { x: 0, y: 50, z: 0, color: 0xffff00, size: 30, name: 'ABOVE' },           // Yellow above door
    { x: 50, y: 0, z: 0, color: 0xff00ff, size: 30, name: 'RIGHT' },           // Magenta to the right
    { x: 0, y: 0, z: 50, color: 0x00ffff, size: 30, name: 'FRONT' }            // Cyan in front
  ]

  toolPositions.forEach((pos, index) => {
    // Create sphere geometry for the marker - MUCH LARGER
    const geometry = new THREE.SphereGeometry(pos.size, 16, 16) // Use size directly (no conversion)
    const material = new THREE.MeshBasicMaterial({
      color: pos.color,
      transparent: false, // Make them solid
      opacity: 1.0
    })
    const sphere = new THREE.Mesh(geometry, material)

    // Position the marker
    sphere.position.set(pos.x, pos.y, pos.z)

    // Add to scene
    scene.add(sphere)
    debugMarkers.push(sphere)

    const colorName = pos.color === 0xff0000 ? 'RED' :
                      pos.color === 0x00ff00 ? 'GREEN' :
                      pos.color === 0x0000ff ? 'BLUE' :
                      pos.color === 0xffff00 ? 'YELLOW' :
                      pos.color === 0xff00ff ? 'MAGENTA' :
                      pos.color === 0x00ffff ? 'CYAN' : 'UNKNOWN'
    addLog('info', `🎯 Debug marker ${index + 1}: ${colorName} ${pos.name} at (${pos.x}, ${pos.y}, ${pos.z})`)
  })

  addLog('success', `✅ Added ${debugMarkers.length} debug markers`)
}

function clearDebugMarkers() {
  if (!scene) return

  debugMarkers.forEach(marker => {
    scene!.remove(marker)
  })
  debugMarkers = []
}

function cleanup() {
  if (worker) {
    worker.terminate()
  }

  if (renderer && threeContainer.value) {
    threeContainer.value.removeChild(renderer.domElement)
    renderer.dispose()
  }

  if (modelUrl.value) {
    URL.revokeObjectURL(modelUrl.value)
  }
}
</script>

<style scoped>
.test-button {
  transition: all 0.2s ease;
}

.test-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-category {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.test-category:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
</style>
