{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV*\t\t*aV* Derece V Bicak \nH_Freze*cT*mm_Ic\t*cT*mm Freze Bicagi \nH_Freze*cW*mm_Ic_SF\t*cW*mm Freze Bicagi \nK_Freze*cW*mm_SF\t*cW*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 140, "description": "minimum dimension limit on the x axis", "parameterName": "xMin"}, {"defaultValue": 140, "description": "minimum dimension limit on the y axis", "parameterName": "yMin"}, {"defaultValue": 10, "description": "mm, glass margin on the back side", "parameterName": "g"}, {"defaultValue": 40, "description": "lath thickness", "parameterName": "a"}, {"defaultValue": 50, "description": "offset from the edge on the x axis", "parameterName": "mX"}, {"defaultValue": 50, "description": "offset from the edge on the y axis", "parameterName": "mY"}, {"defaultValue": 120, "description": "mm, center angle ofthe v-shaped tool", "parameterName": "vToolAngle"}, {"defaultValue": 50, "description": "mm, diameter of the tool (for security checks)", "parameterName": "vToolDiameter"}, {"defaultValue": 8, "description": "mm, depth at which the sunken frame will be made", "parameterName": "sunkenDepth"}, {"defaultValue": 5, "description": "Cam yeri kesim bicak capi", "parameterName": "cT"}, {"defaultValue": 20, "description": "Cam yeri tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 1, "description": "sharp corner", "parameterName": "<PERSON><PERSON><PERSON><PERSON>"}, {"defaultValue": 6, "description": "mm, window depth from bottom", "parameterName": "windowDepthBack"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}