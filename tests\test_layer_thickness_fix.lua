-- Simple test script to verify layer and thickness fixes

function modelMain()
    print("=== TESTING LAYER AND THICKNESS FIXES ===")

    -- Initialize ADekoLib
    G = ADekoLib

    -- Test basic layer and thickness setting
    print("Setting layer: H_Freze20mm_Ic_SF")
    G.setLayer("H_Freze20mm_Ic_SF")

    print("Setting thickness: -6")
    G.setThickness(-6)

    print("Creating rectangle - should be dark red with thickness -6")
    G.rectangle({50, 50}, {200, 150})

    print("Setting layer: K_AciliV45")
    G.setLayer("K_AciliV45")

    print("Setting thickness: -3")
    G.setThickness(-3)

    print("Creating line - should be blue with thickness -3")
    G.line({50, 200}, {250, 200})

    print("=== TEST COMPLETED ===")
    print("Check console for any 'Invalid name' errors")
    print("Check visualization for colors and layer info")
end

-- Execute the test
modelMain()
