-- Test script to see Rust compiled model/path results
-- This will generate various types of output to see what comes back from Rust

function modelMain()
    print("=== STARTING MODEL EXECUTION ===")
    print("Testing Rust output compilation...")
    
    -- Generate some basic drawing commands
    print("Creating basic shapes...")
    line(0, 0, 100, 100)
    rect(10, 10, 50, 30)
    crcl(75, 75, 25)
    
    -- Test text output
    print("Model dimensions: 200x150")
    print("Total shapes created: 3")
    
    -- Test error handling
    printError("This is a test error message")
    
    -- Generate some path data (if using new engine)
    print("Generating path data...")
    
    -- Test makerjs engine output (this should be captured by Rust)
    local makerjs_output = {
        models = {},
        paths = {
            line1 = {
                type = "line",
                origin = {0, 0},
                end_point = {100, 100}
            },
            rect1 = {
                type = "rectangle", 
                origin = {10, 10},
                width = 50,
                height = 30
            }
        }
    }
    
    -- Convert to JSON-like string (this should be auto-captured by Rust)
    local json_output = '{"models":{},"paths":{"line1":{"type":"line","origin":[0,0],"end":[100,100]},"rect1":{"type":"rectangle","origin":[10,10],"width":50,"height":30}}}'
    print(json_output)
    
    print("=== MODEL EXECUTION COMPLETED ===")
    print("Check Rust console and frontend console for compiled results!")
end

-- Execute the model
modelMain()
