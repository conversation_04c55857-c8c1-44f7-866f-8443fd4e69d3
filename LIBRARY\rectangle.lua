function modelMain()
  a = 60
  aa = 30

  X = X or 300
  Y = Y or 200

  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  local function createRectangle (face, thickness, optype, tooltype, radorang, p1, p2, bulge)
    G.setFace(face)
    G.setThickness(thickness)
    <PERSON><PERSON>set<PERSON>("LAYERANG")
    G.rectangle(p1, p2, bulge)
    return true
  end

  createRectangle("top", 18 , "","",9, {0,0}, {100,100}, 20)
  return true
end

require "ADekoDebugMode"