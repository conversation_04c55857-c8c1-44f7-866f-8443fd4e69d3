# Sweep Operation Test Plan

This document outlines comprehensive testing procedures for the sweep operation functionality in the CAD/CAM application.

## Overview

The sweep operation is a core 3D modeling feature that:
1. Creates 3D tool geometries (BReps) using OpenCascade.js
2. Performs boolean operations (subtract, union, intersect) between tools and workpiece
3. Generates realistic material removal simulation for CNC operations

## Test Files Created

### 1. Lua Test Scripts
- `sweep_test_simple.lua` - Basic sweep operation test
- `sweep_test_comprehensive.lua` - Complete tool type coverage
- `test_sweep_ocjs.lua` - OCJS integration test
- `test_sweep_operation.lua` - Engine-level sweep testing

### 2. Worker Test
- `test_ocjs_worker.html` - Standalone OCJS worker testing

## Testing Procedures

### Phase 1: Basic Functionality Test

1. **Load Simple Test Script**
   ```
   File: sweep_test_simple.lua
   Purpose: Test basic sweep operation with minimal geometry
   ```

2. **Expected Results**
   - Door panel created (100x80x18mm)
   - Tools detected: 8MM, 6MM, 4MM, 3MM
   - 3D visualization shows material removal
   - <PERSON>sol<PERSON> shows OCJS worker messages

3. **Verification Steps**
   - Check 3D visualization for door body
   - Verify tool detection in console
   - Confirm sweep operations complete without errors
   - Validate final 3D model shows cuts and holes

### Phase 2: Comprehensive Tool Testing

1. **Load Comprehensive Test Script**
   ```
   File: sweep_test_comprehensive.lua
   Purpose: Test all tool types and operations
   ```

2. **Tool Types Tested**
   - Cylindrical: 12MM, 8MM, 6MM, 4MM, 2MM, 10MM, 5MM
   - Conical: V90, V120
   - Ballnose: BALL6MM, BALL4MM
   - Drilling: DRILL8MM, DRILL5MM, DRILL3MM
   - Edge: EDGE10MM
   - Chamfer: CHAMFER

3. **Operation Types Tested**
   - Rectangular pockets
   - Circular pockets
   - Line cuts
   - V-groove cuts
   - Decorative borders
   - Drilling holes
   - Edge profiling
   - Chamfer operations
   - Overlapping cuts (boolean operations)
   - Complex polyline shapes
   - Arc operations

### Phase 3: OCJS Worker Direct Testing

1. **Open Worker Test Page**
   ```
   File: test_ocjs_worker.html
   Purpose: Direct OCJS worker testing
   ```

2. **Test Sequence**
   - Initialize OCJS Worker
   - Create Door Body
   - Create Tool BRep
   - Create Multiple Tool BReps
   - Perform Sweep Operation
   - Export to GLB

3. **Expected Worker Operations**
   - Worker initialization without errors
   - Door body creation (BRepPrimAPI_MakeBox)
   - Tool BRep generation for all tool types
   - Boolean operations (BRepAlgoAPI_Cut, BRepAlgoAPI_Fuse)
   - GLB export functionality

## Key Components Tested

### 1. Door Body Creation
```typescript
// Creates base workpiece geometry
createDoorBody(params: DoorBodyParams)
```

### 2. Tool BRep Generation
```typescript
// Creates 3D tool geometry for each tool type
createToolBRep(params: ToolBRepParams)
createAllToolBReps(params: AllToolBRepsParams)
```

### 3. Sweep Operations
```typescript
// Performs boolean operations between tools and workpiece
performSweepOperation(params: SweepOperationParams)
```

### 4. Export Functionality
```typescript
// Exports final geometry to GLB format
exportToGLB(shape: any)
createSimpleBoxGLB(doorParams: DoorBodyParams)
```

## Expected Console Output

### Successful Sweep Operation
```
🔧 OCJS Worker: Creating door body...
✅ Door body created: 200x150x18mm
🔧 Creating BRep for cylindrical tool: 8mm Endmill (⌀8mm)
✅ Tool BRep created successfully
🔧 Performing sweep operation: subtract
✅ Sweep operation completed
🔧 Exporting to GLB...
✅ GLB export successful: 1234 bytes
```

### Error Indicators
```
❌ Failed to initialize OpenCascade.js
❌ Error creating door body: [error details]
❌ Error creating tool BRep: [error details]
❌ Error performing sweep operation: [error details]
```

## Validation Criteria

### ✅ Pass Criteria
- [ ] OCJS worker initializes without errors
- [ ] Door body creates successfully
- [ ] All tool types generate BReps correctly
- [ ] Sweep operations complete without errors
- [ ] 3D visualization shows material removal
- [ ] GLB export produces valid files
- [ ] Console shows success messages
- [ ] No memory leaks or crashes

### ❌ Fail Criteria
- Worker initialization fails
- Door body creation errors
- Tool BRep generation fails
- Sweep operations throw exceptions
- 3D visualization shows no changes
- GLB export fails or produces corrupt files
- Console shows error messages
- Application crashes or becomes unresponsive

## Troubleshooting

### Common Issues
1. **OpenCascade.js not loading**: Check WASM file availability
2. **Worker initialization fails**: Verify worker file path
3. **Tool BRep creation errors**: Check tool parameter validation
4. **Sweep operation fails**: Verify geometry validity
5. **GLB export issues**: Check shape mesh generation

### Debug Steps
1. Check browser console for detailed error messages
2. Verify OCJS worker is properly initialized
3. Test with simpler geometry first
4. Validate tool parameters before BRep creation
5. Check memory usage during operations

## Performance Expectations

### Typical Operation Times
- Door body creation: < 100ms
- Single tool BRep: < 50ms
- Multiple tool BReps: < 500ms
- Sweep operation: < 1000ms
- GLB export: < 200ms

### Memory Usage
- Base worker: ~50MB
- Per tool BRep: ~5-10MB
- Door body: ~10-20MB
- Final result: ~100-200MB

## Next Steps

After successful testing:
1. Integrate sweep operations into main application workflow
2. Add progress indicators for long operations
3. Implement caching for frequently used tool BReps
4. Add error recovery mechanisms
5. Optimize performance for complex geometries
