-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  G = ADekoLib
   
  xMin = 140
  yMin = 140
  xLimit = 250
  yLimit = 500
  
  a = 50
  aa = 30
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 10
    
  windowDepthFront = 14
  windowDepthBack = 6
  
  extEdgeVtoolExist       	= 5   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Ka<PERSON>k kose Radusu var mi? derinlik/0:yok
      
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    
  local ust = a

	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
	  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if X < xMin or Y < yMin then
    print("Part finalDepthimension too small")
    return true
  end
  
  local bulge = G.bulge({X-a, Y-1.5*ust, 0, 0}, {X/2, Y-ust}, {a, Y-1.5*ust})
  local point = {{X-a, Y-1.5*ust, 0, bulge},{a, Y-1.5*ust, 0, 0},{a, ust},{X-a, ust},{X-a, Y-1.5*ust, 0, bulge}}
  -- local bulge = G.bulge({X-a*1.5, Y-2.5*a, 0, 0}, {X/2, Y-a}, {a*1.5, Y-2.5*a})
  -- local point = {{X-a*1.5, Y-2.5*a, 0, bulge},{a*1.5, Y-2.5*a, 0, 0},{a*1.5, a},{X-a*1.5, a},{X-a*1.5, Y-2.5*a, 0, bulge}}
  
    
  G.setLayer("H_Freze".. cT .. "mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(point)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze".. cW .."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(point, cW/2))
  
  G.setLayer("K_Freze".. cW .."mm_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(point)
  
  return true
end

------------------------------------------------
require "ADekoDebugMode"