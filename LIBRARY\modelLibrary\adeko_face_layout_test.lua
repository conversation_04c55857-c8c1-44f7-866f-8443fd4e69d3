-- ADekoLib Face Layout Test Script
-- This script tests the ADekoLib face layout system with proper coordinate positioning

function modelMain()
    print("=== ADekoLib Face Layout Test ===")
    
    -- Initialize ADekoLib
    G = ADekoLib
    
    -- Set up the working face
    G.setFace("top")
    G.setThickness(-materialThickness)
    
    -- Create a simple rectangular part outline
    G.makePartShape()
    
    -- Add some basic geometry to demonstrate the face layout
    -- Create a simple rectangle in the center of the top face
    local centerX = X / 2
    local centerY = Y / 2
    local rectWidth = 100
    local rectHeight = 80
    
    G.setThickness(-5) -- 5mm deep cut
    G.rectangle(
        {centerX - rectWidth/2, centerY - rectHeight/2}, 
        {centerX + rectWidth/2, centerY + rectHeight/2}
    )
    
    -- Add a circle on the left face
    G.setFace("left")
    G.circle({Y/2, materialThickness/2}, 20)
    
    -- Add text labels for each face
    text("Top Face", 0, centerX, centerY + 150)
    
    print("ADekoLib face layout test completed!")
end
