-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script Yatay cızgılı
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  
	G = ADekoLib
	
		
	a = 0
	sag, sol= 50, 50
	ad = 2 -- toolDepth kanal derinliği
	ara = 20
	cT = 8
	cW = 20
	h = 25
	aV = 160
	ust, alt, howMany , gaps =0 ,0 , 0, 100       --cond 1    --ust, alt, howMany , gaps =100,10, 3, 100--cond 2--ust, alt, howMany , gaps =0, 0, 0, 100--cond 3 
                                                                              --ust, alt,howMany,gaps =150,150, 0, 75--cond 4

  sideGrooveExist = 2    --Dik kanallar var mı? (Var: Derinlik / Yok: 0)
  dt			= 8 --kanal islemi bicak capi
  GrvBorForV	= 0		--Di<PERSON> kanallar Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)
  
  cabCoreExist            = 1 -- <PERSON><PERSON> var mı Var:1 Yok:0 --gobek yoksa ortayı tarama yap
  shapeToolExist         = 5 --Gobekte Desen bıcak var mı derinlik/0:yok
  
  extEdgeToolExist 	= 0  --Dış kenar Pah işlemi var mı? (Var: Derinlik / Yok: 0)
  extEdgeVorR 		= 0  --Dış kenar Pah işlemi V mi? Raduslu mu? (V:0/R:1)
  edgeCornerRExist 	= 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
  windowOpExist 	= 1 -- Duz mu, cam mi (duz: 1 / Cam: 0)
   
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
  
  if ara/2<alt then
    alt = alt - ara/2
  end

	local xMin 	= 140
	local yMin 	= 140
	
	if a > 0 or a < 0 then
		sag = a
		sol = a
	end
  local calcHeigth = 0
  local calcNum = 0
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
  
  if howMany == 0 and gaps == 0 then 
    return false
  end
  
  function HowMany(calcNum,calcNumC)      -- düzeltme işlemleri için gerekli
      if (calcNum-calcNumC) >= 0.5 then
        calcNum = calcNumC+1
      else
        calcNum = calcNumC
      end
    return calcNum
  end
  
  function rectToPoly (c1,c2)             -- gobekteki islemler icin gerekli
    points = { 
              {c1[1],c1[2]},
              {c2[1],c1[2]},
              {c2[1],c2[2]},
              {c1[1],c2[2]},
              {c1[1],c1[2]}
            }
    return points
  end
  
  function CabCoreOps (corner1,corner2)   --- bolumlerin içindeki işlemler buradan yapılıyor
    points = rectToPoly (corner1,corner2)
    if cabCoreExist>0 and h>0 then
      
      if h<cW and h>cT then
        cW = cT
      end
	  
      if h > cT then
      --if X>xLimit and Y>yLimit then
        G.setLayer("Cep_Acma" .. cW .. "mm")  -- DEEP cleanup
        G.setThickness(-ad)
        G.polylineimp (points)

        if h>cW then
          G.polylineimp (G.offSet(points, -h))
        end
        
        G.setLayer("K_TarKoseTemizl"..cT.."mm")            --kose temizleme
        G.setThickness(-ad)
        if h >= 3*cT then
          G.setThickness(-ad)
          G.cleanCorners(corner1,corner2,0,cT)
        else
          G.polylineimp(G.offSet(points, -cT/2))
        end
        
        if shapeToolExist  > 0 then  ----Göbekte desen bıçağı varsa
          G.setLayer("K_Desen")  -- DEEP cleanup
          G.setThickness(-shapeToolExist)
          G.polylineimp (G.offSet(points, -h))
        else 
          G.setLayer("K_AciliV" .. aV)  --- Gobek
          G.setThickness(-ad)
          G.polylineimp (G.offSet(points, -h))

        end
      end
    elseif h==0 and cabCoreExist>0 then         --h yoksa, gobekte tarama yoksa
    else 
      G.setLayer("Cep_Acma")	
      G.setThickness(-ad)
      G.polylineimp (points)
      G.setLayer("K_Freze"..cT.."mm")            --kose temizleme
      G.setThickness(-ad)
      G.cleanCorners(corner1,corner2,0,cT)
    end     
  end
  
  local hangisi = "cond1-2"
  
  if howMany >0 then                               --howmany değeri girilmişse
    if ust <= 0 and alt <=0 then                     --alt ust mesafe sıfır ise alt ve üsttede eşit aralık olmalı
      calcHeigth = (Y-howMany*ara)/(howMany+2)
      alt = calcHeigth
      ust = calcHeigth
      --print("cond1, alt=0, ust=0 howMany="..howMany..", bulunan gaps="..calcHeigth)
    else                                           --alt ust mesafe girilmişse girtildiği kadar olmalı
      calcHeigth = (Y-alt-ust-howMany*ara)/(howMany)
       --print("cond2, alt="..alt.." , ust="..ust..", howMany="..howMany..", bulunan gaps="..calcHeigth)
    end
  else                                            --howmany değeri girilmemişse
    if ust <= 0 and alt <= 0 then                  --alt ust mesafe sıfır ise alt ve üsttede eşit aralık olmalı
      calcNum = (Y-2*ara)/(ara+gaps)
      calcNumC = math.floor(calcNum)
      howMany = HowMany(calcNum,calcNumC)
      calcHeigth = (Y+2*ara-howMany*ara)/(howMany)
      alt = calcHeigth
      ust = calcHeigth
      --print("cond3, alt=0, ust=0 howMany="..howMany..", bulunan gaps="..calcHeigth)
      hangisi = "cond3"
    else                                           --alt ust mesafe girilmişse girtildiği kadar olmalı
      calcNum = (Y-ust-alt)/(ara+gaps)
      calcNumC = math.floor(calcNum)
      howMany = HowMany(calcNum,calcNumC)
      calcHeigth = (Y-alt-ust-howMany*ara)/(howMany)
      --print("cond4, alt="..alt.." , ust="..ust..", bulunan howMany="..howMany..", bulunan gaps="..calcHeigth)
      hangisi = "cond4"
    end
  end
  
	bulge = math.tan(math.pi/8)
	local D = edgeCornerRExist
	local B = math.tan(math.pi/8)
	
	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist > 0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	
	if extEdgeToolExist  > 0 then             --dış kenarlarda pah var mı?
		if extEdgeVorR == 0 then --  (V:0/R:1)
			G.setLayer("K_AciliV_Pah")	
		elseif extEdgeVorR ==1 then
			G.setLayer("H_Raduslu_Pah_" .. "DIS")	
		end
		G.setThickness(-extEdgeToolExist)
		G.rectangle({0,0},{X,Y})
	end

  if GrvBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
		G.setLayer("K_Ballnose"..dT.."mm")
	elseif GrvBorForV == 1 then
		G.setLayer("K_Freze"..dT.."mm")  
	elseif GrvBorForV == 2 then
		G.setLayer("K_AciliV"..aV)  
	end	
    
  if sideGrooveExist>0 then     --yan dik kanallar var mı
    G.setThickness(-sideGrooveExist)
    point1 = {sol,0}
    point12 = {sol,Y}
    point2 = {X-sag,0}
    point22 = {X-sag,Y}
    
    if sol == 0 and sag >0 then
      G.line(point2,point22,0)
    elseif sag == 0 and sol >0 then	
      G.line(point1,point12,0)
    elseif sol == 0 and sag == 0 then
      -- yan kanal yok
    elseif sol >0 and sag >0 then
      G.line(point1,point12,0)
      G.line(point2,point22,0)
    end
  end

  if hangisi == "cond1-2" then                          --alt ust kenar mesafeleri ve howMany, gaps değerlerine göre
    for i=0,howMany-1 do
      local y1 = alt+ara/2+i*(calcHeigth+ara)
      local y2 = alt-ara/2+(i+1)*(calcHeigth+ara)
      point1 = {sol,y1}
      point2 = {X-sag,y2}
      G.setLayer("K_AciliV"..aV)	
      G.setThickness(0)
      local corner1, corner2 = G.sunkenFrame(point1, point2, ad, aV, 50)
      if windowOpExist>0 then                           -- cam varsa 0 yoksa 1
        CabCoreOps (corner1,corner2)                    --gobek islmeleri
      else
        G.setLayer("H_Freze_Ebatlama_ic")	
        G.setThickness(-materialThickness)
        G.rectangle(corner1,corner2)
      end
    end
  elseif hangisi == "cond3" then 
    for i=0,howMany-3 do
      local y1 = alt+ara/2+i*(calcHeigth+ara)
      local y2 = alt-ara/2+(i+1)*(calcHeigth+ara)
      point1 = {sol,y1}
      point2 = {X-sag,y2}
      G.setLayer("K_AciliV"..aV)	
      G.setThickness(0)
      local corner1, corner2 = G.sunkenFrame(point1, point2, ad, aV, 50)
      if windowOpExist>0 then                           -- cam varsa 0 yoksa 1
        CabCoreOps (corner1,corner2)
      else
        G.setLayer("H_Freze_Ebatlama_ic")	
        G.setThickness(-materialThickness)
        G.rectangle(corner1,corner2)
      end
    end
  elseif hangisi == "cond4" then
    for i=0,howMany-1 do
      local y1 = alt+ara/2+i*(calcHeigth+ara)
      local y2 = alt-ara/2+(i+1)*(calcHeigth+ara)
      point1 = {sol,y1}
      point2 = {X-sag,y2}
      G.setLayer("K_AciliV"..aV)	                            --ic kenar işlemi
      G.setThickness(0)
      local corner1, corner2 = G.sunkenFrame(point1, point2, ad, aV, 50)
      if windowOpExist>0 then                                -- cam varsa 0 yoksa 1                  
        CabCoreOps (corner1,corner2)                  
      else
        G.setLayer("H_Freze_Ebatlama_ic")	
        G.setThickness(-materialThickness)
        G.rectangle(corner1,corner2)
      end
    end
  end 
  
  return true
end  
  
require "ADekoDebugMode"