-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
	minimum = 150
	limit = 350
	
  a = 50                    -- Kenardan Vbite
  aa = 20					-- Dar kapak için Kenardan mesafe
  b = 10                    -- Vbitten Dıs kanala
  d = 10             		-- Göbekten iç kanala
  ad = 6                    -- Vbit derinliği (sunkenDepth)
  aV = 120                  -- Vbit Açısı
  cT = 6                    -- <PERSON>nce bıçak çapı (Köşe Temizleme)
    
  extEdgeRtoolExist      		= 2   -- Dı<PERSON> kenar Pah işlemi var mı? derinlik/0:yok
  extGrooveExist                = 3   -- D<PERSON><PERSON> kanal var mı? derinlik/ 0:yok
  intGrooveExist          		= 2   -- iç kanal var mı? derinlik/ 0:yok
  edgeCornerRExist          	= 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  --cR = 5		  -- Radus Veren form bıcak düz uç dar çapı
	  --bd = 3                    -- dış kanal derinliği
  --local dd = 2              -- iç kanal derinliği
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
 
  local D = edgeCornerRExist
  
  local sunkenDepth = ad
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
  
  kontrolListe = {a,h,d,ad,aV} 
  for i, val in ipairs(kontrolListe) do
    if val <= 0 then
      print("Yanlıs Deger Girilmis")
      return true
    end
  end
    
  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  minXCalculated = 2*a + 2*sunkenWidth
  minYCalculated = 2*ust + 2*sunkenWidth
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if X<minXCalculated or Y<minYCalculated then
    print("Part dimension too small")
    return true
  end
  
  
  G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  if extEdgeRtoolExist > 0 then
  	G.setLayer("H_Raduslu_Pah_" .."DIS")  -- 
  	G.setThickness(-extEdgeRtoolExist)
  	distance = 0
  	point1 = {0-distance, 0-distance}
  	point2 = {X+distance, Y+distance}
  	G.rectangle(point1,point2)
  end
  
 if extGrooveExist > 0 then
    G.setLayer("K_Kanal_".. cT .."mm") 
    G.setThickness(-extGrooveExist)
    distance = a-b
    distance2 = ust-b
    point1 = {distance, distance2}
    point2 = {X-distance, Y-distance2}
    G.rectangle(point1,point2)
  end
        
  if intGrooveExist > 0 then      ------İç kanal varsa
	local checkX = 2*a + sunkenWidth*2 + 2*d
	local checkY = 2*ust + sunkenWidth*2 + 2*d
	if X < checkX or Y < checkY then
      print("Part dimension too small, check a + sunkenWidth + d values")
      return true
    end
    G.setLayer("K_Kanal_".. cT .."mm") 
    G.setThickness(-intGrooveExist)
    distance = a + sunkenWidth*2 + d
    distance2 = ust + sunkenWidth*2 + d
    point1 = {distance, distance2}
    point2 = {X-distance, Y-distance2}
    G.rectangle(point1,point2)
  end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
