-- Test script to verify JSON generation and visualization pipeline
-- This script creates simple geometric shapes to test the makerjs_engine output

function modelMain()
    -- Initialize ADekoLib
    G = AdekoLib

    -- Set up basic parameters
    X = 200
    Y = 150
    materialThickness = 18

    -- Create a simple test pattern
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()

    -- Test Layer 1: Basic shapes
    G.setLayer("TEST_SHAPES")
    G.setThickness(-5)

    -- Rectangle
    G.rectangle({20, 20}, {80, 60})

    -- Line
    G.line({20, 80}, {180, 80})

    -- Test Layer 2: Complex shapes
    G<PERSON>setLayer("TEST_COMPLEX")
    G.setThickness(-10)

    -- <PERSON><PERSON>ine (triangle)
    G.polyline({100, 100}, {130, 120}, {100, 140}, {100, 100})

    print("✓ Test shapes created successfully")
    print("✓ Rectangle: (20,20) to (80,60)")
    print("✓ Line: (20,80) to (180,80)")
    print("✓ Triangle polyline: (100,100) -> (130,120) -> (100,140) -> (100,100)")

    return true
end

-- Load debug mode to execute
require "ADekoDebugMode"
