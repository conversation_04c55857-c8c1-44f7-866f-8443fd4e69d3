-- Example: Creating Complementary Arcs
-- This example shows how to create arcs and their complements to form complete circles

require("ADekoLib")

-- Initialize the engine
local engine = require("makerjs_engine")
ADekoLib.engine = engine

-- Create a simple model demonstrating complementary arcs
engine.model_def("complementary_arc_demo", function()
    
    print("Creating complementary arc demonstration...")
    
    -- Example 1: Quarter arc and its complement
    engine.layer("quarter_example")
    
    local center = {100, 100}
    local radius = 40
    
    -- Create a quarter arc (90 degrees)
    ADekoLib.arc(center, radius, 0, 90, false, "quarter_arc")
    
    -- Create the complementary arc (270 degrees) to complete the circle
    ADekoLib.complementaryArc(center, radius, 0, 90, false, "quarter_complement")
    
    -- Example 2: Using diameter-based function
    engine.layer("diameter_example")
    
    local center2 = {250, 100}
    local diameter = 60
    
    -- Create a 120-degree arc
    ADekoLib.circularArcTrue(center2, diameter, 30, 150, true, "main_arc")
    
    -- Create the complementary arc (240 degrees)
    ADekoLib.complementaryCircularArc(center2, diameter, 30, 150, false, "main_complement")
    
    print("Complementary arc demo completed!")
end)

-- Generate the model
local result = engine.generate_model("complementary_arc_demo")
if result then
    print("Demo model generated successfully!")
    print("You should see two complete circles formed by arc pairs.")
else
    print("ERROR: Failed to generate demo model")
end
