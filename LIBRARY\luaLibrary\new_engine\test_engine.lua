#!/usr/bin/env lua

--[[
Test script for the Lua Maker.js Engine
This script performs basic validation of the engine functionality
and generates sample JSON output for verification.

This test can be run with any Lua interpreter:
  lua test_engine.lua
  lua5.3 test_engine.lua
  luajit test_engine.lua
--]]

-- Load the engine
local engine = require('makerjs_engine')

-- Test counter
local tests_passed = 0
local tests_failed = 0

-- Helper function for testing
local function test(name, func)
    print("Testing: " .. name)
    local success, err = pcall(func)
    if success then
        print("  ✓ PASSED")
        tests_passed = tests_passed + 1
    else
        print("  ✗ FAILED: " .. tostring(err))
        tests_failed = tests_failed + 1
    end
end

-- Test basic API functions
test("Basic model creation", function()
    engine.model_def("test_model", function()
        engine.layer("test_layer")
        engine.rect("test_rect", 0, 0, 100, 200)
    end)
    assert(engine.get_current_model() == nil, "Current model should be nil after model_def")
end)

test("Shape creation functions", function()
    engine.model_def("shapes_test", function()
        engine.layer("shapes")
        
        -- Test all shape types
        engine.rect("rectangle", 0, 0, 100, 50)
        engine.circle("circle", 50, 25, 20)
        engine.line("line", 0, 0, 100, 100)
        engine.polyline("polyline", {{0, 0}, {50, 0}, {50, 50}, {0, 50}})
    end)
end)

test("Layer management", function()
    engine.model_def("layer_test", function()
        engine.layer("layer1")
        engine.rect("rect1", 0, 0, 10, 10)
        
        engine.layer("layer2")
        engine.circle("circle1", 5, 5, 3)
        
        engine.layer("layer1")  -- Switch back
        engine.rect("rect2", 20, 20, 10, 10)
    end)
end)

test("Transformation functions", function()
    engine.model_def("transform_test", function()
        engine.layer("base")
        engine.rect("original", 0, 0, 50, 50)
        
        -- Test copy and transform
        engine.copy_shape("original", "copy1")
        engine.translate("copy1", 100, 0)
        
        engine.copy_shape("original", "copy2")
        engine.rotate("copy2", 45, 25, 25)
        
        engine.copy_shape("original", "copy3")
        engine.scale("copy3", 2, 2, 0, 0)
    end)
end)

test("Utility functions", function()
    engine.model_def("utility_test", function()
        engine.layer("base")
        engine.rect("base_rect", 0, 0, 100, 100)
        engine.circle("base_circle", 150, 50, 30)
        
        -- Test offset
        engine.offset_path("base_rect", 5, "offset_rect")
        engine.offset_path("base_circle", -5, "offset_circle")
        
        -- Test shape info
        local info = engine.get_shape_info("base_rect")
        assert(info.type == "rect", "Shape info should return correct type")
        assert(info.area == 10000, "Rectangle area should be 10000")
        
        -- Test list shapes
        local shapes = engine.list_shapes("base")
        assert(#shapes >= 4, "Should have at least 4 shapes")
    end)
end)

test("JSON export", function()
    engine.model_def("export_test", function()
        engine.layer("cut")
        engine.rect("frame", 0, 0, 100, 200)
        engine.circle("hole", 50, 100, 10)
        
        engine.layer("engrave")
        engine.line("mark", 0, 0, 100, 200)
    end)
    
    local json = engine.export_model("export_test")
    assert(type(json) == "string", "Export should return a string")
    assert(#json > 50, "JSON should be substantial")
    
    -- Basic JSON structure validation
    assert(json:find('"models"'), "JSON should contain models")
    assert(json:find('"paths"'), "JSON should contain paths")
end)

test("Error handling", function()
    -- Test invalid parameters
    local function should_error(func)
        local success, err = pcall(func)
        assert(not success, "Function should have thrown an error")
    end
    
    should_error(function() engine.rect("", 0, 0, 10, 10) end)  -- Empty name
    should_error(function() engine.rect("test", "invalid", 0, 10, 10) end)  -- Invalid coordinate
    should_error(function() engine.circle("test", 0, 0, -5) end)  -- Negative radius
end)

test("3D sweep stubs", function()
    engine.model_def("sweep_test", function()
        engine.layer("profile")
        engine.rect("profile_shape", 0, 0, 10, 5)
        
        engine.define_profile("test_profile", "sweep_test")
        engine.define_3d_path("test_path", "line", {{0,0,0}, {10,0,0}, {10,10,0}})
        engine.define_sweep("test_sweep", "test_profile", "test_path")
    end)
end)

-- Generate sample JSON output
print("\n=== Generating Sample JSON Output ===")

engine.model_def("sample_output", function()
    engine.layer("cut")
    engine.rect("outer", 0, 0, 100, 100)
    engine.circle("hole", 50, 50, 20)
    
    engine.layer("engrave")
    engine.line("diagonal", 0, 0, 100, 100)
    engine.polyline("border", {{10, 10}, {90, 10}, {90, 90}, {10, 90}, {10, 10}})
end)

local sample_json = engine.export_model("sample_output")
print("Sample JSON output:")
print(sample_json)

-- Save to file for inspection
local file = io.open("test_output.json", "w")
if file then
    file:write(sample_json)
    file:close()
    print("\nSample output saved to test_output.json")
end

-- Test summary
print("\n=== Test Results ===")
print("Tests passed: " .. tests_passed)
print("Tests failed: " .. tests_failed)

if tests_failed == 0 then
    print("🎉 All tests passed!")
    os.exit(0)
else
    print("❌ Some tests failed!")
    os.exit(1)
end
