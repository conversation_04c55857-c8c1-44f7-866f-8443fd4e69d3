# Door Model Color Visualization System

## Overview

The enhanced door model visualization system provides comprehensive color representation and layer management for CNC door manufacturing. This system categorizes layers by operation type, surface (top/bottom), and provides visual distinction for different machining operations.

## Color Scheme

### Structural Elements
- **PANEL**: `#8B4513` (<PERSON><PERSON> Brown) - Door panel with filled background
- **LMM**: `#FF6B35` (Orange) - LMM structural layers

### Cutting Operations (Red Spectrum)
- **H_Freze**: `#FF4500` (Orange Red) - Top face contour cuts
- **H_Freze_SF**: `#DC143C` (Crimson) - Bottom face contour cuts
- **K_Freze**: `#FF6347` (Tomato) - Top face groove cuts
- **K_Freze_SF**: `#B22222` (Fire Brick) - Bottom face groove cuts

### V-Bit Operations (Purple Spectrum)
- **K_AciliV**: `#9932CC` (Dark Orchid) - V-bit groove operations
- **K_AciliV_Pah**: `#8A2BE2` (Blue Violet) - V-bit chamfer operations

### Ball Nose Operations (Blue Spectrum)
- **K_Ballnose**: `#4169E1` (Royal Blue) - Ball nose operations
- **K_Desen**: `#1E90FF` (Dodger Blue) - Decorative pattern operations

### Radial Operations (Green Spectrum)
- **H_Raduslu**: `#32CD32` (Lime Green) - Radial operations
- **H_Raduslu_Pah**: `#228B22` (Forest Green) - Radial chamfer operations

### Special Operations (Yellow/Orange Spectrum)
- **K_Panjur**: `#FFD700` (Gold) - Shutter operations
- **CLEANCORNERS**: `#FFA500` (Orange) - Corner cleanup
- **CLEANUP**: `#FF8C00` (Dark Orange) - General cleanup
- **DEEPEND**: `#FF7F50` (Coral) - Deep end operations
- **DEEPFRAME**: `#FF6347` (Tomato) - Deep frame operations
- **THINFRAME**: `#CD853F` (Peru) - Thin frame operations

### Drilling Operations (Cyan Spectrum)
- **DRILL**: `#00CED1` (Dark Turquoise) - Drilling operations
- **POCKET**: `#20B2AA` (Light Sea Green) - Pocket operations

### Numeric Tool Layers
- **Pattern**: `^\d+MM?$` - HSL color generation based on tool diameter
- **Formula**: `hsl((diameter * 25 + 180) % 360, 75%, 55%)`

## Layer Categories

### 1. Structural
- PANEL (door panel)
- LMM layers (structural elements)

### 2. Cutting
- H_Freze (contour cutting)
- K_Freze (groove cutting)

### 3. V-Bit
- K_AciliV (V-groove operations)

### 4. Ball Nose
- K_Ballnose (ball nose operations)
- K_Desen (decorative patterns)

### 5. Radial
- H_Raduslu (radial operations)

### 6. Special
- K_Panjur (shutter operations)
- CLEANCORNERS, CLEANUP, DEEPEND, DEEPFRAME, THINFRAME

### 7. Drilling
- DRILL, POCKET operations

### 8. Tool
- Numeric diameter layers (5MM, 10MM, etc.)

### 9. Other
- Unrecognized layer patterns

## Visual Enhancements

### Door Panel (PANEL Layer)
- **Filled Background**: Semi-transparent brown fill
- **Thick Border**: 2.5x normal line width
- **Transparency**: 40% opacity for background visibility

### Bottom Face Operations
- **Semi-Transparent**: 70% opacity
- **_SF Suffix Detection**: Automatic identification of bottom face layers

### Structural Elements
- **Enhanced Width**: 1.5x normal line width for LMM layers
- **Visual Prominence**: Better visibility for structural components

## Layer Panel Features

### Enhanced Information Display
- **Door Panel Badge**: Special indicator for PANEL layer
- **Face Badges**: Top/Bottom face indicators
- **Category Tags**: Color-coded category labels
- **Tool Information**: Detected tool details

### Sorting and Organization
1. **PANEL layer first** (always at top)
2. **By category** (structural → cutting → v-bit → etc.)
3. **By name** (alphabetical within category)

### Multi-language Support
- **English**: Full category and face labels
- **Turkish**: Translated category names and indicators

## Usage Examples

### Basic Door Model
```lua
-- Create door panel (automatically uses PANEL layer)
G.makePartShape()

-- Add cutting operations
G.setLayer("K_Freze10mm")
G.rectangle({50, 50}, {550, 150})

-- Add bottom face operations
G.setLayer("H_Freze20mm_SF")
G.rectangle({100, 200}, {500, 250})
```

### Testing Color Scheme
Use the provided `test_door_colors.lua` script to see all color categories and visual enhancements in action.

## Technical Implementation

### Color Detection
- Pattern matching for complex layer names
- Fallback to numeric tool diameter detection
- HSL color generation for consistent tool colors

### Layer Analysis
- Automatic categorization based on naming conventions
- Face detection (_SF suffix for bottom face)
- Tool information integration

### Visual Rendering
- Enhanced line widths for different layer types
- Transparency effects for face distinction
- Filled backgrounds for structural elements

## Benefits

1. **Improved Visual Clarity**: Different colors for different operation types
2. **Better Organization**: Categorized layer panel with sorting
3. **Face Distinction**: Clear separation of top/bottom operations
4. **Door Context**: Special handling for door panel visualization
5. **Multi-language Support**: Localized interface elements
6. **Tool Integration**: Automatic tool detection and display
