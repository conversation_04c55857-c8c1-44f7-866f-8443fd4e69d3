-- ADek<PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  marginX = 20
  marginY = 20
  structureSizeX = 30
  structureSizeY = 30
  h = 30

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local g = 8
  local notchWidth = 2
  local notchDepth = 2
  local firstDepth = 5 
  local finalDepth = 8
  local cMidDiameter = 10
  local cThinDiameter = 5
  local vMidAngle = 90
  local vMidDiameter = 40
  local vWideAngle = 120
  local vWideDiameter = 60
  local sunkenWidth1 = firstDepth*math.tan((math.pi*vWideAngle/180)/2.0)
  local sunkenWidth2 = finalDepth*math.tan((math.pi*vWideAngle/180)/2.0)
  
  if X<250 or Y<250 then
    print("Part dimension too small")
    return true
  end
  
  if h<cMidDiameter or h<cThinDiameter then
    print("Tool too large")
    return true
  end
  
  G.setLayer("K_AciliV45")		--Figure
  G.setThickness(-notchDepth)
  
  G.rectangle({marginX/2, marginY/2}, {X-marginX/2, Y-marginY/2})
  G.menderesA(marginX, marginY, structureSizeX, structureSizeY)
  G.rectangle({marginX+structureSizeX, marginY+structureSizeY}, {X-(marginX+structureSizeX), Y-(marginY+structureSizeY)})
  
  G.setLayer("K_AciliV30")		--Angled surfaces
  G.setThickness(0)
  
  distanceX = marginX+structureSizeX+g
  distanceY = marginY+structureSizeY+g
  point1 = {distanceX,distanceY}
  point2 = {X-distanceX,Y-distanceY}
  local corner1,corner2 = G.sunkenFrame(point1,point2,finalDepth,vWideAngle,vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setThickness(-firstDepth)
  distanceX = marginX+structureSizeX+g+sunkenWidth2+h
  distanceY = marginY+structureSizeY+g+sunkenWidth2+h
  G.rectangle({distanceX, distanceY}, {X-distanceX, Y-distanceY})
  
  G.setLayer("K_TarKoseTemizl".."5mm")		--Corner cleaning
  
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,finalDepth,cThinDiameter)
  
  G.setLayer("K_Freze10mm")		--Channel cleaning
  G.setThickness(-finalDepth)
  
  distanceX = marginX+structureSizeX+g+sunkenWidth2+cMidDiameter/2
  distanceY = marginY+structureSizeY+g+sunkenWidth2+cMidDiameter/2
  point1 = {distanceX, distanceY}
  point2 = {X-distanceX, Y-distanceY}
  G.rectangle(point1,point2)
  
  if h > cMidDiameter then
    distanceX = marginX+structureSizeX+g+sunkenWidth2+h-cMidDiameter/2
    distanceY = marginY+structureSizeY+g+sunkenWidth2+h-cMidDiameter/2
    point3 = {distanceX, distanceY}
    point4 = {X-distanceX, Y-distanceY}
    G.rectangle(point3,point4) 
  
    k = (h-cMidDiameter)/(cMidDiameter/2)
    
    for i=1, k, 1 do
      point1 = G.ptAdd(point1,{cMidDiameter/2,cMidDiameter/2})
      point2 = G.ptSubtract(point2, {cMidDiameter/2,cMidDiameter/2})
      if point1[1]>point3[1]-cMidDiameter/2 then
        break
      end
      G.rectangle(point1,point2)
    end
  end
  
  G.setThickness(-notchDepth)
  distanceX = marginX+structureSizeX+g+sunkenWidth2+h+sunkenWidth1+notchWidth-cMidDiameter/2
  distanceY = marginY+structureSizeY+g+sunkenWidth2+h+sunkenWidth1+notchWidth-cMidDiameter/2
  point1 = {distanceX,distanceY}
  point2 = {X-distanceX,Y-distanceY}
  G.rectangle(point1,point2)
  
  return true
end

require "ADekoDebugMode"
