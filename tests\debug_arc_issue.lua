-- ADekoCAM, Model Script - Fillet Corner Frame Test
-- Converted from C# azCAM macro: Fillet Corner Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

-- Use the new engine version of ADekoLib
package.path = package.path .. ";LIBRARY/luaLibrary/new_engine/?.lua"
require("ADekoLib")
local engine = require("makerjs_engine")
ADekoLib.engine = engine

------------------------------------------------
function modelMain()

  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18

  -- Fillet Corner Frame parameters (from original C# macro)
  A = 50    -- Corner radius/fillet size
  B = 100   -- Frame margin/inset
  Z = 5     -- Thickness/depth
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> radü<PERSON> yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  local debug = false
  
  if ((Y-2*b)/(X-2*a)<=1.5) then
    print ("Must have vertical aspect ratio.")
    return true
  end
  
  -- known parameters
  
  local point1 = {a, b}  
  local point2 = {a, Y-b}
  local point3 = {X-a, b}
  local point4 = {X-a, Y-b}
  local point5 = {a, Y/2}
  local point6 = {X-a, Y/2}
  local point7 = {X/2, Y-b}
  local point8 = {X/2, b}
  local point9 = {a+(X-2*a)/4, b+(Y-2*b)/2}
  local point10 = {a+3*(X-2*a)/4, b+(Y-2*b)/2}
  
  -- Calculate the unknown
  
  local bulge2 = G.bulge(point1, point10, point2)
  local radius = G.radius(point1, point2, bulge2)
  comment, intersection1, dummy = G.circleCircleIntersection(point2, radius, point1, radius)
  comment, dummy, intersection2 = G.circleCircleIntersection(point4, radius, point3, radius)
  comment, point12, point11 = G.circleCircleIntersection(intersection1, radius, intersection2, radius) 
  comment, dummy, intersection3 = G.circleLineIntersection(intersection1, radius, point5, point7) 
  local bulge3 = G.bulge(point2, intersection3, point11)
  comment, dummy, intersection4 = G.circleLineIntersection(intersection1, radius, point5, point8)
  local bulge4 = G.bulge(point12, intersection4, point1)
  
  -- Draw the final polylines

  G.setLayer("H_AciliV" .. aV .. "_Ic")
  G.setThickness(-windowDepthFront)

  bulge5 = G.bulge(point11, point10, point12)

  -- Create separate point copies for each polyline to avoid conflicts
  -- Left polyline: point1 -> point2 (with bulge3) -> point11 (with -bulge5) -> point12 (with bulge4) -> point1
  local left_p1 = {point1[1], point1[2], point1[3] or 0, 0}  -- no bulge from point1
  local left_p2 = {point2[1], point2[2], point2[3] or 0, bulge3}  -- bulge3 from point2 to point11
  local left_p11 = {point11[1], point11[2], point11[3] or 0, -bulge5}  -- -bulge5 from point11 to point12
  local left_p12 = {point12[1], point12[2], point12[3] or 0, bulge4}  -- bulge4 from point12 to point1
  G.polyline(left_p1, left_p2, left_p11, left_p12, left_p1)  -- left

  -- Right polyline: point4 -> point3 (with bulge4) -> point12 (with -bulge5) -> point11 (with bulge3) -> point4
  local right_p4 = {point4[1], point4[2], point4[3] or 0, 0}  -- no bulge from point4
  local right_p3 = {point3[1], point3[2], point3[3] or 0, bulge4}  -- bulge4 from point3 to point12
  local right_p12 = {point12[1], point12[2], point12[3] or 0, -bulge5}  -- -bulge5 from point12 to point11
  local right_p11 = {point11[1], point11[2], point11[3] or 0, bulge3}  -- bulge3 from point11 to point4
  G.polyline(right_p4, right_p3, right_p12, right_p11, right_p4)  -- right

  -- Top polyline: point2 -> point4 (with -bulge3) -> point11 (with -bulge3) -> point2
  local top_p2 = {point2[1], point2[2], point2[3] or 0, 0}  -- no bulge from point2
  local top_p4 = {point4[1], point4[2], point4[3] or 0, -bulge3}  -- -bulge3 from point4 to point11
  local top_p11 = {point11[1], point11[2], point11[3] or 0, -bulge3}  -- -bulge3 from point11 to point2
  G.polyline(top_p2, top_p4, top_p11, top_p2)  -- top

  -- Bottom polyline: point3 -> point1 (with -bulge4) -> point12 (with -bulge4) -> point3
  local bottom_p3 = {point3[1], point3[2], point3[3] or 0, 0}  -- no bulge from point3
  local bottom_p1 = {point1[1], point1[2], point1[3] or 0, -bulge4}  -- -bulge4 from point1 to point12
  local bottom_p12 = {point12[1], point12[2], point12[3] or 0, -bulge4}  -- -bulge4 from point12 to point3
  G.polyline(bottom_p3, bottom_p1, bottom_p12, bottom_p3)  -- bottom

  G.setLayer("H_AciliV" .. aV .. "_Dis")
  G.setThickness(-windowDepthFront)
  -- Middle arc: point11 -> point12 (with bulge5) -> point11
  local middle_p11 = {point11[1], point11[2], point11[3] or 0, bulge5}  -- bulge5 from point11 to point12
  local middle_p12 = {point12[1], point12[2], point12[3] or 0, 0}  -- no bulge from point12 back to point11
  G.polyline(middle_p11, middle_p12, middle_p11)  -- middle
  
  
  if (debug) then
    G.circle(intersection1, radius)
    G.circle(intersection2, radius)
    G.polyline(point5, point7, point6, point8, point5, point6)
    G.line(point11, point2, -bulge3)
    G.line(point11, point12, bulge5)  -- Use bulge5 instead of bulge2
    G.line(point12, point1, bulge4)
  end
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-cW/2, b-cW/2}, {X-a+cW/2, Y-b+cW/2})
  
  G.setLayer("K_Freze_" .. cW .. "mm_SF")
  G.line(point1, point2, bulge2)
  G.line(point4, point3, bulge2)
  
  return true
end

-- Using new engine version, no need for ADekoDebugMode
