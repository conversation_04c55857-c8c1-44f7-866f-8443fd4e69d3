-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  a = 50
  b = 30
  windowDepthFront = 12
  windowDepthBack = 8
  cW = 20 
  aV = 90 
    
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 5   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kö<PERSON>e radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  local debug = false
  
  if ((Y-2*b)/(X-2*a)<=1.5) then
    print ("Must have vertical aspect ratio.")
    return true
  end
  
  -- known parameters
  
  local point1 = {a, b}  
  local point2 = {a, Y-b}
  local point3 = {X-a, b}
  local point4 = {X-a, Y-b}
  local point5 = {a, Y/2}
  local point6 = {X-a, Y/2}
  local point7 = {X/2, Y-b}
  local point8 = {X/2, b}
  local point9 = {a+(X-2*a)/4, b+(Y-2*b)/2}
  local point10 = {a+3*(X-2*a)/4, b+(Y-2*b)/2}
  
  -- Calculate the unknown
  
  local bulge2 = G.bulge(point1, point10, point2)
  local radius = G.radius(point1, point2, bulge2)
  comment, intersection1, dummy = G.circleCircleIntersection(point2, radius, point1, radius)
  comment, dummy, intersection2 = G.circleCircleIntersection(point4, radius, point3, radius)
  comment, point12, point11 = G.circleCircleIntersection(intersection1, radius, intersection2, radius) 
  comment, dummy, intersection3 = G.circleLineIntersection(intersection1, radius, point5, point7) 
  local bulge3 = G.bulge(point2, intersection3, point11)
  comment, dummy, intersection4 = G.circleLineIntersection(intersection1, radius, point5, point8)
  local bulge4 = G.bulge(point12, intersection4, point1)
  
  -- Draw the final polylines
  
  G.setLayer("H_AciliV" .. aV .. "_Ic")
  G.setThickness(-windowDepthFront)
  
  bulge5 = G.bulge(point11, point10, point12)
  point2[4] = bulge3
  point11[4] = -bulge5
  point12[4] = bulge4
  G.polyline(point1, point2, point11, point12, point1)  -- left
  
  point3[4] = bulge4
  point12[4] = -bulge5
  point11[4] = bulge3
  G.polyline(point4, point3, point12, point11, point4)  -- right
  
  point2[4] = 0
  point4[4] = -bulge3
  point11[4] = -bulge3
  G.polyline(point2, point4, point11, point2)  -- top
  
  point3[4] = 0
  point1[4] = -bulge4 
  point12[4] = -bulge4
  G.polyline(point3, point1, point12, point3)  -- bottom
  
  G.setLayer("H_AciliV" .. aV .. "_Dis")
  G.setThickness(-windowDepthFront)
  point11[4] = bulge5
  point12[4] = bulge5
  G.polyline(point11, point12, point11)  -- middle
  
  
  if (debug) then
    G.circle(intersection1, radius)
    G.circle(intersection2, radius)
    G.polyline(point5, point7, point6, point8, point5, point6)
    G.line(point11, point2, -bulge3)
    G.line(point11, point12, bulge2)
    G.line(point12, point1, bulge4)
  end
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-cW/2, b-cW/2}, {X-a+cW/2, Y-b+cW/2})
  
  G.setLayer("K_Freze_" .. cW .. "mm_SF")
  G.line(point1, point2, bulge2)
  G.line(point4, point3, bulge2)
  
  return true
end

require "ADekoDebugMode"