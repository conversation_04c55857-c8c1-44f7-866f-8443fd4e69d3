-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
	minimum = 150
	limit = 350

  a = 60  
  aa = 30
  ad = 6                -- Vbit derinliği (sunkenDepth)
  aV = 120              -- Vbit Açısı
  yy = 60				-- yay kısmının yüksekliği
  h = 25				-- Vbit göbek arası düz kısım
  gaps = 50				-- kanallar arası mesafe
  cW = 20               -- Tarama Bıçak Çapı
  cT = 6                -- İnce bıçak çapı (Köşe Temizleme)
  
  edgeCornerRExist          	  = 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  extEdgeRtoolExist      			= 0   -- Dı<PERSON> kenar Pah işlemi var mı? derinlik/0:yok
  intVertGrvExist 			      = 2   -- iç kanal var mı? dik çizgiler  derinlik/ 0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  	local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
    --local cR = 5		  -- Radus Veren form bıcak düz uç dar çapı
  local extGrooveOffset			    = 0 --En dıştaki kanalın v bitden nekadar içerde mi "-" dışarda mı "+" tam çizgiden mi "0"
  local extGrooveExist   		    = 2   -- Dış kanal var mı? derinlik/0:yok
  local intGrooveExist 			    = 0   -- iç kanal var mı? derinlik/ 0:yok
  local shapeToolExist 			    = 5   -- Göbek Desen bıçağı var mı? derinlik/0:yok
  
  
  local notchWidth = 0
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi * aV/180)/2.0)
  local sunkenWidth2 = 10              --göbekte desen bıçağı yoksa eşittir. Varsa shapetoolexist den bak

  local Dd = edgeCornerRExist
  local Bb = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape(        ----chamfered part shape
      {Dd,0},		
      {X-Dd,0,0,Bb},
      {X,Dd},
      {X,Y-Dd,0,Bb},
      {X-Dd,Y},
      {Dd,Y,0,Bb},
      {0,Y-Dd},
      {0,Dd,0,Bb},
      {Dd,0}
      )
  else
    G.makePartShape()
  end

	if X < xMin or Y < yMin then
		print("Part dimension too small")
		return true
	end
  
	if h<cW then
		print("Tool too large")
		return true
	end
  
  --G.setLayer("H_Freze10mm_Dis")
  --G.setThickness(-notchDepth)
  --G.rectangle ({notchWidth,notchWidth},{X-notchWidth,Y-notchWidth})
	
  ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  
	yh = ust+yy
	
	local kenardan_mesafe1 = a + sunkenWidth + h
	local kenardan_mesafe2 = ust + sunkenWidth + h				--
	local kenardan_mesafe3 = yh + sunkenWidth + h/2				--içteki kanalların  üst yay başlangıç noktası mesafesi
	
	local gobekGenislik = X-2*a-4*sunkenWidth-2*h
	local stepX = math.floor(gobekGenislik/gaps)
	  
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local bulge1 = G.bulge(
		{X-notchWidth-a, Y-notchWidth-yh, 0, 0},
		{X/2, Y-notchWidth-ust}, {a, Y-notchWidth-yh}
		)
	local points = {
    	{X-notchWidth-a, Y-notchWidth-yh, 0, bulge1},  --1
    	{notchWidth+a, Y-notchWidth-yh, 0, 0},       --2
    	{notchWidth+a, notchWidth+ust, 0, 0},         --3
    	{X-notchWidth-a, notchWidth+ust},             --4
    	{X-notchWidth-a, Y-notchWidth-yh, 0, bulge1}   --5
    	}
  

	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_"  .. "DIS")  -- 
		G.setThickness(-extEdgeVtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
  
  G.setLayer("K_AciliV"..aV)					                ----Dış Sunken
	G.setThickness(0)
	G.sunkenFrameAny(points, 30,ad,aV,vWideDiameter)

	if shapeToolExist > 0 then  				          ----Göbekte desen bıçağı varsa
		G.setLayer("K_Desen")
		G.setThickness(-shapeToolExist)
		G.polylineimp (G.offSet(points, -(sunkenWidth+h)))
	else										----Göbekte desen bıçağı yoksa
    G.setThickness(-ad)
    G.polylineimp (G.offSet(points, -(sunkenWidth+h)))
		sunkenWidth2 = sunkenWidth       					--ustunde kanal işlemi varsa taramadan kaydırılacak mesafe
	end
	
	G.setLayer("K_Freze"..cW.."mm")             --göbekteki tarama işlemi
	G.setThickness(-ad)
	point1 = G.offSet(points, -(sunkenWidth+cW/2))
	G.polylineimp(point1)
		
	if h > cW then
		point2 = G.offSet(points, -(sunkenWidth+h-cW/2))
		G.polylineimp(point2)
		
		k = (h-cW)/(cW/2)
		
		for i=1, k, 1 do
			point1 = G.offSet(point1, -cW/2)
			if point1[2][1]>point2[2][1]-cW/2 then
			break
			end
			G.polylineimp(point1)
		end
	end
	  
    
	--burası ortadaki çizgiler
	--çizgiler
	if intVertGrvExist > 0 then            --içerde dik kanallar var mı?
	
		local point1 = {X-kenardan_mesafe1, Y-kenardan_mesafe3, 0, 0}		--
		local bulge = G.bulge(point1, {X/2, Y-kenardan_mesafe2}, {kenardan_mesafe1, Y-kenardan_mesafe3})
		if (bulge>1) then
			print("Bulge too large")
			return true
		end
		point1[4] = bulge
		
		local point2 = {kenardan_mesafe1, Y-kenardan_mesafe3, 0, 0}
		local aprxGaps = gobekGenislik/(stepX)
		local e = a+sunkenWidth*2+aprxGaps
		local radius = G.radius(point1, point2, bulge)
		
		
		for i=1, (stepX-1), 1		-- loop for the vertical lines --i=0, stepX di
		do
			local Lp1 = {kenardan_mesafe1 + sunkenWidth + i*aprxGaps, kenardan_mesafe2}		-- imaginary lines to intersect the above arc
			local Lp2 = {kenardan_mesafe1 + sunkenWidth + i*aprxGaps, Y}
			comment, pc1, pc2 = G.circleCircleIntersection(point1, radius, point2, radius)		--find center of the arc
			if (comment=='tangent' or comment=='intersection') then
				comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		--find line-arc intersection point
				if (comment2=='tangent' or comment2=='secant') then
				if (i==0 or i==stepX) then 
				--G.setLayer("K_Freze10mm")      -- GELMEYECEK AMA KALSIN
				else
				G.setLayer("K_Ballnose")
				end
				G.setThickness(-intVertGrvExist)
					G.line(intersection1, Lp1)
				end
			else
				G.error()
			return false
			end
		end
	end

  --çizgiler bitti
  
  if intGrooveExist > 0 then              --içerde kanal işlemi var mi?
    
    G.setLayer("K_Freze"..cT.."mm")
    G.setThickness(-intGrooveExist)
    G.polylineimp (G.offSet(points, -(sunkenWidth+h+sunkenWidth2)))
    
  end
   
  if extGrooveExist > 0 then              --dışarda kanal işlemi var mı?
    
    extGrooveOffset = extGrooveOffset + cT/2
    G.setLayer("H_Freze" .. cT .. "mm_icerden")
    G.setThickness(-extGrooveExist)
    
    if extGrooveOffset ~= 0 then
      G.polylineimp (G.offSet(points, extGrooveOffset))
    else
      G.polylineimp(points)
    end
    
  end
  
  
  local distance1 = a + sunkenWidth
  local distance2 = yh + sunkenWidth - 1
  local distance3 = ust + sunkenWidth
  local cc1={distance1,distance3}
  local cc2={X-distance1,Y-distance2}
  cc1[3] = 0
  cc2[3] = 0
  G.setLayer("K_TarKoseTemizl"..cT.."mm")
  G.setThickness(0)
  G.cleanCorners(cc1,cc2,ad,cT)
  
  return true
end


------------------------------------------------
require "ADekoDebugMode"