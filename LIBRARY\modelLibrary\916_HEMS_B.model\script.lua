-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 50 --Kenardan Vbite
  aa = 30					-- Dar kapak için Kenardan mesafe
  b = 15 --Vbitten Dıs kanala
  c = 25 --Vbitten Dıs kanal 2 ye
  aV = 120    --Vbit Açısı
  cT = 6      -- İnce bıçak çapı (Köşe Temizleme)
  cW = 20      -- Cam Yeri arkadan temizleme
  glassMargin = 10
  
      
  windowDepthFront = 14
  windowDepthBack = 6
  
  extGroove2Exist   = 0  --Dış kanal var mı (Var: Derinlik / Yok: 0)
  extGrv2BorForV	= 2		--Dış kanal2 Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)
  extGrooveExist    = 0  --<PERSON>ı<PERSON> kanal var mı (Var: Derinlik / Yok: 0)
  extGrvBorForV	= 1		--<PERSON><PERSON>ş kanal1 Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)
  
  topGrooveExist    = 1.5  --Dış kanal var mı (Var: Derinlik / Yok: 0)
  dt				= 8 --kanal islemi bicak capi
  topGrvExtExist	= 0 --Ust kanal uzatma var mı? (Var:1/Yok:0)
  topGrooveBorF    	= 0  --Ust kanal Ballnose mu? Duz Kanal mi? (B:0/F:1)
  intEdgeToolExist	= 7  
  intEdgeVorSVorR	= 0  --Ic kenar Pah işlemi V mi? Sunken V mi? Raduslu mu?(V:0/SV:1/R:2)
  
  extEdgeToolExist 	= 0  --Dış kenar Pah işlemi var mı? (Var: Derinlik / Yok: 0)
  extEdgeVorR 		= 0  --Dış kenar Pah işlemi V mi? Raduslu mu? (V:0/R:1)
  edgeCornerRExist 	= 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
   
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  --local toolType = "Form" -- "Freze" , "Ballnose" or "Acili_V"
  --local layerNameEd ="K_" .. toolType .. tostring(toolDiameter) .. "mm"
  -- local cR = 5--Raduslu ıc kenar pah bıçak uç düz çapı 
  local ad = intEdgeToolExist-- Vbit derinliği (sunkenDepth)
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  local vWideDiameter = 60
  local R = 5
  local sunkenWidth = 0
  local ustKanal = 0
  
  local D = edgeCornerRExist
  local B = math.tan(math.pi/8)
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist > 0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  
   local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end

  -- if noCabCoreBLimit2 == 1 then
  -- end
 
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
	
	if intEdgeToolExist > 0 then
		
		sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
		if intEdgeVorSVorR == 0 then --(V:0/SV:1/R:2)
			G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
			G.setThickness(-intEdgeToolExist)
			G.rectangle({a+sunkenWidth, ust+sunkenWidth}, {X-a-sunkenWidth, Y-ust-sunkenWidth})
		
		elseif intEdgeVorSVorR == 1 then
			G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
			G.setThickness(0)
			local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
			corner1[3] = 0
			corner2[3] = 0
		
		elseif intEdgeVorSVorR == 2 then
			G.setLayer("H_Raduslu_Pah_" .. "Ic")  -- V shaped deep with large angle 
			G.setThickness(-intEdgeToolExist)
			distance = a + 0
			distance1 = ust + 0
			point1 = {distance, distance1}
			point2 = {X-distance, Y-distance1}
			G.rectangle(point1,point2)
			sunkenWidth = 5  --radus degeri
		end 
	end
  
	if extEdgeToolExist  > 0 then
		if extEdgeVorR == 0 then --  (V:0/R:1)
			G.setLayer("K_AciliV_Pah")	
		elseif extEdgeVorR ==1 then
			G.setLayer("H_Raduslu_Pah_" .. "DIS")	
		end
		G.setThickness(-extEdgeToolExist)
		G.rectangle({0,0},{X,Y})
	end
	
	function BulgePoly(distance1, distance2, R)
		local points = {
			{distance1+R, distance2,0,0},		
			{X-distance1-R, distance2,0,B},		
			{X-distance1, distance2+R,0,0},		
			{X-distance1, Y-distance2-R,0,B},	
			{X-distance1-R, Y-distance2,0,0},	
			{distance1+R, Y-distance2,0,B},		
			{distance1, Y-distance2-R,0,0},		
			{distance1, distance2+R,0,B},		
			{distance1+R, distance2,0,0}		
			}	
			return points
	end
 
	if topGrooveExist  > 0 then
      
		G.setThickness(-topGrooveExist)
		if topGrooveBorF == 0 then  --0 ballnose or 1 flat
			G.setLayer("K_Ballnose"..dT.."mm")  -- DEEP cleanup
			if ad > cT/2 then
				ustKanal = dT/2
			else
				ustKanal = ((dT/2)^2 - ((dT/2)-topGrooveExist)^2)^0.5
			end
      
		elseif topGrooveBorF == 1 then
			G.setLayer("K_Freze"..dT.."mm")  -- DEEP cleanup
			ustKanal = dT/2
		end
		distance = a + ustKanal
		distance1 = ust + ustKanal

		if topGrvExtExist == 1 then
			G.line({distance, 0}, {distance, Y})
			G.line({X-distance, 0}, {X-distance, Y})
			G.line({distance, distance1}, {X-distance, distance1})
			G.line({distance, Y-distance1}, {X-distance, Y-distance1})
			
		elseif topGrvExtExist == 0 then
			if intEdgeToolExist > 0 then
				if intEdgeVorSVorR == 0 then
					points1 = BulgePoly(distance,distance1,sunkenWidth-ustKanal)
					G.polylineimp(points1)
				else
					point1 = {distance, distance1}
					point2 = {X-distance, Y-distance1}
					G.rectangle(point1,point2)
				end
			else
				point1 = {distance, distance1}
				point2 = {X-distance, Y-distance1}
				G.rectangle(point1,point2)
			end
		end
	
	end
	
	if extGrooveExist  > 0 then
    
		if extGrvBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			G.setLayer("K_Ballnose")
		elseif extGrvBorForV == 1 then
			G.setLayer("K_Freze")  
		elseif extGrvBorForV == 2 then
			G.setLayer("K_AciliV")  
		end	

		G.setThickness(-extGrooveExist)
		distance = a-b
		distance1 = ust-b	
		point1 = {distance, distance1}
		point2 = {X-distance, Y-distance1}
		G.rectangle(point1,point2)		
	end
	
	if extGroove2Exist  > 0 then
    
		if extGrv2BorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			G.setLayer("K_Ballnose")
		elseif extGrv2BorForV == 1 then
			G.setLayer("K_Freze")  
		elseif extGrv2BorForV == 2 then
			G.setLayer("K_AciliV")  
		end	

		G.setThickness(-extGroove2Exist)
		distance = a-c
		distance1 = ust-c	
		point1 = {distance, distance1}
		point2 = {X-distance, Y-distance1}
		G.rectangle(point1,point2)		
	end
	
	G.setLayer("H_Freze"..cT.."mm_Ic")
	G.setThickness(-windowDepthFront)
	if sunkenWidth > 0 then
		G.rectangle({a+sunkenWidth, ust+sunkenWidth}, {X-a-sunkenWidth, Y-ust-sunkenWidth})
	elseif sunkenWidth == 0 then
		G.rectangle({a + ustKanal, ust + ustKanal}, {X-a-ustKanal, Y-ust-ustKanal})
	end
	
	--ballnose top groove olduğunda hata var, iç kenar vbit ve sunken olduğunda çizgi çıkmıyor
	G.setFace("bottom")
	G.setThickness(-windowDepthBack)
	G.setLayer("H_Freze"..cW.."mm_Ic_SF")
	local camYeri = sunkenWidth + ustKanal - glassMargin
	G.rectangle({a + camYeri, ust + camYeri}, {X-(a + camYeri), Y-(ust + camYeri)})
  
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
