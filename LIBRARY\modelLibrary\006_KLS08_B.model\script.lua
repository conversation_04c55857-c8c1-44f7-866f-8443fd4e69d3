-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xMin = 140
  yMin = 140
  
  a = 50
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
	
  local finalDepth = 8
  local notchWidth = 2
  local notchDepth = 1
  local sunkenDepth1 = 2
  local sunkenDepth2 = 2*sunkenDepth1+notchDepth
  local sunkenDepth3 = finalDepth-sunkenDepth2
  local vNarrowAngle = 60
  local vMidAngle = 90
  local vWideAngle = 150
  local vNarrowDiameter = 30
  local vMidDiameter = 40
  local vWideDiameter = 75
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vNarrowAngle/180)/2.0)
  local sunkenWidth2 = sunkenDepth1*math.tan((math.pi*vWideAngle/180)/2.0)
  local sunkenWidth3 = sunkenDepth2*math.tan((math.pi*vMidAngle/180)/2.0)
  local sunkenWidth4 = sunkenDepth3*math.tan((math.pi*vMidAngle/180)/2.0)
  local overlap = 10
  local cThinDiameter = 5
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end

  G.setLayer("Cep_Acma")		--Outer pocketing
  G.setThickness(-sunkenDepth2)
  point1 = {-overlap/2,-overlap/2}
  point2 = {a,Y+overlap/2}
  G.rectangle(point1,point2)
  
  point1 = {a-overlap,Y-a}
  point2 = {X-a+overlap,Y+overlap/2}
  G.rectangle(point1,point2)
  
  point1 = {X-a,-overlap/2}
  point2 = {X+overlap/2,Y+overlap/2}
  G.rectangle(point1,point2)
  
  point1 = {a-overlap,-overlap/2}
  point2 = {X-a+overlap,a}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze10mm_Dis")		--Notches
  G.setThickness(-2*sunkenDepth1)
  distance = a+notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setThickness(-sunkenDepth2)
  distance = a
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-sunkenDepth2)
  distance = a+notchWidth+sunkenWidth1+sunkenWidth2+sunkenWidth3
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_AciliV60")		--First angled surface
  G.setThickness(-2*sunkenDepth1)
  distance = a+notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_AciliV15")		--Second angled surface
  G.setThickness(-sunkenDepth1)
  distance = a+notchWidth+sunkenWidth1
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  G.rectangle(point1,point2)
  
  G.setLayer("K_AciliV45")		--SunkenFrames
  G.setThickness(0)
  distance = a+notchWidth+sunkenWidth1+sunkenWidth2
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  local corner1,corner2 = G.sunkenFrame(point1,point2,sunkenDepth2,vMidAngle,vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setThickness(-sunkenDepth2)
  distance = a+notchWidth+sunkenWidth1+sunkenWidth2+sunkenWidth3+notchWidth
  point1 = {distance,distance}
  point2 = {X-distance,Y-distance}
  local corner3,corner4 = G.sunkenFrame(point1,point2,sunkenDepth3,vMidAngle,vMidDiameter)
  corner3[3] = 0
  corner4[3] = 0
  
  G.setLayer("K_Freze5mm")		--Corner cleaning
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,sunkenDepth2,cThinDiameter)
  G.cleanCorners(corner3,corner4,finalDepth,cThinDiameter)
  
  G.setLayer("H_Freze10mm_Ic")		--window
  G.setThickness(-windowDepthFront)
  distance = a+notchWidth+sunkenWidth1+sunkenWidth2+sunkenWidth3+notchWidth+sunkenWidth4
  point1 = {distance, distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance-10, distance-10}, {X-distance+10, Y-distance+10})
  
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({distance, distance}, {X-distance, Y-distance})
  
  return true
end

require "ADekoDebugMode"