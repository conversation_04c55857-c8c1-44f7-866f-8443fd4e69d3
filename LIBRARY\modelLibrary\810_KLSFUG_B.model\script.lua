-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  limit = 280
  
  a = 70
  aa = 30
  ad = 5
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 10
    
  windowDepthFront = 14
  windowDepthBack = 6
  
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> i<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> radü<PERSON>ü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
      
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  local point1 = {X-a, Y-2.5*ust, 0, 0}
  local point2 = {a, Y-2.5*ust, 0, 0}
  local point3 = {a, ust}
  local point4 = {X-a, ust}
  local bulge = G.bulge(point1, {X/2, Y-ust}, point2)
  
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
  
  point1[4] = bulge
  points = {point1,point2,point3,point4,point1}
  
  G.setLayer("H_Freze"..cT.."mm_Ic")
  G.setThickness(-ad)
  G.polylineimp(points)
  
  G.setThickness(-windowDepthFront)
  G.polylineimp(G.offSet(points,-cT/2))
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points,cW/2))
  
  G.setLayer("K_Freze"..cW.."mm_SF")
  G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(points,-cW/4))
  return true
end

require "ADekoDebugMode"