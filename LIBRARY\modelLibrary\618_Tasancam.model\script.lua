-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  local a = 30                                        -- mm cinsinden kenardan mesafe
  local en = 2                                        -- adet cinsinden yataydaki pencere sayısı
  local boy = 3                                       -- adet cinsinden boydaki pencere sayısı
  local vToolAngle = 120                              -- derece cinsinden açı
  local vToolDiameter = 50                            -- mm cinsinden takım çapı, verilmediği için yeterince geniş seçtim
  local d = 6                                         -- mm cinsinden pencere derinliği
  local k = 1                                         -- mm, küt derinliği
  local g = 10                                        -- mm, cam girinti mesafesi
  local m = d*math.tan((math.pi*vToolAngle/180)/2.0)  -- mm cinsinden, V çakısı ile açılmış vadi genişliğinin yarısı
  local n = k*math.tan((math.pi*vToolAngle/180)/2.0)  -- mm cinsinden, pencere iç kesim eni
  local c = 2.5                                       -- mm cinsinden çentik genişliği
  local x = (X-2*a-2*c)/en                            -- mm cinsinden pencerelerin eni
  local y = (Y-2*a-2*c)/boy                           -- mm cinsinden pencerelerin boyu
  local t = 2                                         -- mm cinsinden çentiklerin derinliği
  local p = 12                                        -- mm, pencere içlerini küt kesmek için inilecek derinlik
  local cToolDiameter = 5                             -- mm cinsinden çentikleri oluşturacak silindirik çakının çapı
  local cToolThick = 30                               -- mm cinsinden arka tarafı açacak takımın çapı
  local cTool12MM = 12                                -- mm, 12mm'lik takım
  
  G.setLayer("V120PENCERE")  -- pencereler
  G.setThickness(-t)
  for i=0, en-1, 1
  do
    for j=0, boy-1, 1
    do
      p1 = {i*x+a+c, j*y+a+c}
      p2 = G.ptAdd(p1, {x, y})
      G.sunkenFrame(p1, p2, d, vToolAngle, vToolDiameter)
    end
  end
  
  G.setLayer("5MM_CENTiK")  -- pencerelerin dışındaki ortak çentik
  G.setThickness(-t)  
  o = a+cToolDiameter/2
  G.rectangle({o, o}, {X-o, Y-o})
  
  G.setThickness(-p)  -- V'nin ortasından kes
  for i=0, en-1, 1
  do
    for j=0, boy-1, 1
    do
      o = m+cToolDiameter/2
      p1 = {i*x+a+c+o, j*y+a+c+o}
      p2 = G.ptAdd(p1, {x-2*o, y-2*o})
      G.rectangle(p1, p2)
    end
  end
  
  G.setLayer("CAMLIK30MM_SF")  -- Arka yüz
  G.setThickness(-6)
  
  o = a+c+m-n-g+cToolThick/2
  G.line({X/2, o}, {X/2, Y-o})
  
  u = a+y+c
  G.line({o, u}, {X-o, u})
  
  u = a+2*y+c
  G.line({o, u}, {X-o, u})
  
  o = a+m-n-g+cToolThick/2
  G.rectangle({o, o}, {X-o, Y-o})
  
  return true
end

require "ADekoDebugMode"
