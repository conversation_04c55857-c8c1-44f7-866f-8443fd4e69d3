-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
	G = ADekoLib
	minimum = 150
	limit = 350
	
  a = 50          -- Kenardan Vbite
  aa = 30		  -- Dar kapak için Kenardan mesafe
  ad = 6          -- Vbit derinliği (sunkenDepth)
  aV = 90        -- Vbit Açısı
  gaps = 60
  cT = 6          -- Açılı V üzerinde kanal bıçak çapı
  gd = 15					--göbekteki kanallar arası mesafe
  sbt = 0		 			-- göbek desen bıçağına göre içteki kanallar için kaydırma mesafesi-yoksa gaps/2
  
  intGrooveExist  			= 2   -- iç kanal var mı? derinlik/ 0:yok
  edgeCornerRExist          = 3   -- <PERSON>pak köşe Radüsü var mı? derinlik/0:yok	
	
  if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  --local bd = 3    -- Acili V Ustu kanal derinliği
  -- local d = 10          -- Acili V den iç kanala
  --dd = 2          -- iç kanal derinliği
   
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  
  local extEdgeVtoolExist       	= 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
  local D = edgeCornerRExist
  local topGrooveExist    	= 3   -- Açılı V üzerinde kanal var mı? derinlik/0:yok
  
  
  kontrolListe = {a,d,ad,aV} 
  for i, val in ipairs(kontrolListe) do
    if val <= 0 then
      print("Yanlıs Deger Girilmis")
      return true
    end
  end
    
	local B = math.tan(math.pi/8)

	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist >0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end

	
	local vWideDiameter = 60
	local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
	
	--burası ortadaki çizgiler
	local gobekGenislik = X-2*a-2*sunkenWidth-2*sbt
	
	local stepX = math.floor(gobekGenislik/gaps)
	
	if ((gobekGenislik/(gaps+gd))-stepX)  >= 0.5 then
	--if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local aprxGaps = gobekGenislik/(stepX+1)
	
	if sbt == 0 then
		e = a+sunkenWidth+aprxGaps-gd/2
		e2 = a+sunkenWidth+aprxGaps+gd/2
	else
		e = a+sunkenWidth+sbt+aprxGaps/2-gd/2
		e2 = a+sunkenWidth+sbt+aprxGaps/2+gd/2
		stepX = stepX+1
	end
	--burası ortadaki çizgiler^
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
  
	if topGrooveExist > 0 then
		--G.setLayer("K_Ballnose") 
		G.setLayer("K_Freze"..cT.."mm")  -- 
		G.setThickness(-topGrooveExist)
		distance = a + cT/2
		distance2 = ust + cT/2
		G.line({distance, 0}, {distance, Y})
		G.line({X-distance, 0}, {X-distance, Y})
		G.line({distance, distance2}, {X-distance, distance2})
		G.line({distance, Y-distance2}, {X-distance, Y-distance2})
	end
  
	G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	G.setThickness(0)
	local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	corner1[3] = 0
	corner2[3] = 0
  
	if extEdgeVtoolExist > 0 then
		G.setLayer("K_AciliV_Pah")	
		G.setThickness(-extEdgeVtoolExist)
		G.rectangle({0,0},{X,Y})
	end
  
	if intGrooveExist > 0 then      ------İç kanal varsa
		-- local check = 2*a + 2*sunkenWidth +2*d
		-- if X < check or Y < check then
		-- print("Part dimension too small, check a + h + d value")
		-- return true
		-- end
		G.setLayer("K_Kanal")  -- 
		G.setThickness(-intGrooveExist)
		-- distance = a + 2*sunkenWidth + d
		-- point1 = {distance, distance}
		-- point2 = {X-distance, Y-distance}
		-- G.rectangle(point1,point2)
	
	--burası ortadaki çizgi 1 ler
		for i=0,stepX-1 do
			local x = e+i*aprxGaps
			local offset = ust + sunkenWidth 
			point3 = {x,offset}
			--point3 = {x,0}
			point4 = {x,Y-offset}  
			--point4 = {x,Y}  
			G.line(point3,point4,0)
			i = i+1
		end 
	--burası ortadaki çizgi 2 ler
	
		--burası ortadaki çizgi 1 ler
		for i=0,stepX-1 do
			local x = e2+i*aprxGaps
			local offset = ust + sunkenWidth 
			point3 = {x,offset}
			--point3 = {x,0}
			point4 = {x,Y-offset}  
			--point4 = {x,Y}  
			G.line(point3,point4,0)
			i = i+1
		end 
	--burası ortadaki çizgi 2 ler
	
	end
    
  return true
end

------------------------------------------------

require "ADekoDebugMode"
