-- 3D Positioning Test Script
-- This script tests the updated 3D positioning system with various operations and faces
-- Uses AdekoLib functions for the new architecture

function modelMain()
    G = AdekoLib

    print("=== 3D Positioning System Test ===")

    -- Door dimensions
    local doorWidth = 500
    local doorHeight = 700
    local doorThickness = 18

    -- Set material thickness
    G.setThickness(-doorThickness)

    -- Parse model parameters if available
    if modelParameters then
        if G.parse<PERSON>lParameters(modelParameters) == false then
            return false
        end
    end

    -- Create door outline to establish coordinate system
    -- Test with a custom door shape (chamfered corners)
    local cornerRadius = 30
    G.makePartShape(
        {cornerRadius, 0},
        {doorWidth - cornerRadius, 0},
        {doorWidth, cornerRadius},
        {doorWidth, doorHeight - cornerRadius},
        {doorWidth - cornerRadius, doorHeight},
        {cornerRadius, doorHeight},
        {0, doorHeight - cornerRadius},
        {0, cornerRadius},
        {cornerRadius, 0}
    )

    -- Test 1: TOP SURFACE OPERATIONS (should map to door top surface Y=0)
    print("Testing top face operations...")
    G.setFace("top")

    -- Test line operation with 10mm cylindrical tool
    G.setLayer("TOP_10MM_Line")
    G.setThickness(-3) -- 3mm deep cut
    G.line({100, 100}, {200, 100}) -- Horizontal line

    -- Test circle operation with 8mm cylindrical tool
    G.setLayer("TOP_8MM_Circle")
    G.setThickness(-4) -- 4mm deep drill
    G.circle({150, 150}, 15) -- Circle for drilling

    -- Test rectangle operation with 6mm cylindrical tool
    G.setLayer("TOP_6MM_Pocket")
    G.setThickness(-5) -- 5mm deep pocket
    G.rectangle({250, 120}, {300, 180}) -- Rectangle pocket

    -- Test arc operation with 8mm tool
    G.setLayer("TOP_8MM_Arc")
    G.setThickness(-3) -- 3mm deep arc
    G.arc({400, 150}, 25, 0, 90) -- Quarter circle arc

    -- Test V-groove operation with conical tool
    G.setLayer("TOP_V120_Groove")
    G.setThickness(-2) -- 2mm deep V-groove
    G.line({50, 200}, {150, 300}) -- Diagonal V-groove

    -- Test ballnose operation
    G.setLayer("TOP_BALLNOSE6MM")
    G.setThickness(-3) -- 3mm deep ballnose cut
    G.circle({350, 250}, 20) -- Ballnose circular operation

    -- Test 2: BOTTOM SURFACE OPERATIONS (should map to door bottom surface Y=-18)
    print("Testing bottom face operations...")
    G.setFace("bottom")

    -- Test line operation on bottom face
    G.setLayer("BOTTOM_12MM_Line")
    G.setThickness(-4) -- 4mm deep cut from bottom
    G.line({100, 400}, {200, 400}) -- Horizontal line on bottom

    -- Test circle operation on bottom face
    G.setLayer("BOTTOM_5MM_Circle")
    G.setThickness(-6) -- 6mm deep drill from bottom
    G.circle({150, 450}, 10) -- Circle on bottom face

    -- Test rectangle pocket on bottom face
    G.setLayer("BOTTOM_8MM_Pocket")
    G.setThickness(-8) -- 8mm deep pocket from bottom
    G.rectangle({250, 420}, {320, 480}) -- Rectangle pocket on bottom

    -- Test 3: COMPLEX OPERATIONS
    print("Testing complex operations...")

    -- Back to top face for complex operations
    G.setFace("top")

    -- Multiple operations on same layer
    G.setLayer("TOP_10MM_MultiOp")
    G.setThickness(-3)
    G.line({300, 350}, {350, 350}) -- First line
    G.line({300, 360}, {350, 360}) -- Second line (parallel)
    G.line({300, 370}, {350, 370}) -- Third line (parallel)

    -- Complex shape with multiple segments
    G.setLayer("TOP_6MM_Complex")
    G.setThickness(-4)
    -- Create a complex path
    G.line({50, 500}, {100, 500})
    G.arc({100, 520}, 20, 270, 0) -- Quarter arc
    G.line({120, 520}, {170, 520})
    G.arc({170, 500}, 20, 0, 90) -- Quarter arc
    G.line({170, 480}, {50, 480})

    -- Test 4: DIFFERENT TOOL TYPES
    print("Testing different tool types...")

    -- Radial tool operation
    G.setLayer("TOP_RADIAL2MM")
    G.setThickness(-1) -- Shallow radial cut
    G.line({400, 400}, {450, 400}) -- Edge profiling

    -- Special tool operation
    G.setLayer("TOP_SPECIAL_Dovetail")
    G.setThickness(-5) -- Dovetail depth
    G.rectangle({400, 450}, {450, 500}) -- Dovetail joint

    -- Test 5: EDGE OPERATIONS
    print("Testing edge operations...")
    G.setFace("front")

    -- Edge profiling
    G.setLayer("EDGE_Profile_2mm")
    G.setThickness(-2) -- Edge rounding
    G.line({0, 0}, {doorWidth, 0}) -- Bottom edge
    G.line({doorWidth, 0}, {doorWidth, doorHeight}) -- Right edge

    print("3D positioning test completed!")
    print("Check 3D view to verify:")
    print("- Top face operations should be positioned on door top surface (Y=0)")
    print("- Bottom face operations should be positioned on door bottom surface (Y=-18)")
    print("- Tools should be positioned correctly relative to door center")
    print("- Different tool types should have appropriate geometries")
    print("- Operations should align with their 2D canvas positions")
    print("- Face detection should work correctly for top/bottom/edge operations")

    return true
end
