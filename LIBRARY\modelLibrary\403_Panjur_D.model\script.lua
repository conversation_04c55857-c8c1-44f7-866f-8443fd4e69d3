-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 200
  limit = 350
  
  a = 70  -- side margin
  aa = 30					-- Dar <PERSON>pak icin <PERSON> mesafe
  b = 50  -- inclined pocket height
  th = 300 -- height of the top part
  d = 75   -- vertical distance between top and bottom
  cW = 20
  cT = 5
  step = 0.7				--acili yuzey tarama yan adim mesafesi 
  
  startingDepth = 2
  finalDepth = 10
  
  extEdgeVtoolExist       	= 0   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak kose Radusu var mi? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")		
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if X < xMin or Y < yMin then
    G.error("Dimension too small.")
    return false
  end
  
  local sum = 2*aa + th + d + 2*b
  if (Y <= sum) then
    return false
  end
  
  bh = Y-th-d-2*aa -- height of the bottom part
 
  
  -- top part
  
  local point1, point2, point3, point4, point5 = {}, {}, {}, {}, {}
  
  local step2 = step
  
  local howMany = math.floor(th/b)
  
  if ((th/b) - howMany) >0.5 then
	howMany = howMany + 1
  end
  
  local mB = th/howMany
  local polylines, sols, sags = {}, {}, {}
  local araThickness = finalDepth - (finalDepth * (mB - cW)) / (mB - cT)

  for i=0, howMany-1, 1
  do
    point1 = {a, aa+i*mB}
    point2 = {X-a, aa+(i+1)*mB}
    
    local polyline = G.inclinedPocket2(point1, point2, finalDepth-araThickness, step, cW, true)
    polyline = G.scaleDepth(polyline, 0, -finalDepth+araThickness)
    local g1 = G.ptAdd(polyline[#polyline], {0, 0, 1.1*materialThickness})
    table.insert(polyline, g1)
    local g2 = G.ptAdd({a, aa+(i+1)*mB}, {cW/2, cW/2, 1.1*materialThickness})
    table.insert(polyline, g2)
    polylines = G.joinPolylines(polylines, polyline)
    
    if (cT~=0) then
      q1 = {point2[1], point1[2] + cW}
      
      G.setLayer("K_Freze"..cT.."mm")		-- Fine pass
      G.setThickness(-startingDepth)
      G.polylineimp(G.scaleDepth(G.inclinedPocket2(point1, q1, araThickness, step2, cT, true), -(finalDepth-araThickness), -(finalDepth)))
      
      G.setThickness(-startingDepth)		-- Fine side passes
      point4 = G.ptAdd(point1, {3*cW/4, mB})
      local sol = G.scaleDepth(G.inclinedPocket(point1, point4, finalDepth, step2, cT, "h", true), 0, -finalDepth)
      table.insert(sol, {a+cT/2.0, aa+(i+1)*mB+cT/2.0, sol[#sol][3]})
      sols = G.joinPolylines(sols, sol)

      point5 = G.ptSubtract(point2, {3*cW/4, mB})
      local sag = G.scaleDepth(G.inclinedPocket(point5, point2, finalDepth, step2, cT, "h", true), 0, -finalDepth)
      table.insert(sag, {X-a-3*cW/4+cT/2.0, aa+(i+1)*mB+cT/2.0, sag[#sag][3]})
      sags = G.joinPolylines(sags, sag)

    end
  end
  
  G.setLayer("K_Freze"..cW.."mm")		-- Rough pass
  G.setThickness(-startingDepth)
  table.remove(polylines, #polylines)
  G.polylineimp(polylines)
  
  if (startingDepth ~= 0) then
    G.setLayer("H_Freze"..cT.."mm_Ic")
    G.setThickness(-2)
    G.rectangle({a, aa}, {X-a, th+aa})
  end
  
  if (cT~=0) then
    G.setLayer("K_Freze"..cT.."mm")
    table.remove(sols, #sols)
    G.polylineimp(sols)
    table.remove(sags, #sags)
    G.polylineimp(sags)
  end
  
  -- bottom part
  
  point1, point2, point3, point4, point5 = {}, {}, {}, {}, {}

  step2 = step
  
  howMany = math.floor(bh/b)
  
  if ((bh/b) - howMany) > 0.5 then
	howMany = howMany + 1
  end
  
  mB = bh/howMany
  polylines, sols, sags = {}, {}, {}
  araThickness = finalDepth - (finalDepth * (mB - cW)) / (mB - cT)

  for i=0, howMany-1, 1
  do
    point1 = {a, aa+i*mB+th+d}
    point2 = {X-a, aa+(i+1)*mB+th+d}
    
    local polyline = G.inclinedPocket2(point1, point2, finalDepth-araThickness, step, cW, true)
    polyline = G.scaleDepth(polyline, 0, -finalDepth+araThickness)
    local g1 = G.ptAdd(polyline[#polyline], {0, 0, 1.1*materialThickness})
    table.insert(polyline, g1)
    local g2 = G.ptAdd({a, aa+(i+1)*mB+th+d}, {cW/2, cW/2, 1.1*materialThickness})
    table.insert(polyline, g2)
    polylines = G.joinPolylines(polylines, polyline)
    
    if (cT~=0) then
      q1 = {point2[1], point1[2] + cW}
      
      G.setLayer("K_Freze"..cT.."mm")		-- Fine pass
      G.setThickness(-startingDepth)
      G.polylineimp(G.scaleDepth(G.inclinedPocket2(point1, q1, araThickness, step2, cT, true), -(finalDepth-araThickness), -(finalDepth)))
      
      G.setThickness(-startingDepth)		-- Fine side passes
      point4 = G.ptAdd(point1, {3*cW/4, mB})
      local sol = G.scaleDepth(G.inclinedPocket(point1, point4, finalDepth, step2, cT, "h", true), 0, -finalDepth)
      table.insert(sol, {a+cT/2.0, aa+(i+1)*mB+cT/2.0+th+d, sol[#sol][3]})
      sols = G.joinPolylines(sols, sol)

      point5 = G.ptSubtract(point2, {3*cW/4, mB})
      local sag = G.scaleDepth(G.inclinedPocket(point5, point2, finalDepth, step2, cT, "h", true), 0, -finalDepth)
      table.insert(sag, {X-a-3*cW/4+cT/2.0, aa+(i+1)*mB+cT/2.0+th+d, sag[#sag][3]})
      sags = G.joinPolylines(sags, sag)

    end
  end
  
  G.setLayer("K_Freze"..cW.."mm")		-- Rough pass
  G.setThickness(-startingDepth)
  table.remove(polylines, #polylines)
  G.polylineimp(polylines)
  
  if (startingDepth ~= 0) then
    G.setLayer("H_Freze"..cT.."mm_Ic")
    G.setThickness(-2)
    G.rectangle({a, Y-aa}, {X-a, th+aa+d})
  end
  
  if (cT~=0) then
    G.setLayer("K_Freze"..cT.."mm")
    table.remove(sols, #sols)
    G.polylineimp(sols)
    table.remove(sags, #sags)
    G.polylineimp(sags)
  end
  
  return true
end

require "ADekoDebugMode"