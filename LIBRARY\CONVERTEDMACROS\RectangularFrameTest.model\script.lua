-- ADekoCA<PERSON>, Model Script - Rectangular Frame Test
-- Converted from C# azCAM macro: Rectangular Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Rectangular Frame parameters (from original C# macro)
  A = 2     -- Left margin
  B = 2     -- Top margin
  C = 2     -- Right margin
  D = 2     -- Bottom margin
  Z = 5     -- Thickness/depth
  
  -- Reference corner (from original parameters.json: reference = 3)
  -- 1=top-left, 2=top-center, 3=top-right, 4=middle-left, 5=center, 6=middle-right, 7=bottom-left, 8=bottom-center, 9=bottom-right
  referenceCorner = 3  -- top-right (original default)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + C + 20   -- minimum required width
  local minHeight = B + D + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Rectangular Frame function (converted from C# macro)
  local function rectangular_frame(A, B, C, D, Z, refCorner)
    -- Use default values if parameters not provided
    A = A or 2
    B = B or 2
    C = C or 2
    D = D or 2
    Z = Z or -5
    refCorner = refCorner or 3
    
    print(string.format("Creating rectangular frame"))
    print(string.format("Margins: A=%d, B=%d, C=%d, D=%d", A, B, C, D))
    print(string.format("Reference corner: %d", refCorner))
    
    -- Calculate frame dimensions
    local frameWidth = A + C  -- Total horizontal margins
    local frameHeight = B + D -- Total vertical margins
    
    -- Calculate frame position based on reference corner
    local frameLeft, frameBottom
    
    if refCorner == 1 then      -- top-left
      frameLeft = 0
      frameBottom = height - frameHeight
    elseif refCorner == 2 then  -- top-center
      frameLeft = (width - frameWidth) / 2
      frameBottom = height - frameHeight
    elseif refCorner == 3 then  -- top-right
      frameLeft = width - frameWidth
      frameBottom = height - frameHeight
    elseif refCorner == 4 then  -- middle-left
      frameLeft = 0
      frameBottom = (height - frameHeight) / 2
    elseif refCorner == 5 then  -- center
      frameLeft = (width - frameWidth) / 2
      frameBottom = (height - frameHeight) / 2
    elseif refCorner == 6 then  -- middle-right
      frameLeft = width - frameWidth
      frameBottom = (height - frameHeight) / 2
    elseif refCorner == 7 then  -- bottom-left
      frameLeft = 0
      frameBottom = 0
    elseif refCorner == 8 then  -- bottom-center
      frameLeft = (width - frameWidth) / 2
      frameBottom = 0
    elseif refCorner == 9 then  -- bottom-right
      frameLeft = width - frameWidth
      frameBottom = 0
    else
      -- Default to top-right if invalid reference
      frameLeft = width - frameWidth
      frameBottom = height - frameHeight
    end
    
    -- Calculate frame corner points (following C# logic exactly)
    -- p1 = (A, D), p2 = (width-C, D), p3 = (width-C, height-B), p4 = (A, height-B)
    local p1 = {frameLeft + A, frameBottom + D}                    -- Bottom-left
    local p2 = {frameLeft + frameWidth - C, frameBottom + D}       -- Bottom-right
    local p3 = {frameLeft + frameWidth - C, frameBottom + frameHeight - B}  -- Top-right
    local p4 = {frameLeft + A, frameBottom + frameHeight - B}      -- Top-left
    
    -- Validate that frame fits within panel bounds
    if frameLeft < 0 or frameBottom < 0 or 
       frameLeft + frameWidth > width or frameBottom + frameHeight > height then
      print("Warning: Frame extends beyond panel boundaries")
      print(string.format("Frame bounds: (%.1f,%.1f) to (%.1f,%.1f)", 
            frameLeft, frameBottom, frameLeft + frameWidth, frameBottom + frameHeight))
      print(string.format("Panel bounds: (0,0) to (%.1f,%.1f)", width, height))
    end
    
    -- Set layer and thickness for frame
    G.setLayer("G_V15")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the rectangular frame
    G.polyline(p1, p2, p3, p4, p1)  -- Close the rectangle
    
    print(string.format("Rectangular frame created:"))
    print(string.format("  Frame bounds: (%.1f,%.1f) to (%.1f,%.1f)", 
          frameLeft, frameBottom, frameLeft + frameWidth, frameBottom + frameHeight))
    print(string.format("  Inner frame: (%.1f,%.1f) to (%.1f,%.1f)", p1[1], p1[2], p3[1], p3[2]))
    print(string.format("  Frame corners:"))
    print(string.format("    Bottom-left: (%.1f, %.1f)", p1[1], p1[2]))
    print(string.format("    Bottom-right: (%.1f, %.1f)", p2[1], p2[2]))
    print(string.format("    Top-right: (%.1f, %.1f)", p3[1], p3[2]))
    print(string.format("    Top-left: (%.1f, %.1f)", p4[1], p4[2]))
    
    return true
  end
  
  -- Call the rectangular frame function
  local success = rectangular_frame(A, B, C, D, -Z, referenceCorner)
  
  if success then
    local refNames = {
      [1] = "top-left", [2] = "top-center", [3] = "top-right",
      [4] = "middle-left", [5] = "center", [6] = "middle-right", 
      [7] = "bottom-left", [8] = "bottom-center", [9] = "bottom-right"
    }
    
    print(string.format("Rectangular frame created with parameters:"))
    print(string.format("  Margins: A=%d, B=%d, C=%d, D=%d mm", A, B, C, D))
    print(string.format("  Reference corner: %d (%s)", referenceCorner, refNames[referenceCorner] or "unknown"))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a rectangular frame with specified margins")
    print("  - Frame size adapts to panel size minus margins")
    print("  - Independent margin control for each side")
    print("  - Frame positioned relative to specified reference corner")
    print("")
    print("Applications:")
    print("  - Standard rectangular frames")
    print("  - Margin-based frame positioning")
    print("  - Adaptive frame sizing")
    print("  - Panel edge-relative frames")
  end
  
  return true
end

require "ADekoDebugMode"
