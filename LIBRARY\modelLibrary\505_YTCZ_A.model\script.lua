-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script Yatay cızgılı
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
    
  a = -5
  xMin = 140
  yMin = 140
  toolDiameter = 16
  ad = 5 -- toolDepth kanal derinliği
  gaps = 60
  
  edgeCornerRExist        = 0   -- Kapak köşe Radüsü var mı?
  extEdgeVtoolExist        = 0   --Kenarlarda aynı takımla işleme var mı? "1" var/ "0" 0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  bulge = math.tan(math.pi/8)
  
  local D = edgeCornerRExist
  local B = math.tan(math.pi/8)
  if edgeCornerRExist > 0 then
    <PERSON><PERSON>makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
    G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
   
  local toolType = "Form" -- "Freze" , "Ballnose" or "Acili_V"
  --local toolDepth = 8
  --local toolDiameter = 16       --Ballnose or conical
  local toolAngle = 170
  local grooveThickness =0
  local toolRadius = toolDiameter/2
  --local gaps = 12.5
  local ara = 0
  local layerNameEd ="K_" .. toolType .. tostring(toolDiameter) .. "mm"
  local gorunenHal = false              --kenarlarda da takım hareketi varsa "true", sadece ortalarda kanal varsa "alse"
  grooveThickness = toolDiameter
  
  --Aralık hesaplama
  
    local aprxGaps = gaps
    local e = gaps
    G.setLayer(layerNameEd)
    G.setThickness(-ad)
    -- local fPcY = 0

    local fPcY = math.floor(Y/gaps)

    if ((Y/gaps)-fPcY)  > 0.5 then
      fPcY = fPcY + 1
    end
    
    if edgeChamferExist == 1 then      --kenardaki işlem varsa
      point1 = {0+a, 0}
      point2 = {X-a, 0}
      point5 = {0+a, Y}
      point6 = {X-a, Y}
      G.line(point1,point2,0)
      G.line(point5,point6,0)
      gorunenHal = true
    end 
    
    
    if gorunenHal == false then
      e = ((Y-(fPcY)*grooveThickness)/(fPcY+1))+grooveThickness/2
      aprxGaps = e+grooveThickness/2

      for i=0,fPcY-1 do
        local y = e+i*aprxGaps
          --point3 = {x,a}
          --point4 = {x,Y-a} 
        point3 = {a,y}
        point4 = {X-a,y}  
        G.line(point3,point4,0)
        i = i+1
      end
      --print("bu kenarlar farklı ortalar eşit aralık FALSE"..aprxGaps)
    else
      aprxGaps = (Y/(fPcY+1))
      for i=0,fPcY-1 do
        local y = aprxGaps+i*aprxGaps
          --point3 = {x,a}
          --point4 = {x,Y-a} 
        point3 = {a,y}
        point4 = {X-a,y}  
        G.line(point3,point4,0)
        i = i+1
      end
      --print("bu esit aralık TRUE"..aprxGaps)
    end  
    
  
  return true
end  
  
require "ADekoDebugMode"