X = 500  -- en
Y = 700  -- boy/y<PERSON>kseklik
modelParameters = ""
materialThickness = 18
offset = 20
edge1layer="LMM0"
edge2layer="LMM1"
edge3layer="LMM2"
edge4layer="LMM3"
edge1thickness=0.1
edge2thickness=0.2
edge3thickness=0.3
edge4thickness=0.4
doesSizeIncludeEdgeThickness = "false"
-- Don't override global ADekoLib - use the one that's already loaded
-- ADekoLib = require("ADekoLib")  -- REMOVED: This was overriding the global ADekoLib
local engine = require("makerjs_engine")

-- Initialize the debug model
engine.model_def("debug_model", function()
    engine.layer("debug")

    -- Set up variables for coordinate system
    local current_origin = {0, 0}
    local current_position = {0, 0}
    local current_angle = 0
    local pen_down = true
    local shape_counter = 0

    -- Helper function to generate unique shape names
    local function get_shape_name(prefix)
        shape_counter = shape_counter + 1
        return (prefix or "shape") .. "_" .. shape_counter
    end

    -- Helper functions for turtle-like operations
    local function zero(x, y)
        current_origin = {x or 0, y or 0}
        current_position = {0, 0}
    end

    local function move(distance)
        if not distance then return end
        local rad = math.rad(current_angle)
        local dx = distance * math.cos(rad)
        local dy = distance * math.sin(rad)

        local old_pos = {current_position[1], current_position[2]}
        current_position[1] = current_position[1] + dx
        current_position[2] = current_position[2] + dy

        if pen_down then
            local adj_x1 = old_pos[1] + current_origin[1]
            local adj_y1 = old_pos[2] + current_origin[2]
            local adj_x2 = current_position[1] + current_origin[1]
            local adj_y2 = current_position[2] + current_origin[2]
            engine.line(get_shape_name("move"), adj_x1, adj_y1, adj_x2, adj_y2)
        end
    end

    local function turn(angle)
        if angle then
            current_angle = (current_angle + angle) % 360
        end
    end

    local function crcl(x, y, radius)
        local adj_x = (x or 0) + current_origin[1]
        local adj_y = (y or 0) + current_origin[2]
        engine.circle(get_shape_name("circle"), adj_x, adj_y, radius or 0)
    end

    local function text(txt, angle, x, y)
        if not txt then return end
        local adj_x = (x or 0) + current_origin[1]
        local adj_y = (y or 0) + current_origin[2]
        -- Create a small rectangle to mark text position
        engine.rect(get_shape_name("text_marker"), adj_x - 1, adj_y - 1,
                   math.max(string.len(tostring(txt)) * 8, 4), 12)
    end

end) -- End of model definition

-- Initialize ADekoLib with makerjs_engine support
ADekoLib.engine = engine

-- Create a temporary model for the actual geometry from modelMain()
engine.model_def("temp_model", function()
    -- This model will be populated by ADekoLib calls during modelMain()
end)

-- Set the engine to use the temp_model for geometry
engine.set_current_model("temp_model")

-- Set up global variables that ADekoLib expects
_G.showPoints = true
ADekoLib.start()
ADekoLib.showPoints(true)
ADekoLib.enableListing(true)
modelMain()
ADekoLib.finish()

-- Export the debug model to JSON (face layout)
local debug_json = engine.export_model("debug_model")
print("Debug model JSON generated:")
print(debug_json)

-- Export the temporary model that contains the actual geometry from modelMain()
local temp_json = engine.export_model("temp_model")
print("Geometry model JSON generated:")
print(temp_json)

-- Note: File writing removed to prevent app restart cycles in dev mode
-- The JSON output is captured by the Rust engine and available for visualization

-- wait() -- Removed as it's not needed for model generation