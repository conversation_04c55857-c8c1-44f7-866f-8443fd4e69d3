-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script - Cross Divided Frame Test
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 300
  Y = Y or 200
  materialThickness = materialThickness or 18
  
  -- Frame parameters
  a = 15    -- margin from edges (A parameter)
  b = 8     -- gap between frame parts (B parameter)  
  c = 20    -- frame width (C parameter)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Check minimum dimensions
  local minWidth = 2 * a + c + b + 20  -- minimum required width
  local minHeight = 2 * a + 2 * c + b + 20  -- minimum required height
  
  if X < minWidth or Y < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, X, Y))
    return true
  end
  
  -- Adjust parameters for smaller doors if needed
  if X < 250 or Y < 200 then
    a = math.max(8, math.floor(math.min(X, Y) * 0.06))  -- 6% of smaller dimension
    b = math.max(3, math.floor(math.min(X, Y) * 0.03))  -- 3% of smaller dimension
    c = math.max(10, math.floor(math.min(X, Y) * 0.08)) -- 8% of smaller dimension
  end
  
  -- Create the cross divided frame
  local frameDepth = 4  -- 4mm depth
  G.setLayer("K_Freze10mm")
  G.setThickness(-frameDepth)
  
  -- Cross divided frame function (embedded for standalone use)
  local function cross_divided_frame(A, B, C, Z)
    -- Use default values if parameters not provided
    A = A or 10
    B = B or 5
    C = C or 15
    Z = Z or -3
    
    -- Get current part dimensions
    local frameWidth = X
    local frameHeight = Y
    
    -- Create first frame part (left/bottom section)
    local p1 = {A, A}
    local p2 = {frameWidth - A, A}
    local p3 = {frameWidth - A, A + C}
    local p4 = {A, frameHeight - A - C - B}
    local p5 = {A, A}  -- close the path
    
    -- Create the first polyline
    G.polyline(p1, p2, p3, p4, p5)
    
    -- Start a new shape for the second frame part
    G.nextShape()
    
    -- Create second frame part (right/top section)
    local q1 = {frameWidth - A, A + C + B}
    local q2 = {frameWidth - A, frameHeight - A}
    local q3 = {A, frameHeight - A}
    local q4 = {A, frameHeight - A - C}
    local q5 = {frameWidth - A, A + C + B}  -- close the path
    
    -- Create the second polyline
    G.polyline(q1, q2, q3, q4, q5)
    
    return true
  end
  
  -- Call the cross divided frame function
  cross_divided_frame(a, b, c, -frameDepth)
  
  print(string.format("Cross divided frame created with parameters: A=%d, B=%d, C=%d, Z=%d", 
        a, b, c, -frameDepth))
  print(string.format("Door size: %dx%dmm", X, Y))
  
  return true
end

require "ADekoDebugMode"
