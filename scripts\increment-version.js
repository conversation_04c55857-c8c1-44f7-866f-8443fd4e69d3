#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Increment version script for Tauri project
 * Updates version in package.json, Cargo.toml, and tauri.conf.json
 */

function incrementVersion(version, type = 'patch') {
  const parts = version.split('.').map(Number);
  
  switch (type) {
    case 'major':
      parts[0]++;
      parts[1] = 0;
      parts[2] = 0;
      break;
    case 'minor':
      parts[1]++;
      parts[2] = 0;
      break;
    case 'patch':
    default:
      parts[2]++;
      break;
  }
  
  return parts.join('.');
}

function updatePackageJson(newVersion) {
  const packagePath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  console.log(`Updating package.json: ${packageJson.version} -> ${newVersion}`);
  packageJson.version = newVersion;
  
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
}

function updateCargoToml(newVersion) {
  const cargoPath = path.join(process.cwd(), 'src-tauri', 'Cargo.toml');
  let cargoContent = fs.readFileSync(cargoPath, 'utf8');
  
  const versionRegex = /^version = "([^"]+)"$/m;
  const match = cargoContent.match(versionRegex);
  
  if (match) {
    console.log(`Updating Cargo.toml: ${match[1]} -> ${newVersion}`);
    cargoContent = cargoContent.replace(versionRegex, `version = "${newVersion}"`);
    fs.writeFileSync(cargoPath, cargoContent);
  }
}

function updateTauriConf(newVersion) {
  const tauriConfPath = path.join(process.cwd(), 'src-tauri', 'tauri.conf.json');
  const tauriConf = JSON.parse(fs.readFileSync(tauriConfPath, 'utf8'));
  
  console.log(`Updating tauri.conf.json: ${tauriConf.version} -> ${newVersion}`);
  tauriConf.version = newVersion;
  
  // Also update productName and window title if they contain version
  if (tauriConf.productName && tauriConf.productName.includes('0.10.0')) {
    tauriConf.productName = tauriConf.productName.replace(/\d+\.\d+\.\d+/, newVersion);
  }
  
  if (tauriConf.app && tauriConf.app.windows) {
    tauriConf.app.windows.forEach(window => {
      if (window.title && window.title.includes('0.10.0')) {
        window.title = window.title.replace(/\d+\.\d+\.\d+/, newVersion);
      }
    });
  }
  
  fs.writeFileSync(tauriConfPath, JSON.stringify(tauriConf, null, 2) + '\n');
}

function main() {
  const args = process.argv.slice(2);
  const versionType = args[0] || 'patch'; // patch, minor, major

  try {
    // Read current version from package.json
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const currentVersion = packageJson.version;

    // Increment version
    const newVersion = incrementVersion(currentVersion, versionType);

    console.log(`Incrementing ${versionType} version: ${currentVersion} -> ${newVersion}`);

    // Update all files
    updatePackageJson(newVersion);
    updateCargoToml(newVersion);
    updateTauriConf(newVersion);

    console.log('Version updated successfully!');
    console.log(`New version: ${newVersion}`);

    return newVersion;
  } catch (error) {
    console.error('Error updating version:', error.message);
    process.exit(1);
  }
}

// Check if this module is being run directly
const isMainModule = import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'));
if (isMainModule) {
  main();
}

export { incrementVersion, main };
