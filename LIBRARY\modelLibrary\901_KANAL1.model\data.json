{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Ballnose\t\t Kure uclu kanal bicagi \nH_Raduslu_Pah_DIS\tRaduslu pah bicagi  \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-minimum kenar u<PERSON>", "parameterName": "xMin"}, {"defaultValue": 150, "description": "Y-minimum kenar u<PERSON>u", "parameterName": "yMin"}, {"defaultValue": 250, "description": "X icin kucuk kapak deseni limit degeri", "parameterName": "xLimit"}, {"defaultValue": 250, "description": "Y icin kucuk kapak deseni limit degeri", "parameterName": "yLimit"}, {"defaultValue": 60, "description": "Kenardan kanal 1 e mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 2, "description": "<PERSON><PERSON> derin<PERSON>gi", "parameterName": "ad"}, {"defaultValue": 15, "description": "Kanal1 den  kanal2 ye", "parameterName": "b"}, {"defaultValue": 15, "description": "Kanal2 den  kanal3 e", "parameterName": "c"}, {"defaultValue": 6, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeRtoolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}