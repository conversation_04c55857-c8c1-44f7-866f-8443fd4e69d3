{"modelNote": "Katmanlar:\t\tTakimlar: \n----------------------------------------------------------------------------------------- \nK_Acili_aV \t\tAcili V bicagi \nCep_Acma\t\t Tarama duz Freze bicagi \nK_Freze_cW_mm\t\t cW Capli Freze bicagi \nK_Freze_cT_mm\t\t cT Capli Freze bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 15, "description": "Acili V kenardan DIS kanala mesafe", "parameterName": "b"}, {"defaultValue": 2, "description": "Acili V kenardan ustundeki kanala mesafe", "parameterName": "c"}, {"defaultValue": -2, "description": "Gobekteki acili V kenardan ustundeki kanala mesafe", "parameterName": "d"}, {"defaultValue": 25, "description": "Acili V kenardan gobege mesafe", "parameterName": "h"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 135, "description": "Acili vbit acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 6, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 2, "description": "DIS kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 2, "description": "Ic veya vbit ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}