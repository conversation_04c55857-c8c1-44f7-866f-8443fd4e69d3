-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  	
  G = ADekoLib
  minimum = 150
  limit = 350

  a = 60                    -- Kenardan Vbite
  aa = 30					-- Dar kapak için Kenardan mesafe
  ad = 6                    -- Vbit derinliği (sunkenDepth)
  b = 10                   -- D<PERSON>ş Kenardan Dıs kanala
  cW = 50                  -- Tarama Bıçak Çapı
  cT = 6                   -- İnce bıçak çapı (Köşe Temizleme)
	
	intVtoolExist				= 90 -- Ic kenar Vbit var mı? var:aci/0:yok
	extGrooveExist          	= 3   -- Dış kanal ve-veya V bit üstü var mı? derinlik/0:yok
	extEdgeVtoolExist       	= 6  -- <PERSON><PERSON><PERSON> kenar <PERSON> işlem<PERSON> var mı? derinlik/0:yok
	edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
	 
	local sunkenWidth =  ad * math.tan((math.pi*intVtoolExist/180)/2)
	local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
	
	local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  
	local D = edgeCornerRExist

	local B = math.tan(math.pi/8)

	G.setThickness(-materialThickness)
	G.setFace("top")
	
	if edgeCornerRExist >0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	 
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
	
	if cW <= 0 then
		cW = cT
	end
	
	if extGrooveExist > 0 then
		G.setLayer("K_Freze"..cT.."mm")  -- DEEP cleanup
		G.setThickness(-extGrooveExist)
		distance = b 
		distance2 = b 
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	end
	
	if intVtoolExist > 0 then
		G.setLayer("K_AciliV"..intVtoolExist)  -- DEEP cleanup
		G.setThickness(-ad)
		distance = a 
		distance2 = ust 
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	end
		
	G.setLayer("Cep_Acma")  -- DEEP cleanup
	G.setThickness(-ad)
	distance1 = a + sunkenWidth
	distance2 = ust + sunkenWidth
    aa3 = {distance1,distance2,0}
	aa4 = {X-distance1,Y-distance2,0}
    aa1 = {X-distance1,distance2,0}
    aa2 = {distance1,Y-distance2,0}
    G.rectangle(aa3,aa4)
	
	if cW/2 >= 3*cT then
		distance = cW/2
		G.setLayer("Cep_Acma_Kose")
		G.setThickness(-ad)
		G.rectangle(aa1, {aa1[1]-distance,aa1[2]+distance})
		G.rectangle(aa2, {aa2[1]+distance,aa2[2]-distance})
		G.rectangle(aa3, {aa3[1]+distance,aa3[2]+distance})
		G.rectangle(aa4, {aa4[1]-distance,aa4[2]-distance})
			
		G.setLayer("K_TarKoseTemizl"..cT.."mm")
		G.setThickness(-ad)
		distance = cT/2
		G.rectangle({aa3[1]+distance,aa3[2]+distance},{aa4[1]-distance,aa4[2]-distance})
	else
		G.setLayer("K_TarKoseTemizl"..cT.."mm")
		G.setThickness(0)
		distance1 = a + sunkenWidth
		distance2 = ust + sunkenWidth
		point1 = {distance1, distance2}
		point2 = {X-distance1, Y-distance2}
		G.cleanCorners(point1,point2,ad,cT)
	end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
