-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib 
  xMin = 150
  yMin = 150
  xLimit = 200
  yLimit = 200
  
  a = 60          -- Kenardan Kanal1 e
  aa = 30		  -- Dar kapak için <PERSON> mesafe
  ad = 2                    -- Vbit derinliği (sunkenDepth)
  b = 15          -- Kanal1 den  Kanal2 ye
  c = 15          -- Kanal2 den  Kanal3 ye
  cT = 6          -- <PERSON><PERSON> bıçak çapı (Köşe Temizleme)
  
  edgeCornerRExist          		= 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  extEdgeRtoolExist       			= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON>h işlemi var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  --bd = 3          -- dış kanal derinliği
  
  ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
	
  local D = edgeCornerRExist

  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
 
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end

		G.setLayer("K_Ballnose") 
		G.setThickness(-ad)
		distance = a
		distance2 = ust
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)

  if b > 0 then
		distance = a+b
		distance2 = ust+b
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	end
  if c > 0 then
		distance = a+b+c
		distance2 = ust+b+c
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	end
	
		
	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end

  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
