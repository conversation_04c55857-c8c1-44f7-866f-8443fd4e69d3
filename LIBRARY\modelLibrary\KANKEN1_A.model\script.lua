-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  minimum = 250
  
  K1Off = 30              -- <PERSON><PERSON><PERSON> Offset
  K1Opr = 1              --Yapilacak islem 1-<PERSON>_<PERSON><PERSON>, 2-<PERSON>_<PERSON><PERSON>, 3-K_<PERSON><PERSON>, 4-<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5-K_<PERSON><PERSON><PERSON><PERSON>, 6-<PERSON><PERSON><PERSON>
  K1Depth = 2           -- Kanal Derinligi
  K1DiaAngle = 10 				-- Cap veya Vbit Acisi
 
  K2Off = 40              -- <PERSON><PERSON><PERSON> Offset
  K2Opr = 2              --Ya<PERSON><PERSON>ca<PERSON> islem 1-K_<PERSON><PERSON><PERSON>, 2-<PERSON>_<PERSON><PERSON>, 3-K_BalikSirti, 4-<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5-<PERSON>_<PERSON><PERSON><PERSON>, 6-<PERSON><PERSON><PERSON>2<PERSON>h = 4            -- <PERSON><PERSON>
  K2DiaAngle = 10 				-- Cap veya Vbit Acisi
  
  K3Off = 50              -- <PERSON><PERSON><PERSON> Offset
  K3Opr = 3              --<PERSON><PERSON><PERSON><PERSON><PERSON> islem 1-<PERSON>_<PERSON><PERSON><PERSON>, 2-<PERSON>_<PERSON><PERSON>, 3-<PERSON>_<PERSON><PERSON>, 4-<PERSON><PERSON>_<PERSON>c<PERSON>,5-K_AciliV, 6-SunkenV
  K3Depth = 6            -- Kanal Derinligi
  K3DiaAngle = 10 				-- Cap veya Vbit Acisi
  
  K4Off = 60              -- Kenardan Offset
  K4Opr = 4              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K4Depth = 5            -- Kanal Derinligi
  K4DiaAngle = 10 				-- Cap veya Vbit Acisi

  K5Off = 90             -- Kenardan Offset
  K5Opr = 4              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K5Depth = 5            -- Kanal Derinligi
  K5DiaAngle = 10 				-- Cap veya Vbit Acisi

  K6Off = 100             -- Kenardan Offset
  K6Opr = 6              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K6Depth = 6            -- Kanal Derinligi
  K6DiaAngle = 90 				-- Cap veya Vbit Acisi
  
  K7Off = 80              -- Kenardan Offset
  K7Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K7Depth = 7            -- Kanal Derinligi
  K7DiaAngle = 120 				-- Cap veya Vbit Acisi

  K8Off = 85              -- Kenardan Offset
  K8Opr = 0             --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K8Depth = 7            -- Kanal Derinligi
  K8DiaAngle = 150 				-- Cap veya Vbit Acisi
  
  KLineAvailable = 1
  KLineRotation = 1   --yatay:0, dikey:1
  KLineOffset = 60
  KLineDepth = 1.4
  
  --ARKA YUZEY ISLEMLER BASLA--
  
  KB1Off = 30              -- Kenardan Offset
  KB1Opr = 1              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  KB1Depth = 2           -- Kanal Derinligi
  KB1DiaAngle = 7.5 				-- Cap veya Vbit Acisi
   
  KB2Off = 40              -- Kenardan Offset
  KB2Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  KB2Depth = 4            -- Kanal Derinligi
  KB2DiaAngle = 10 				-- Cap veya Vbit Acisi
   
  KB3Off = 50              -- Kenardan Offset
  KB3Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  KB3Depth = 6            -- Kanal Derinligi
  KB3DiaAngle = 10 				-- Cap veya Vbit Acisi
  
 
  --ARKA YUZEY ISLEMLER SON--
  
  
  extEdgeVtoolExist       	= 0   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 0   -- Kapak kose Radusu var mi? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    
  local xMin = minimum
  local yMin = minimum
  
  function Decimals(W)
    W = string.gsub(W,"[.]",",")
    return W
  end
  
  function Kanallar(offset,operation,ad,W)	-- KENARDAN MESAFE, İSLEM, DERİNLİK, CAP VEYA ACI
	local sunkenWidth = ad * math.tan((math.pi*W/180)/2.0)
	local vMidDiameter = 100
	local distance = offset
	G.setThickness(-ad)
	point1 = {distance, distance}
	point2 = {X-distance, Y-distance}
  W=Decimals(W)
	
	if operation ==1 then
		G.setLayer("K_Freze"..W.."mm")		
		G.rectangle(point1,point2)
	end
	
	if operation ==2 then
		G.setLayer("K_Ballnose"..W.."mm")		
		G.rectangle(point1,point2)
	end
	
	if operation ==3 then
		G.setLayer("K_BalikSirti"..W.."mm")		
		G.rectangle(point1,point2)
	end
	
	if operation ==4 then
		G.setLayer("Cep_Acma")		
		G.rectangle(point1,point2)
	end
	
	if operation ==5 then
		G.setLayer("K_AciliV"..W)		
		G.rectangle(point1,point2)
	end
		
	if operation ==6 then
		G.setLayer("K_AciliV"..W)	
		G.setThickness(0)			
		G.sunkenFrame(point1, point2, ad, W, vMidDiameter)
	end
  
	if operation ==7 then
		G.setLayer("K_Desen"..W)		
		G.rectangle(point1,point2)
	end
 	--return
  end
   
  if KLineAvailable == 1 then
    G.setLayer("K_Cizgi")	
    G.setThickness(-KLineDepth)
    if KLineRotation == 0 then
      G.line({0, KLineOffset}, {X, KLineOffset})
      G.line({0, Y - KLineOffset}, {X, Y-KLineOffset})
    end
    if KLineRotation == 1 then
      G.line({KLineOffset, 0},{KLineOffset, Y})
      G.line({X-KLineOffset, 0}, {X-KLineOffset, Y})
    end
  end
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end

	Kanallar(K1Off,K1Opr,K1Depth,K1DiaAngle)
	if K2Opr>0 then
    if (X-2*K2Off)> 0 and (Y-2*K2Off)> 0 then
      Kanallar(K2Off,K2Opr,K2Depth,K2DiaAngle)
		end
    if K3Opr>0 then
      if (X-2*K3Off)> 0 and (Y-2*K3Off)> 0 then
        Kanallar(K3Off,K3Opr,K3Depth,K3DiaAngle)
			end
      if K4Opr>0 then
        if (X-2*K4Off)> 0 and (Y-2*K4Off)> 0 then
          Kanallar(K4Off,K4Opr,K4Depth,K4DiaAngle)
				end
        if K5Opr>0 then
            if (X-2*K5Off)> 0 and (Y-2*K5Off)> 0 then
            Kanallar(K5Off,K5Opr,K5Depth,K5DiaAngle)
					end
          if K6Opr>0 then
            if (X-2*K6Off)> 0 and (Y-2*K6Off)> 0 then
              Kanallar(K6Off,K6Opr,K6Depth,K6DiaAngle)
						end
            if K7Opr>0 then
              if (X-2*K7Off)> 0 and (Y-2*K7Off)> 0 then
                Kanallar(K7Off,K7Opr,K7Depth,K7DiaAngle)
							end
              if K8Opr>0 then
                if (X-2*K8Off)> 0 and (Y-2*K8Off)> 0 then
                  Kanallar(K8Off,K8Opr,K8Depth,K8DiaAngle)
                end
							end
						end
					end
				end
			end
		end
	end
	
	if KB1Opr>0 then
		G.setFace("bottom")
		if (X-2*KB1Off)> 0 and (Y-2*KB1Off)> 0 then
			Kanallar(KB1Off,KB1Opr,KB1Depth,KB1DiaAngle)
		end
		if KB2Opr>0 then
			if (X-2*KB2Off)> 0 and (Y-2*KB2Off)> 0 then
				Kanallar(KB2Off,KB2Opr,KB2Depth,KB2DiaAngle)
			end
			if KB3Opr>0 then
				if (X-2*KB3Off)> 0 and (Y-2*KB3Off)> 0 then
					Kanallar(KB3Off,KB3Opr,KB3Depth,KB3DiaAngle)
				end
			end
		end
	end
    
  return true
end


require "ADekoDebugMode"
