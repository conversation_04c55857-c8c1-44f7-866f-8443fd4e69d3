-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  xMin = 150
  yMin = 150
  xLimit = 350
  yLimit = 350
  
  a = 50                    -- Kenardan Vbite
  aa = 20					-- Dar kapak için Kenardan mesafe
  ad = 6                    -- Vbit derinliği (sunkenDepth)
  aV = 135                  -- Vbit Açısı
  local cW = 20                   -- Tarama Bıçak Çapı
  cT = 6                   -- <PERSON>nce bıçak çapı (Köşe Temizleme)
  
  extEdgeRtoolExist      		= 6   -- <PERSON><PERSON>ş kenar Pah işlemi var mı? derinlik/0:yok
  edgeCornerRExist       		= 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    
  local b = 0                    -- Vbitten Dıs kanala
  local d = 20              		-- Göbekten iç kanala veya göbek vbit üstü kanal
  local h = 25                    -- Vbitten göbeğe
  --bd = 3                    -- dış kanal derinliği
  --local hd = 5              -- Göbek Desen Bıcak derinliği
  --local dd = 2              -- iç kanal derinliği
  	
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
	

  local extGrooveExist          	= 0   -- Dış kanal ve-veya V bit üstü var mı? derinlik/0:yok
  local intGrooveExist        		= 0   -- Göbekte vbit üstü veya içeride kanal var mı? derinlik/ 0:yok
  local shapeToolExist 				= 0   -- Göbek Desen bıçağı var mı? derinlik/0:yok
  local cabCoreExist            = false   -- Göbek var mı? Cam varsa buna göre bişeyler yapılabilir
  
  local D = edgeCornerRExist
  
  
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)     --indiği derinlikte yarısı oluyor
    
  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  minXCalculated = 2*a + 2*sunkenWidth + 2*h
  minYCalculated = 2*ust + 2*sunkenWidth + 2*h

  if cW <= 0 then
    cW = cT
  end
  
  if X<minXCalculated or Y<minYCalculated then
    print("Part dimension too small")
    return true
  end
  
  
  if extGrooveExist > 0 then
    G.setLayer("K_Freze"..cT.."mm")  -- DEEP cleanup
    G.setThickness(-extGrooveExist)
    distance = a-b + cT/2
    distance2 = ust-b + cT/2
    point1 = {distance, distance2}
    point2 = {X-distance, Y-distance2}
    G.rectangle(point1,point2)
  end
  
    
	G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	G.setThickness(0)
	local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	corner1[3] = 0
	corner2[3] = 0
	
	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
  
	if cabCoreExist then
	
		if shapeToolExist > 0 then  ----Göbekte desen bıçağı varsa
			G.setLayer("K_Desen")  -- 
			G.setThickness(-shapeToolExist)
			distance = a + sunkenWidth + h
			distance1 = ust + sunkenWidth + h
			point1 = {distance, distance1}
			point2 = {X-distance, Y-distance1}
			G.rectangle(point1,point2)
		else 
			G.setLayer("K_AciliV" .. aV)  --- Gobek
			G.setThickness(-ad)
			distance = a + sunkenWidth + h
			distance1 = ust + sunkenWidth + h
			point1 = {distance, distance1}
			point2 = {X-distance, Y-distance1}
			G.rectangle(point1,point2)
		end
	
		G.setLayer("K_Freze"..cW.."mm")  -- DEEP cleanup
		G.setThickness(-ad)
		distance1 = a+sunkenWidth+cW/2
		distance2 = ust+sunkenWidth+cW/2
		point1 = {distance1, distance2}
		point2 = {X-distance1, Y-distance2}
		G.rectangle(point1,point2)
	
	
		if h<cT or h<cW then
			print("Tool too large")
			return true
		end
	
		if h>cW then
			distance = a+sunkenWidth+h-cW/2
			distance2 = ust+sunkenWidth+h-cW/2
			point3 = {distance, distance2}
			point4 = {X-distance, Y-distance2}
			--G.rectangle(point3,point4)
			k = (h-cW)/(cW/2)
			for i=1, k, 1 do
				point1 = G.ptAdd(point1,{cW/2,cW/2})
				point2 = G.ptSubtract(point2, {cW/2,cW/2})
				if point1[1]>point3[1]-cW/2 then
					break
				end
			G.rectangle(point1,point2)
			end
		end
	
	
		if h ~= cW then
			distance = a+sunkenWidth+h-cW/2
			distance2 = ust+sunkenWidth+h-cW/2
			point10 = {distance, distance2}
			point11 = {X-distance, Y-distance2}
			G.rectangle(point10,point11)
		end
	
		if intGrooveExist > 0 then      ------İç kanal varsa
			local checkX = 2*a + 2*h + 2*d
			local checkY = 2*ust + 2*h + 2*d
			if X < checkX or Y < checkY then
				print("Part dimension too small, check a + h + d value")
				return true
			end
			G.setLayer("K_Freze"..cT.."mm") --içteki göbek vbit üstü kanal
			G.setThickness(-intGrooveExist)
			distance = a + sunkenWidth + h + d
			distance2 = ust + sunkenWidth + h + d
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)

		end
	else
	
		G.setLayer("Cep_Acma")  -- DEEP cleanup
		G.setThickness(-ad)
		G.rectangle(corner1,corner2)
    --distance = a + sunkenWidth
		--distance2 = ust + sunkenWidth
		--point6 = {distance, distance2}
		--point7 = {X-distance, Y-distance2}
		--G.rectangle(point6,point7)

	end
	
	G.setLayer("K_TarKoseTemizl"..cT.."mm")
	G.setThickness(0)
	G.cleanCorners(corner1,corner2,ad,cT)
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
