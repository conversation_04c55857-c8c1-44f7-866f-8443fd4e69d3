colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Dark (vibrant-dark)
colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Light (vibrant-light)
colorfulThemeService.ts:190 ✅ Registered theme: Neon Dark (neon-dark)
ocjsService.ts:74 OC.js Worker initialized
colorfulThemeService.ts:236 Colorful themes initialized: Array(3)
App.vue:1060 Adeko Lua Editörü başlatıldı
App.vue:1074 Created welcome file: Untitled
EditorGroup.vue:102 Active file in group: group-1751788406709-95c9ieqcd File: Untitled Content length: 48
Editor.vue:66 Initializing Monaco Editor with content: -- <PERSON><PERSON>ı
-- <PERSON><PERSON><PERSON><PERSON> buraya ekleyin

...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- <PERSON><PERSON>
-- <PERSON><PERSON><PERSON><PERSON> bura<PERSON> ekleyin


Editor.vue:259 Container dimensions: Object
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: Array(1)
Editor.vue:299 Line 2 tokens: Array(1)
Editor.vue:299 Line 3 tokens: Array(1)
Editor.vue:299 Line 4 tokens: Array(1)
ocjsWorker.ts:835 Worker received message: createDoorBody
ocjsWorker.ts:77 OpenCascade.js initialized in worker
ocjsWorker.ts:93 Creating box with dimensions: 200 150 18
ocjsWorker.ts:97 🚪 Door body dimensions: W=200m, H=150m, T=18m
ocjsWorker.ts:104 🚪 Door body centered at origin: X=[-100, 100], Y=[-75, 75], Z=[-9, 9]
ocjsWorker.ts:109 ✅ Door body cached with ID: door_1751788447433
ocjsWorker.ts:872 Worker completed: createDoorBody
ocjsWorker.ts:835 Worker received message: exportGLB
ocjsWorker.ts:609 🔧 Exporting to GLB...
ocjsWorker.ts:634 🔍 Exporting final shape to GLB format
ocjsWorker.ts:660 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:872 Worker completed: exportGLB
ocjsWorker.ts:835 Worker received message: createDoorBody
ocjsWorker.ts:93 Creating box with dimensions: 100 100 18
ocjsWorker.ts:97 🚪 Door body dimensions: W=100m, H=100m, T=18m
ocjsWorker.ts:104 🚪 Door body centered at origin: X=[-50, 50], Y=[-50, 50], Z=[-9, 9]
ocjsWorker.ts:109 ✅ Door body cached with ID: door_1751788458738
ocjsWorker.ts:872 Worker completed: createDoorBody
ocjsWorker.ts:835 Worker received message: createPositionedToolShapes
ocjsWorker.ts:223 🔧 Tool 8MM: diameter=8mm, radius=0.004m (4mm)
ocjsWorker.ts:225 🔧 Creating 1 positioned cylindrical tool shapes for 8MM
ocjsWorker.ts:321 ✅ Created 1 positioned tool shapes
ocjsWorker.ts:872 Worker completed: createPositionedToolShapes
ocjsWorker.ts:835 Worker received message: performSweepOperation
ocjsWorker.ts:486 🔧 Starting sweep operation: subtract
ocjsWorker.ts:505 🔧 Processing 1 tool geometries
ocjsWorker.ts:508 🔍 Starting CSG operations on door body
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_8MM_0_1751788458745_1jw5fx8k4
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 0 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:551 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:587 ✅ Sweep operation completed, result cached with ID: result_1751788459032
ocjsWorker.ts:588 📊 Successfully processed 1 out of 1 tools
ocjsWorker.ts:872 Worker completed: performSweepOperation
ocjsWorker.ts:835 Worker received message: exportGLB
ocjsWorker.ts:609 🔧 Exporting to GLB...
ocjsWorker.ts:634 🔍 Exporting final shape to GLB format
ocjsWorker.ts:660 ✅ GLB export completed, size: 7168 bytes
ocjsWorker.ts:872 Worker completed: exportGLB
ocjsWorker.ts:835 Worker received message: createDoorBody
ocjsWorker.ts:93 Creating box with dimensions: 150 100 18
ocjsWorker.ts:97 🚪 Door body dimensions: W=150m, H=100m, T=18m
ocjsWorker.ts:104 🚪 Door body centered at origin: X=[-75, 75], Y=[-50, 50], Z=[-9, 9]
ocjsWorker.ts:109 ✅ Door body cached with ID: door_1751788469546
ocjsWorker.ts:872 Worker completed: createDoorBody
ocjsWorker.ts:835 Worker received message: createPositionedToolShapes
ocjsWorker.ts:223 🔧 Tool 6MM: diameter=6mm, radius=0.003m (3mm)
ocjsWorker.ts:225 🔧 Creating 4 positioned cylindrical tool shapes for 6MM
ocjsWorker.ts:321 ✅ Created 4 positioned tool shapes
ocjsWorker.ts:872 Worker completed: createPositionedToolShapes
ocjsWorker.ts:835 Worker received message: performSweepOperation
ocjsWorker.ts:486 🔧 Starting sweep operation: subtract
ocjsWorker.ts:505 🔧 Processing 4 tool geometries
ocjsWorker.ts:508 🔍 Starting CSG operations on door body
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_6MM_0_1751788469550_y4dbio0cq
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 0 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:551 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_6MM_1_1751788469552_70ri88m2y
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 1 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:551 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_6MM_2_1751788469552_ygw2acnz0
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 2 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:551 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_6MM_3_1751788469552_vh8cwcxo3
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 3 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 3...
ocjsWorker.ts:551 ✅ Tool 3 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:587 ✅ Sweep operation completed, result cached with ID: result_1751788470105
ocjsWorker.ts:588 📊 Successfully processed 4 out of 4 tools
ocjsWorker.ts:872 Worker completed: performSweepOperation
ocjsWorker.ts:835 Worker received message: exportGLB
ocjsWorker.ts:609 🔧 Exporting to GLB...
ocjsWorker.ts:634 🔍 Exporting final shape to GLB format
ocjsWorker.ts:660 ✅ GLB export completed, size: 6388 bytes
ocjsWorker.ts:872 Worker completed: exportGLB
ocjsWorker.ts:835 Worker received message: createDoorBody
ocjsWorker.ts:93 Creating box with dimensions: 200 150 20
ocjsWorker.ts:97 🚪 Door body dimensions: W=200m, H=150m, T=20m
ocjsWorker.ts:104 🚪 Door body centered at origin: X=[-100, 100], Y=[-75, 75], Z=[-10, 10]
ocjsWorker.ts:109 ✅ Door body cached with ID: door_1751788491929
ocjsWorker.ts:872 Worker completed: createDoorBody
ocjsWorker.ts:835 Worker received message: createPositionedToolShapes
ocjsWorker.ts:223 🔧 Tool 8MM: diameter=8mm, radius=0.004m (4mm)
ocjsWorker.ts:225 🔧 Creating 1 positioned cylindrical tool shapes for 8MM
ocjsWorker.ts:321 ✅ Created 1 positioned tool shapes
ocjsWorker.ts:872 Worker completed: createPositionedToolShapes
ocjsWorker.ts:835 Worker received message: createPositionedToolShapes
ocjsWorker.ts:223 🔧 Tool 6MM: diameter=6mm, radius=0.003m (3mm)
ocjsWorker.ts:225 🔧 Creating 1 positioned cylindrical tool shapes for 6MM
ocjsWorker.ts:321 ✅ Created 1 positioned tool shapes
ocjsWorker.ts:872 Worker completed: createPositionedToolShapes
ocjsWorker.ts:835 Worker received message: createPositionedToolShapes
ocjsWorker.ts:223 🔧 Tool 4MM: diameter=4mm, radius=0.002m (2mm)
ocjsWorker.ts:225 🔧 Creating 1 positioned cylindrical tool shapes for 4MM
ocjsWorker.ts:321 ✅ Created 1 positioned tool shapes
ocjsWorker.ts:872 Worker completed: createPositionedToolShapes
ocjsWorker.ts:835 Worker received message: performSweepOperation
ocjsWorker.ts:486 🔧 Starting sweep operation: subtract
ocjsWorker.ts:505 🔧 Processing 3 tool geometries
ocjsWorker.ts:508 🔍 Starting CSG operations on door body
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_8MM_0_1751788491932_ibmi4h915
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 0 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 0...
ocjsWorker.ts:551 ✅ Tool 0 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_6MM_0_1751788491933_2f416zg4f
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 1 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 1...
ocjsWorker.ts:551 ✅ Tool 1 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:519 🔍 Looking for tool shape ID: positioned_tool_4MM_0_1751788491934_ap8dvp282
ocjsWorker.ts:520 🔍 Found in toolCache: true
ocjsWorker.ts:521 🔍 Found in shapeCache: false
ocjsWorker.ts:538 ✅ Found tool shape 2 for operation
ocjsWorker.ts:541 🔧 Attempting to subtract tool 2...
ocjsWorker.ts:551 ✅ Tool 2 subtracted successfully
ocjsWorker.ts:555 📊 Boolean operation completed - result shape is valid
ocjsWorker.ts:587 ✅ Sweep operation completed, result cached with ID: result_1751788492132
ocjsWorker.ts:588 📊 Successfully processed 3 out of 3 tools
ocjsWorker.ts:872 Worker completed: performSweepOperation
ocjsWorker.ts:835 Worker received message: exportGLB
ocjsWorker.ts:609 🔧 Exporting to GLB...
ocjsWorker.ts:634 🔍 Exporting final shape to GLB format
ocjsWorker.ts:660 ✅ GLB export completed, size: 9928 bytes
ocjsWorker.ts:872 Worker completed: exportGLB
ocjsWorker.ts:835 Worker received message: createDoorBody
ocjsWorker.ts:93 Creating box with dimensions: 200 150 20
ocjsWorker.ts:97 🚪 Door body dimensions: W=200m, H=150m, T=20m
ocjsWorker.ts:104 🚪 Door body centered at origin: X=[-100, 100], Y=[-75, 75], Z=[-10, 10]
ocjsWorker.ts:109 ✅ Door body cached with ID: door_1751788494421
ocjsWorker.ts:872 Worker completed: createDoorBody
ocjsWorker.ts:835 Worker received message: exportGLB
ocjsWorker.ts:609 🔧 Exporting to GLB...
ocjsWorker.ts:634 🔍 Exporting final shape to GLB format
ocjsWorker.ts:660 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:872 Worker completed: exportGLB
ocjsWorker.ts:835 Worker received message: createDoorBody
ocjsWorker.ts:93 Creating box with dimensions: 200 150 20
ocjsWorker.ts:97 🚪 Door body dimensions: W=200m, H=150m, T=20m
ocjsWorker.ts:104 🚪 Door body centered at origin: X=[-100, 100], Y=[-75, 75], Z=[-10, 10]
ocjsWorker.ts:109 ✅ Door body cached with ID: door_1751788496179
ocjsWorker.ts:872 Worker completed: createDoorBody
ocjsWorker.ts:835 Worker received message: exportGLB
ocjsWorker.ts:609 🔧 Exporting to GLB...
ocjsWorker.ts:634 🔍 Exporting final shape to GLB format
ocjsWorker.ts:660 ✅ GLB export completed, size: 3232 bytes
ocjsWorker.ts:872 Worker completed: exportGLB
