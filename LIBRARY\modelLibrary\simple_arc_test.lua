-- Simple Arc Test Script
-- This script creates basic arcs to test the angle fix

print("=== Simple Arc Test ===")

-- Load the new engine
local makerjs_engine = require("LIBRARY.luaLibrary.new_engine.makerjs_engine")
local ADekoLib = require("LIBRARY.luaLibrary.new_engine.ADekoLib")

-- Set up the engine
ADekoLib.engine = makerjs_engine

-- Create a test model
makerjs_engine.model_def("simple_arc_test", function()
    
    -- Test 1: Quarter arc from 0° to 90°
    print("Creating quarter arc from 0° to 90°")
    makerjs_engine.layer("quarter_arc")
    makerjs_engine.arc("quarter", 100, 100, 50, 0, 90, false)
    
    -- Test 2: Half arc from 0° to 180°
    print("Creating half arc from 0° to 180°")
    makerjs_engine.layer("half_arc")
    makerjs_engine.arc("half", 250, 100, 50, 0, 180, false)
    
    -- Test 3: Reference circles for comparison
    print("Creating reference circles")
    makerjs_engine.layer("reference")
    makerjs_engine.circle("ref1", 100, 100, 50)
    makerjs_engine.circle("ref2", 250, 100, 50)
    
    -- Test 4: Using ADekoLib arc function
    print("Creating ADekoLib arcs")
    ADekoLib.setFace("top")
    ADekoLib.setLayer("adeko_arcs")
    ADekoLib.arc({400, 100}, 50, 0, 90, false, "adeko_quarter")
    
end)

-- Export the model
local json_output = makerjs_engine.export_model("simple_arc_test")

-- Save to file
local file = io.open("simple_arc_test_output.json", "w")
if file then
    file:write(json_output)
    file:close()
    print("✓ Simple arc test model exported to simple_arc_test_output.json")
else
    print("✗ Failed to save output file")
end

print("=== Simple Arc Test Complete ===")
