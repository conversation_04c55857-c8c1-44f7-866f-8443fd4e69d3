{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Acili_aV \t\tAcili V bicagi \nH_Raduslu_Pah_DIS\tRaduslu pah bicagi \nK_Freze_cT_mm\t\t cT Capli Freze bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar kapakda acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 0, "description": "DIS vbit Ustu kanal Kaydirma mesafesi", "parameterName": "b"}, {"defaultValue": 20, "description": "<PERSON><PERSON> kisminin yuk<PERSON>", "parameterName": "yy"}, {"defaultValue": 50, "description": "<PERSON><PERSON><PERSON> a<PERSON>i me<PERSON>", "parameterName": "gaps"}, {"defaultValue": 6, "description": "Vbit Ustu kanal capi", "parameterName": "cT"}, {"defaultValue": 0, "description": "Ic Dik kanallar var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intVerticalGrooveExist"}, {"defaultValue": 4, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeRtoolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}