{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Kanal*cT*mm\t\t *cT*mm Freze bicagi \nK_AciliV_Pah\t                                        Pah bicagi  \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "Acili V ustu kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 60, "description": "Ic kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 20, "description": "<PERSON><PERSON><PERSON> ile ic kenar arasi kaydirma miktari", "parameterName": "sbt"}, {"defaultValue": 2, "description": "Kanal cizgileri var mi? (Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 2, "description": "Ic yatay-dikey kanal islemi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "intGrvExist"}, {"defaultValue": 1, "description": "Ic kanallar yatay mi?-dikey mi? (Dikey-Vert:1/Yatay-<PERSON>r:0) ", "parameterName": "<PERSON><PERSON><PERSON>_<PERSON>"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}