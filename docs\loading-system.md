# Loading System Documentation

The Adeko Lua Editor now includes a comprehensive loading system to improve user experience during application startup and long-running operations.

## Features

### 1. Application Startup Loader
- **Automatic Display**: Shows during app initialization
- **Progress Tracking**: Real-time progress updates for each initialization step
- **Smooth Animations**: Professional loading animations with floating logo
- **Minimum Display Time**: Ensures loader is visible for at least 2 seconds to prevent flashing

### 2. Loading Steps
The application initialization includes these steps:
1. **Initialization** - Basic app setup
2. **Themes** - Loading theme services
3. **Internationalization** - Setting up i18n
4. **Settings** - Loading user settings
5. **Monaco Editor** - Initializing code editor
6. **Editor Workspace** - Setting up editor components
7. **Services** - Starting background services

### 3. Visual Design
- **Modern Gradient Background**: Dark theme with subtle gradients
- **Animated Logo**: Floating animation with gradient styling
- **Progress Bar**: Smooth progress indication with glow effects
- **Status Messages**: Clear status updates with animated dots
- **Professional Typography**: Clean, readable fonts

## Implementation

### Core Components

#### 1. LoadingService (`src/services/loadingService.ts`)
Manages the main application loading screen:

```typescript
import { loadingService } from '@/services/loadingService'

// Start a loading step
loadingService.startStep('themes')

// Update progress
loadingService.updateStepProgress('themes', 50)

// Complete a step
loadingService.completeStep('themes')

// Run async operation with progress
await loadingService.simulateAsyncOperation('settings', async () => {
  await loadSettings()
}, 1000)
```

#### 2. AppLoader Component (`src/components/AppLoader.vue`)
Reusable loading component for other parts of the app:

```vue
<template>
  <AppLoader
    :visible="isLoading"
    title="Processing Files"
    subtitle="Please wait..."
    :status-message="statusMessage"
    :progress="progressValue"
    :show-progress="true"
    @hidden="isLoading = false"
  />
</template>
```

#### 3. useLoading Composable (`src/composables/useLoading.ts`)
Vue composable for component-level loading states:

```typescript
import { useLoading } from '@/composables/useLoading'

const { isLoading, progress, withLoading } = useLoading()

// Wrap async operations
await withLoading(
  async () => {
    // Your async operation
  },
  {
    message: 'Loading data...',
    duration: 2000,
    showProgress: true
  }
)
```

### Usage Examples

#### Basic Loading
```typescript
const { startLoading, stopLoading, updateProgress } = useLoading()

startLoading({ message: 'Loading...' })
updateProgress(50, 'Processing...')
stopLoading()
```

#### Multi-step Operations
```typescript
const { withSteps } = useLoading()

await withSteps([
  {
    name: 'Connecting to server',
    operation: async () => await connect(),
    duration: 1000
  },
  {
    name: 'Fetching data',
    operation: async () => await fetchData(),
    duration: 2000
  },
  {
    name: 'Processing results',
    operation: async () => await process(),
    duration: 500
  }
])
```

#### Error Handling
```typescript
const { setError } = useLoading()

try {
  await riskyOperation()
} catch (error) {
  setError('Failed to complete operation')
}
```

## Customization

### Styling
The loader can be customized through CSS variables and component props:

```vue
<AppLoader
  title="Custom Title"
  subtitle="Custom subtitle"
  icon-text="🚀"
  icon-class="custom-icon"
  :is-overlay="true"
/>
```

### Custom Icons
Replace the default "L" icon with custom content:

```vue
<AppLoader>
  <template #icon>
    <img src="/custom-logo.png" alt="Logo" />
  </template>
</AppLoader>
```

### Additional Content
Add custom content below the progress bar:

```vue
<AppLoader>
  <template #content>
    <div class="custom-actions">
      <button @click="cancel">Cancel</button>
    </div>
  </template>
</AppLoader>
```

## Configuration

### Minimum Load Time
Adjust the minimum display time in `main.ts`:

```typescript
await loadingService.addMinimumLoadTime(async () => {
  // initialization code
}, 3000) // 3 seconds minimum
```

### Step Durations
Customize individual step durations:

```typescript
await loadingService.simulateAsyncOperation('themes', async () => {
  // operation
}, 1500) // 1.5 seconds
```

## Best Practices

1. **Always show progress** for operations longer than 1 second
2. **Use meaningful messages** that describe what's happening
3. **Handle errors gracefully** with clear error messages
4. **Test loading states** to ensure smooth user experience
5. **Avoid flashing** by using minimum display times for quick operations

## Browser Compatibility

The loading system works in all modern browsers and includes:
- CSS animations and transitions
- Flexbox layouts
- CSS gradients
- Modern JavaScript features

## Performance

- **Lightweight**: Minimal impact on app startup time
- **Efficient**: Uses CSS animations instead of JavaScript
- **Memory-friendly**: Cleans up resources after use
- **Responsive**: Adapts to different screen sizes
