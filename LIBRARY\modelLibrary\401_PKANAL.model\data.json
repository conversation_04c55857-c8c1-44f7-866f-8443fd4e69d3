{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV_aV \t\tAcili V bicagi \nK_Form\t\t Ic kanal islemi icin kullanilacak bir bicak raduslu form bicak \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "Cizgiler arasi bosluk(Yaklasik olarak)", "parameterName": "gaps"}, {"defaultValue": 2, "description": "Ic kanal var mi?-Form- (Var :Derinlik/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}