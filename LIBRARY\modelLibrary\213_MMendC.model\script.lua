-- ADek<PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  marginX = 20
  marginY = 20
  structureSizeX = 30
  structureSizeY = 30
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local g = 12
  local firstDepth = 4
  local finalDepth = 8
  local notchDepth = 2
  local vWideAngle = 120
  local vWideDiameter = 60
  local cThinDiameter = 5
  
  G.setLayer("K_AciliV45")
  G.setThickness(-notchDepth)
  G.menderesC(marginX, marginY, structureSizeX, structureSizeY)
  G.rectangle({marginX/2, marginY/2}, {X-marginX/2, Y-marginY/2})
  G.rectangle({marginX+structureSizeX, marginY+structureSizeY}, {X-(marginX+structureSizeX), Y-(marginY+structureSizeY)})
  
  G.setLayer("K_AciliV30")
  G.setThickness(0)
  distanceX = marginX+structureSizeX+g
  distanceY = marginY+structureSizeY+g
  point1 = {distanceX,distanceY}
  point2 = {X-distanceX,Y-distanceY}
  local corner1, corner2 = G.sunkenFrame(point1, point2, firstDepth, vWideAngle, vWideDiameter)
        corner1, corner2 = G.sunkenFrame(corner1, corner2, firstDepth, vWideAngle, vWideDiameter)
        corner1[3] = 0
        corner2[3] = 0
  
  G.setLayer("Cep_Acma")
  G.setThickness(-finalDepth)
  G.rectangle(corner1, corner2)
  
  G.setLayer("K_TarKoseTemizl".."5mm")
  G.setThickness(0)
  G.cleanCorners(corner1, corner2, finalDepth, cThinDiameter)
  
  return true
end

require "ADekoDebugMode"
