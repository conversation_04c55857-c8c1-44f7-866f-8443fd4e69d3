-- ADekoCAM, Model Script - Dynamic Horizontal Dividers Test
-- Converted from C# azCAM macro: Dynamic Horizontal.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 400
  Y = Y or 600
  materialThickness = materialThickness or 18
  
  -- Dynamic Horizontal Dividers parameters (from original C# macro)
  A = 50    -- Left/right margin
  B = 50    -- Top/bottom margin  
  C = 10    -- Gap between dividers
  dividerY = 50  -- Height of each divider (~Y parameter from C#)
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * A + 20  -- minimum required width
  local minHeight = 2 * B + dividerY + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Dynamic Horizontal Dividers function (converted from C# macro)
  local function dynamic_horizontal_dividers(A, B, C, dividerY, Z)
    -- Use default values if parameters not provided
    A = A or 50
    B = B or 50
    C = C or 10
    dividerY = dividerY or 50
    Z = Z or -5
    
    -- Calculate number of dividers that can fit
    -- N = Floor((height-2*B+C) / (dividerY+C))
    local N = math.floor((height - 2*B + C) / (dividerY + C))
    
    if N <= 0 then
      print("No dividers can fit with current parameters")
      return false
    end
    
    -- Calculate dimensions
    local mX = width - 2*A  -- Available width for dividers
    local mY = (height - 2*B - (N-1)*C) / N  -- Actual height of each divider
    
    print(string.format("Creating %d horizontal dividers", N))
    print(string.format("Each divider: %.1f x %.1f mm", mX, mY))
    
    -- Set layer and thickness for dividers
    G.setLayer("K_Freze10mm")
    G.setThickness(Z)
    
    -- Create each divider
    for i = 0, N-1 do
      -- Calculate position for this divider
      local rectPosX = A + mX/2  -- Center X position
      local rectPosY = B + (i * mY) + (i * C) + mY/2  -- Center Y position
      
      -- Create rectangle for this divider
      -- Rectangle corners relative to center
      local x1 = rectPosX - mX/2
      local y1 = rectPosY - mY/2
      local x2 = rectPosX + mX/2
      local y2 = rectPosY + mY/2
      
      -- Create the rectangle using polyline
      G.polyline(
        {x1, y1},  -- bottom-left
        {x2, y1},  -- bottom-right
        {x2, y2},  -- top-right
        {x1, y2},  -- top-left
        {x1, y1}   -- close the path
      )
      
      -- Start new shape for next divider
      if i < N-1 then
        G.nextShape()
      end
      
      print(string.format("Divider %d: center at (%.1f, %.1f)", i+1, rectPosX, rectPosY))
    end
    
    return true
  end
  
  -- Call the dynamic horizontal dividers function
  local success = dynamic_horizontal_dividers(A, B, C, dividerY, -Z)
  
  if success then
    print(string.format("Dynamic horizontal dividers created with parameters:"))
    print(string.format("  A (margins): %d mm", A))
    print(string.format("  B (top/bottom): %d mm", B))
    print(string.format("  C (gap): %d mm", C))
    print(string.format("  Divider height: %d mm", dividerY))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Door size: %dx%d mm", X, Y))
  end
  
  return true
end

require "ADekoDebugMode"
