<template>
  <div class="graphics-canvas-container">
    <canvas
      ref="canvasRef"
      class="graphics-canvas"
      @wheel="handleWheel"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
    ></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'


interface DrawCommand {
  command_type: string
  x1: number
  y1: number
  x2: number
  y2: number
  z1?: number // Z coordinate for start point of 3D lines
  z2?: number // Z coordinate for end point of 3D lines
  radius: number
  color: string
  size: number
  text: string
  layer_name: string
  thickness?: number // Thickness/extrusion for 3D representation
  start_angle?: number // Start angle for arcs in degrees
  end_angle?: number   // End angle for arcs in degrees
  clockwise?: boolean  // Arc direction
  svg_path?: string    // SVG path data for complex shapes
  points?: number[][]  // Array of [x, y, z, bulge] points for polylines/polygons
}

interface Props {
  drawCommands: DrawCommand[]
}

const props = defineProps<Props>()

const canvasRef = ref<HTMLCanvasElement>()
const scale = ref(1)
const offsetX = ref(0)
const offsetY = ref(0)
const isDragging = ref(false)
const lastMouseX = ref(0)
const lastMouseY = ref(0)

const resetView = () => {
  scale.value = 1
  offsetX.value = 100  // Move view right to show positive X
  offsetY.value = 700  // Move view up to show positive Y
  drawCanvas()
}

const zoomIn = () => {
  scale.value = Math.min(scale.value * 1.2, 5)
  drawCanvas()
}

const zoomOut = () => {
  scale.value = Math.max(scale.value / 1.2, 0.1)
  drawCanvas()
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? 0.9 : 1.1
  scale.value = Math.max(0.1, Math.min(5, scale.value * delta))
  drawCanvas()
}

const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
}

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - lastMouseX.value
  const deltaY = event.clientY - lastMouseY.value
  
  offsetX.value += deltaX
  offsetY.value += deltaY
  
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY
  
  drawCanvas()
}

const handleMouseUp = () => {
  isDragging.value = false
}

const drawCanvas = () => {
  console.log('drawCanvas called with', props.drawCommands.length, 'commands')
  const canvas = canvasRef.value
  if (!canvas) {
    console.log('No canvas ref, retrying in 100ms...')
    // Retry after a short delay to allow the canvas to mount
    setTimeout(() => {
      if (canvasRef.value) {
        drawCanvas()
      } else {
        console.log('Canvas ref still not available after retry')
        // Try one more time after a longer delay
        setTimeout(() => {
          if (canvasRef.value) {
            drawCanvas()
          } else {
            console.warn('Canvas ref permanently unavailable - component may not be mounted')
          }
        }, 500)
      }
    }, 100)
    return
  }

  const ctx = canvas.getContext('2d')
  if (!ctx) {
    console.log('No canvas context')
    return
  }

  // Set canvas size
  const rect = canvas.parentElement?.getBoundingClientRect()
  if (rect) {
    canvas.width = rect.width
    canvas.height = rect.height
    console.log('Canvas size set to:', rect.width, 'x', rect.height)
  }

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // Set up coordinate system (ADekoLib-compatible origin)
  ctx.save()

  // Check if we have ADekoLib-style face layout (door schemas)
  const hasADekoLibLayout = props.drawCommands.some(cmd =>
    cmd.command_type === 'text' &&
    ['Left', 'Right', 'Top', 'Bottom', 'Front', 'Rear'].includes(cmd.text)
  )

  if (hasADekoLibLayout) {
    // ADekoLib coordinate system: origin at bottom-left, Y+ up
    ctx.translate(offsetX.value, canvas.height - offsetY.value)
    ctx.scale(scale.value, -scale.value)  // Flip Y axis
  } else {
    // Standard coordinate system: origin at top-left, Y+ down
    ctx.translate(offsetX.value, offsetY.value)
    ctx.scale(scale.value, scale.value)
  }

  // Draw grid
  drawGrid(ctx, canvas.width, canvas.height)

  // Draw face layout guides (ZeroBrane-style)
  drawFaceLayoutGuides(ctx)

  // Draw commands
  console.log('Drawing', props.drawCommands.length, 'commands')
  props.drawCommands.forEach(command => {
    drawCommand(ctx, command)
  })

  ctx.restore()
  console.log('drawCanvas completed')
}

const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
  const gridSize = 20

  // Check if we have ADekoLib-style face layout
  const hasADekoLibLayout = props.drawCommands.some(cmd =>
    cmd.command_type === 'text' &&
    ['Left', 'Right', 'Top', 'Bottom', 'Front', 'Rear'].includes(cmd.text)
  )

  ctx.save()
  ctx.strokeStyle = '#e5e7eb'
  ctx.lineWidth = 1 / scale.value
  ctx.setLineDash([])

  // Calculate grid bounds based on current view
  const startX = Math.floor(-offsetX.value / scale.value / gridSize) * gridSize
  const endX = Math.ceil((width - offsetX.value) / scale.value / gridSize) * gridSize
  const startY = Math.floor(-offsetY.value / scale.value / gridSize) * gridSize
  const endY = Math.ceil((height - offsetY.value) / scale.value / gridSize) * gridSize

  // Draw vertical lines
  for (let x = startX; x <= endX; x += gridSize) {
    ctx.beginPath()
    ctx.moveTo(x, startY)
    ctx.lineTo(x, endY)
    ctx.stroke()
  }

  // Draw horizontal lines
  for (let y = startY; y <= endY; y += gridSize) {
    ctx.beginPath()
    ctx.moveTo(startX, y)
    ctx.lineTo(endX, y)
    ctx.stroke()
  }

  // Draw axes
  ctx.strokeStyle = '#9ca3af'
  ctx.lineWidth = 2 / scale.value

  // X-axis
  ctx.beginPath()
  ctx.moveTo(startX, 0)
  ctx.lineTo(endX, 0)
  ctx.stroke()

  // Y-axis
  ctx.beginPath()
  ctx.moveTo(0, startY)
  ctx.lineTo(0, endY)
  ctx.stroke()

  // Draw origin marker
  ctx.fillStyle = '#ef4444'
  const markerSize = 4 / scale.value
  ctx.fillRect(-markerSize/2, -markerSize/2, markerSize, markerSize)

  // Add coordinate labels
  ctx.fillStyle = '#6b7280'
  ctx.font = `${12 / scale.value}px Arial`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  if (hasADekoLibLayout) {
    // ADekoLib coordinate system labels
    ctx.fillText('(0,0)', 10 / scale.value, -10 / scale.value)
    ctx.fillText('X+', 50 / scale.value, -10 / scale.value)
    ctx.fillText('Y+', -10 / scale.value, 50 / scale.value)
  } else {
    // Standard coordinate system labels
    ctx.fillText('(0,0)', 10 / scale.value, 10 / scale.value)
    ctx.fillText('X+', 50 / scale.value, 10 / scale.value)
    ctx.fillText('Y+', -10 / scale.value, -50 / scale.value)
  }

  ctx.restore()
}

const drawFaceLayoutGuides = (ctx: CanvasRenderingContext2D) => {
  // Draw ZeroBrane-style face layout guides if we detect face layout commands
  const hasTextCommands = props.drawCommands.some(cmd =>
    cmd.command_type === 'text' &&
    ['Left', 'Right', 'Top', 'Bottom', 'Front', 'Rear'].includes(cmd.text)
  )

  if (hasTextCommands) {
    ctx.strokeStyle = '#ffd93d'
    ctx.lineWidth = 1
    ctx.setLineDash([5, 5])

    // Draw face boundary indicators based on ADekoDebugMode.lua layout
    // Using the exact coordinates from ADekoDebugMode.lua with offset=20, materialThickness=18
    const offset = 20
    const materialThickness = 18
    const X = 500  // Default cabinet width
    const Y = 700  // Default cabinet height

    // Face boundaries (matching ADekoDebugMode.lua layout)
    const faces = [
      // Left face
      { x: offset, y: offset, width: Y, height: X, label: 'Left' },
      // Right face  
      { x: offset + Y + offset, y: offset, width: Y, height: X, label: 'Right' },
      // Top face
      { x: offset, y: offset + X + offset, width: Y, height: materialThickness, label: 'Top' },
      // Bottom face
      { x: offset, y: offset + X + offset + materialThickness + offset, width: Y, height: materialThickness, label: 'Bottom' },
      // Front face
      { x: offset + Y + offset + Y + offset, y: offset, width: materialThickness, height: X, label: 'Front' },
      // Rear face
      { x: offset + Y + offset + Y + offset + materialThickness + offset, y: offset, width: materialThickness, height: X, label: 'Rear' }
    ]

    faces.forEach(face => {
      ctx.strokeRect(face.x, face.y, face.width, face.height)
    })

    ctx.setLineDash([])
  }
}

// Grid offset for drawing commands (to align with grid)
const gridOffsetX = 0
const gridOffsetY = 0

const drawCommand = (ctx: CanvasRenderingContext2D, command: DrawCommand) => {
  ctx.save()

  // Enhanced drawing style for door models
  const isDoorPanel = command.layer_name === 'PANEL'
  const isBottomFace = command.layer_name?.includes('_SF') || false
  const isStructural = command.layer_name?.includes('LMM') || isDoorPanel

  // Set color and style based on layer type
  ctx.strokeStyle = command.color || 'black'
  ctx.fillStyle = command.color || 'black'

  // Enhanced line width for different layer types
  let lineWidth = (command.size || 1) / scale.value
  if (isDoorPanel) {
    lineWidth *= 2.5 // Thicker lines for door panel
  } else if (isStructural) {
    lineWidth *= 1.5 // Slightly thicker for structural elements
  }
  ctx.lineWidth = lineWidth

  // Add transparency and special effects for different layer types
  if (isBottomFace) {
    ctx.globalAlpha = 0.7 // Semi-transparent for bottom face operations
  } else if (isDoorPanel) {
    ctx.globalAlpha = 0.4 // More transparent for door panel background
  } else {
    ctx.globalAlpha = 1.0 // Fully opaque for regular operations
  }

  switch (command.command_type) {
    case 'line':
      ctx.beginPath()
      const lx1 = command.x1 + gridOffsetX
      const ly1 = -command.y1 + gridOffsetY  // Flip Y for ADekoLib compatibility
      const lx2 = command.x2 + gridOffsetX
      const ly2 = -command.y2 + gridOffsetY  // Flip Y for ADekoLib compatibility
      ctx.moveTo(lx1, ly1)
      ctx.lineTo(lx2, ly2)
      ctx.stroke()
      break

    case 'circle':
      const cx = command.x1 + gridOffsetX
      const cy = -command.y1 + gridOffsetY  // Flip Y for ADekoLib compatibility
      ctx.beginPath()
      ctx.arc(cx, cy, command.radius, 0, 2 * Math.PI)
      ctx.stroke()
      break

    case 'arc':
      const ax = command.x1 + gridOffsetX
      const ay = -command.y1 + gridOffsetY  // Flip Y for ADekoLib compatibility
      // Fix: Use proper null checking for angles (0 is a valid angle!)
      let startAngle = (command.start_angle !== undefined ? command.start_angle : 0) * Math.PI / 180
      let endAngle = (command.end_angle !== undefined ? command.end_angle : 360) * Math.PI / 180
      let clockwise = command.clockwise || false

      ctx.beginPath()

      // Fix arc angle issue: When Y is flipped (as we do with -command.y1),
      // we need to negate the angles to maintain the correct arc direction
      // This is because flipping Y effectively mirrors the coordinate system
      startAngle = -startAngle
      endAngle = -endAngle

      // When we negate angles, clockwise becomes counter-clockwise and vice versa
      clockwise = !clockwise

      // Ensure start angle is less than end angle for proper arc drawing
      if (startAngle > endAngle) {
        [startAngle, endAngle] = [endAngle, startAngle]
        clockwise = !clockwise
      }

      // Canvas arc() method: arc(x, y, radius, startAngle, endAngle, counterclockwise)
      // Note: Canvas uses counterclockwise parameter (opposite of clockwise)
      ctx.arc(ax, ay, command.radius, startAngle, endAngle, !clockwise)
      ctx.stroke()
      break

    case 'pixel':
      const px = command.x1 + gridOffsetX
      const py = -command.y1 + gridOffsetY  // Flip Y for ADekoLib compatibility
      ctx.fillRect(px - 0.5, py - 0.5, 1, 1)
      break

    case 'rect':
      const rx = command.x1 + gridOffsetX
      const ry = -command.y1 + gridOffsetY  // Flip Y for ADekoLib compatibility
      const width = command.x2 - command.x1
      const height = command.y2 - command.y1

      // Check if this is a rounded rectangle
      const radius = command.radius || 0
      if (radius > 0) {
        // Draw rounded rectangle corners as circles (simplified)
        ctx.beginPath()
        // Top-left corner
        ctx.arc(rx + radius, ry - radius, radius, 0, 2 * Math.PI)
        ctx.stroke()

        ctx.beginPath()
        // Top-right corner
        ctx.arc(rx + width - radius, ry - radius, radius, 0, 2 * Math.PI)
        ctx.stroke()

        ctx.beginPath()
        // Bottom-left corner
        ctx.arc(rx + radius, ry - height + radius, radius, 0, 2 * Math.PI)
        ctx.stroke()

        ctx.beginPath()
        // Bottom-right corner
        ctx.arc(rx + width - radius, ry - height + radius, radius, 0, 2 * Math.PI)
        ctx.stroke()
      } else {
        // Enhanced rectangle drawing for door models
        if (isDoorPanel) {
          // Fill the door panel with semi-transparent color
          ctx.fillRect(rx, ry, width, -height)  // Negative height for Y flip
          // Add a border
          ctx.strokeRect(rx, ry, width, -height)
        } else {
          // Regular stroke for other layers
          ctx.strokeRect(rx, ry, width, -height)  // Negative height for Y flip
        }
      }
      break

    case 'text':
      // Make text much bigger - multiply by 4 and ensure minimum size of 32px
      const fontSize = Math.max(20, (command.size || 12) * 1.5) / scale.value
      ctx.font = `${fontSize}px Arial`
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      const tx = command.x1 + gridOffsetX
      const ty = -command.y1 + gridOffsetY  // Flip Y for ADekoLib compatibility
      ctx.fillText(command.text, tx, ty)
      break

    case 'svg_path':
      // Render SVG path data using Path2D
      if (command.svg_path) {
        try {
          const path2D = new Path2D(command.svg_path)
          ctx.save()
          // Apply coordinate transformation for SVG paths
          ctx.translate(gridOffsetX, gridOffsetY)
          ctx.scale(1, -1) // Flip Y for ADekoLib compatibility
          ctx.stroke(path2D)
          ctx.restore()
        } catch (error) {
          console.warn('Failed to render SVG path:', command.svg_path, error)
          // Fallback: draw a small circle to indicate the path location
          ctx.beginPath()
          ctx.arc(command.x1 + gridOffsetX, -command.y1 + gridOffsetY, 2, 0, 2 * Math.PI)
          ctx.stroke()
        }
      }
      break

    case 'polyline':
    case 'polygon':
      // Render polyline or polygon from points array
      if (command.points && command.points.length > 1) {
        ctx.beginPath()
        const firstPoint = command.points[0]
        ctx.moveTo(firstPoint[0] + gridOffsetX, -firstPoint[1] + gridOffsetY)

        for (let i = 1; i < command.points.length; i++) {
          const point = command.points[i]
          ctx.lineTo(point[0] + gridOffsetX, -point[1] + gridOffsetY)
        }

        // Close the path for polygons
        if (command.command_type === 'polygon') {
          ctx.closePath()
        }

        ctx.stroke()
      }
      break

    case 'bezier':
      // Render cubic Bezier curve
      if (command.points && command.points.length >= 4) {
        ctx.beginPath()
        const p0 = command.points[0]
        const p1 = command.points[1]
        const p2 = command.points[2]
        const p3 = command.points[3]

        ctx.moveTo(p0[0] + gridOffsetX, -p0[1] + gridOffsetY)
        ctx.bezierCurveTo(
          p1[0] + gridOffsetX, -p1[1] + gridOffsetY,
          p2[0] + gridOffsetX, -p2[1] + gridOffsetY,
          p3[0] + gridOffsetX, -p3[1] + gridOffsetY
        )
        ctx.stroke()
      }
      break
  }

  ctx.restore()
}

// Watch for draw commands changes
watch(() => props.drawCommands, (newCommands) => {
  console.log('GraphicsCanvas received draw commands:', newCommands.length, 'commands')
  nextTick(() => {
    // Only draw if the component is mounted and canvas is available
    if (canvasRef.value) {
      drawCanvas()
    }
  })
}, { immediate: false }) // Don't run immediately, wait for mount

// Expose functions for parent component
defineExpose({
  resetView,
  zoomIn,
  zoomOut
})

// Redraw on mount
onMounted(() => {
  // Set initial view to show X+Y+ quadrant
  offsetX.value = 100  // Move view right to show positive X
  offsetY.value = 700  // Move view up to show positive Y

  nextTick(() => {
    // Only draw if canvas is available
    if (canvasRef.value) {
      drawCanvas()
    }

    // Handle window resize
    const resizeObserver = new ResizeObserver(() => {
      if (canvasRef.value) {
        drawCanvas()
      }
    })

    if (canvasRef.value?.parentElement) {
      resizeObserver.observe(canvasRef.value.parentElement)
    }
  })
})
</script>

<style scoped>
.graphics-canvas-container {
  @apply h-full relative overflow-hidden;
}

.graphics-canvas {
  @apply w-full h-full cursor-grab;
}

.graphics-canvas:active {
  @apply cursor-grabbing;
}
</style>
