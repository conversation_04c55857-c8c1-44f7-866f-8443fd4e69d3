{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV*-K_Ballnose*cT*-K_Freze*cT* \tKanal bicagi \nK_AciliV*aV* \tKanal bicagi \nCep_Acma \tCep acma islemi tarama bicagi\nH_Freze_Ebatlama_ic \tCam acma islemi ebatlama bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 0, "description": "<PERSON><PERSON><PERSON> ofset (<PERSON><PERSON><PERSON> sag sol a gore belirlenir)", "parameterName": "a"}, {"defaultValue": 100, "description": "Ust kenardan mesafe (0 girilirse alt ve ustden esit araliklanır)", "parameterName": "ust"}, {"defaultValue": 150, "description": "Alt kenardan mesafe (0 girilirse alt ve ustden esit araliklanır)", "parameterName": "alt"}, {"defaultValue": 100, "description": "Sol kenardan ic kenara mesafe(a:0 sa girilir)", "parameterName": "sag"}, {"defaultValue": 100, "description": "Sol kenardan ic kenara mesafe(a:0 sa girilir)", "parameterName": "sol"}, {"defaultValue": 6, "description": "<PERSON><PERSON> derin<PERSON>gi", "parameterName": "ad"}, {"defaultValue": 20, "description": "Cita duz genisligi", "parameterName": "c"}, {"defaultValue": 25, "description": "Acili V Kenardan Gobege Mesafe", "parameterName": "h"}, {"defaultValue": 20, "description": "Tarama <PERSON>", "parameterName": "cW"}, {"defaultValue": 6, "description": "ic Kose Finis Bicak Capi", "parameterName": "cT"}, {"defaultValue": 135, "description": "Acili Vbit uc acisi(varsa)", "parameterName": "aV"}, {"defaultValue": 0, "description": "--------------------TUM KAPI VEYA UST BOLUM ISLEMLERI---------------------", "parameterName": "-----"}, {"defaultValue": 0, "description": "Duz kapakmi camli mi?(Duz :1/ Cam:0)", "parameterName": "windowOpExist"}, {"defaultValue": 2, "description": "X yonunde kac adet", "parameterName": "blockNoX"}, {"defaultValue": 3, "description": "Y yonunde kac adet", "parameterName": "blockNoY"}, {"defaultValue": 0, "description": "--------------------ALT BOLUM ISLEMLERI---------------------", "parameterName": "-----"}, {"defaultValue": 1, "description": "Alt bolum var mi?(Var :1/ Yok:0)", "parameterName": "bottomSectExist"}, {"defaultValue": 1, "description": "Duz kapakmi camli mi?(Duz :1/ Cam:0)", "parameterName": "botWindowOpExist"}, {"defaultValue": 100, "description": "Ust bolum alt bolum arasi mesafe", "parameterName": "ara"}, {"defaultValue": 900, "description": "Alttan kol deligine olan mesafe", "parameterName": "KD"}, {"defaultValue": 400, "description": "Alt bolum yuksekligi (KD varsa iptal olur)", "parameterName": "altH"}, {"defaultValue": 2, "description": "X yonunde kac adet", "parameterName": "botBlockNoX"}, {"defaultValue": 1, "description": "Y yonunde kac adet", "parameterName": "botBlockNoY"}, {"defaultValue": 0, "description": "----------------------GOBEK ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 2, "description": "Ic kenar ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 6, "description": "Ic kenar ustu kanal islemi bicak capi", "parameterName": "dT"}, {"defaultValue": 0, "description": "Ic kenar ustu kanal islemi Ballnose mu? Flat-Duz mu? (B:0/ F:1)", "parameterName": "topGrooveBorF"}, {"defaultValue": 1, "description": "Gobek var mi?(Var:1/ Yok:0)", "parameterName": "cabCoreExist"}, {"defaultValue": 1, "description": "Gobek desen bicagi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "shapeToolExist"}, {"defaultValue": 0, "description": "----------------------DIS KENAR ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeToolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi V mi? Raduslu mu? (V :0/ R:1)", "parameterName": "extEdgeVorR"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}