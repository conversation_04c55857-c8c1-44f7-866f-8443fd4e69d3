-- Test script to verify arc bulge fixes in polyline function (Version 2)
X = 600  -- width
Y = 400  -- height
materialThickness = 18
G = require("ADekoLib")
local engine = require("makerjs_engine")

-- Initialize the debug model (for face layout)
engine.model_def("debug_model", function()
    engine.layer("debug")
end)

-- Initialize ADekoLib with makerjs_engine support
G.engine = engine

-- Create a temporary model for the actual geometry from modelMain()
engine.model_def("temp_model", function()
    -- This model will be populated by ADekoLib calls during modelMain()
end)

-- Set the engine to use the temp_model for geometry
engine.set_current_model("temp_model")

-- Enable debug output for arcs
G.debug_arcs = true

function modelMain()
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  print("=== Testing Arc Bulge Fixes V2 ===")
  
  -- Test 1: Simple polyline with positive bulge
  print("Test 1: Simple polyline with positive bulge")
  G.setLayer("TEST_positive_bulge")
  G.setThickness(-5)
  
  G.polyline(
    {50, 50, 0, 0.5},      -- start point with positive bulge for next segment
    {150, 50}  -- end point
  )

  -- Test 2: Simple polyline with negative bulge
  print("Test 2: Simple polyline with negative bulge")
  G.setLayer("TEST_negative_bulge")
  G.setThickness(-5)

  G.polyline(
    {50, 150, 0, -0.5},      -- start point with negative bulge for next segment
    {150, 150}  -- end point
  )
  
  -- Test 3: Line function with bulge
  print("Test 3: Line function with bulge")
  G.setLayer("TEST_line_bulge")
  G.setThickness(-5)
  
  G.line({250, 50}, {350, 50}, 0.5)  -- Positive bulge
  G.line({250, 150}, {350, 150}, -0.5) -- Negative bulge
  
  print("=== Arc bulge test V2 completed ===")
  print("Expected results:")
  print("- All arcs should appear as partial curves, NOT full circles")
  print("- Positive bulges should curve counter-clockwise")
  print("- Negative bulges should curve clockwise")
  print("- Debug output should show start/end angles")
end

-- Set up global variables that ADekoLib expects
_G.showPoints = true
G.start()
G.showPoints(true)
G.enableListing(true)
modelMain()
G.finish()

-- Export the temporary model that contains the actual geometry from modelMain()
local temp_json = engine.export_model("temp_model")
print("Geometry model JSON generated:")
print(temp_json)
