-- Test Integrated Turtle Graphics Buttons
-- This script tests the integrated turtle graphics controls in the visualization panel

function modelMain()
    print("=== Testing Integrated Turtle Graphics Buttons ===")
    
    -- Clear screen and set up
    wipe()
    pndn()  -- Put pen down
    pncl("blue")  -- Set pen color to blue
    pnsz(3)  -- Set pen size to 3
    
    -- Draw a test pattern to verify the buttons work
    -- Draw a spiral pattern
    for i = 1, 50 do
        move(i * 2)  -- Gradually increase movement
        turn(91)     -- Slightly more than 90 degrees for spiral effect
        
        -- Change colors periodically
        if i % 10 == 0 then
            if i % 20 == 0 then
                pncl("red")
            else
                pncl("green")
            end
        end
    end
    
    -- Add some geometric shapes
    pnup()
    posn(-100, -100)
    pndn()
    pncl("purple")
    pnsz(2)
    
    -- Draw a hexagon
    for i = 1, 6 do
        move(60)
        turn(60)
    end
    
    -- Add circles
    pnup()
    posn(100, 100)
    pndn()
    pncl("orange")
    crcl(0, 0, 40)
    
    pnup()
    posn(-100, 100)
    pndn()
    pncl("cyan")
    crcl(0, 0, 30)
    
    -- Add text instructions
    pnup()
    posn(0, 200)
    text("Test the buttons in the visualization panel:", 0, 0, 0)
    
    pnup()
    posn(0, 180)
    text("• Reset View: Resets zoom and position", 0, 0, 0)
    
    pnup()
    posn(0, 160)
    text("• Zoom In/Out: Controls zoom level", 0, 0, 0)
    
    pnup()
    posn(0, 140)
    text("• Minimize: Minimizes the turtle graphics", 0, 0, 0)
    
    print("Test pattern completed!")
    print("Check the visualization panel for integrated buttons:")
    print("- Reset View (↻)")
    print("- Zoom In (+)")
    print("- Zoom Out (-)")
    print("- Minimize (⊟)")
    print("- Clear (🗑)")
    print("- Expand/Collapse (⊞/⊟)")
end
