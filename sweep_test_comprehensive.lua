-- Comprehensive Sweep Operation Test
-- Tests all tool types and sweep operations

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for comprehensive testing
AdekoLib.setPanelSize(200, 150, 20)

print("=== Comprehensive Sweep Operation Test ===")

-- Create the door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("main_door", 0, 0, 200, 150)

-- Test cylindrical tools (endmills)
print("2. Testing cylindrical tools...")

AdekoLib.layer("12MM")  -- Large endmill for roughing
AdekoLib.rect("rough_pocket", 20, 20, 80, 40)

AdekoLib.layer("8MM")   -- Medium endmill for general cutting
AdekoLib.rect("medium_cut", 120, 20, 60, 30)

AdekoLib.layer("4MM")   -- Small endmill for detail work
AdekoLib.circle("detail_pocket", 50, 80, 15)
AdekoLib.line("detail_line", 120, 70, 180, 70)

AdekoLib.layer("2MM")   -- Very small endmill
AdekoLib.circle("fine_hole", 160, 90, 5)

-- Test conical tools (V-bits)
print("3. Testing conical tools...")

AdekoLib.layer("V90")   -- 90-degree V-bit
AdekoLib.line("v_groove1", 30, 100, 90, 100)

AdekoLib.layer("V120")  -- 120-degree V-bit  
AdekoLib.polyline("decorative_border", {
    {10, 10}, {190, 10}, {190, 140}, {10, 140}, {10, 10}
})

-- Test ballnose tools
print("4. Testing ballnose tools...")

AdekoLib.layer("BALL6MM")  -- 6mm ballnose
AdekoLib.circle("smooth_pocket", 140, 110, 20)

AdekoLib.layer("BALL4MM")  -- 4mm ballnose for finishing
AdekoLib.rect("finish_area", 60, 110, 40, 25)

-- Test drilling operations
print("5. Testing drilling operations...")

AdekoLib.layer("DRILL8MM")  -- 8mm drill
AdekoLib.circle("large_hole1", 40, 130, 4)
AdekoLib.circle("large_hole2", 160, 130, 4)

AdekoLib.layer("DRILL5MM")  -- 5mm drill
AdekoLib.circle("med_hole1", 70, 50, 2.5)
AdekoLib.circle("med_hole2", 130, 50, 2.5)

AdekoLib.layer("DRILL3MM")  -- 3mm drill
AdekoLib.circle("small_hole1", 25, 60, 1.5)
AdekoLib.circle("small_hole2", 175, 60, 1.5)

-- Test edge profiling
print("6. Testing edge profiling...")

AdekoLib.layer("EDGE10MM")  -- Edge profiling tool
AdekoLib.rect("edge_profile", -2, -2, 204, 154)  -- Slightly oversized

-- Test chamfer operations
print("7. Testing chamfer operations...")

AdekoLib.layer("CHAMFER")  -- Chamfer tool
AdekoLib.rect("chamfer1", 100, 80, 30, 20)
AdekoLib.circle("chamfer2", 50, 40, 12)

-- Test overlapping operations (for boolean testing)
print("8. Testing overlapping operations...")

AdekoLib.layer("6MM")   -- 6mm endmill
AdekoLib.circle("overlap1", 80, 90, 15)
AdekoLib.circle("overlap2", 100, 90, 15)  -- Overlaps with overlap1

-- Test complex polyline operations
print("9. Testing complex polyline operations...")

AdekoLib.layer("10MM")  -- 10mm endmill
AdekoLib.polyline("complex_shape", {
    {150, 100}, {170, 100}, {170, 120}, {160, 130}, {150, 120}, {150, 100}
})

-- Test arc operations
print("10. Testing arc operations...")

AdekoLib.layer("5MM")   -- 5mm endmill
AdekoLib.arc("test_arc", 120, 40, 15, 0, 180)  -- Semi-circle

print("Comprehensive geometry created!")
print("Tool types tested:")
print("- Cylindrical: 12MM, 8MM, 4MM, 2MM, 6MM, 10MM, 5MM")
print("- Conical: V90, V120")
print("- Ballnose: BALL6MM, BALL4MM")
print("- Drilling: DRILL8MM, DRILL5MM, DRILL3MM")
print("- Edge: EDGE10MM")
print("- Chamfer: CHAMFER")

print("\nOperation types tested:")
print("- Rectangular pockets")
print("- Circular pockets")
print("- Line cuts")
print("- V-groove cuts")
print("- Decorative borders")
print("- Drilling holes")
print("- Edge profiling")
print("- Chamfer operations")
print("- Overlapping cuts (boolean operations)")
print("- Complex polyline shapes")
print("- Arc operations")

-- Finish the model
AdekoLib.finish()

print("\n=== Comprehensive Test Complete ===")
print("This test covers all major sweep operation scenarios.")
print("Expected OCJS worker operations:")
print("1. Door body creation (200x150x20mm)")
print("2. Tool BRep generation for all tool types")
print("3. Multiple sweep operations with different tools")
print("4. Boolean operations for overlapping cuts")
print("5. Complex geometry processing")
print("\nCheck 3D visualization and console for results!")
