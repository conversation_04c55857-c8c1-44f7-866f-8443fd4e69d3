-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib  
  minimum = 150
  limit = 350
  
  a = 60                    -- Kenardan Vbite
  aa = 30					-- Dar kapak için Kenardan mesafe
  h = 35                    -- Vbitten göbeğe
  ad = 6                    -- Vbit derinliği (sunkenDepth)
  aV = 90                  	-- Vbit Açısı
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 6                   	-- <PERSON><PERSON> bıçak çapı (Köşe Temizleme)
  gaps = 60                	--burası ortadaki çizgiler
  gd = 10					--göbekteki kanallar arası mesafe
  sbt = 0		 			-- göbek desen bıçağına göre içteki kanallar için kaydırma mesafesi-yoksa gaps/2
    
  extGrooveExist                = 3   -- D<PERSON><PERSON> kanal var mı? derinlik/0:yok
  edgeCornerRExist          	= 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local b = 0                    -- Vbitten Dıs kanala
  --bd = 3                    -- dış kanal derinliği
  --local hd = 5              -- Göbek Desen Bıcak derinliği
  --local dd = 2              -- iç kanal derinliği
  --local d = 35              -- Göbekten iç kanala
	
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
  local extEdgeVtoolExist		= 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
  local cabCoreExist            = true   -- Göbek var mı?
  local intGrooveExist        	= 2   -- iç kanal var mı? derinlik/ 0:yok
  local shapeToolExist        	= 0   -- Göbek Desen bıçağı var mı? derinlik/0:yok
  local D = edgeCornerRExist
  
  
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)     --indiği derinlikte yarısı oluyor
  
    
  --burası ortadaki çizgiler
  local gobekGenislik = X-2*a-2*sunkenWidth-2*h-2*sbt
  
  local stepX = math.floor(gobekGenislik/gaps)
  
	if ((gobekGenislik/(gaps+gd))-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
  
	local aprxGaps = gobekGenislik/(stepX+1)
  
	if sbt == 0 then
		e = a+sunkenWidth+h+aprxGaps-gd/2
		e2 = a+sunkenWidth+h+aprxGaps+gd/2
	else
		e = a+sunkenWidth+h+sbt+aprxGaps/2-gd/2
		e2 = a+sunkenWidth+h+sbt+aprxGaps/2+gd/2
		stepX = stepX+1
	end

  -- local e = a+sunkenWidth+aprxGaps-bw/2
  -- local e2 = a+sunkenWidth+aprxGaps+bw/2
  --burası ortadaki çizgiler^
    
  local B = math.tan(math.pi/8)
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end

  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end

  if cW <= 0 then
    cW = cT
  end
  
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
  
  G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  if extEdgeVtoolExist > 0 then
  	G.setLayer("K_AciliV_Pah")
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if extGrooveExist > 0 then
    G.setLayer("K_Kanal")  -- DEEP cleanup
    G.setThickness(-extGrooveExist)
    distance = a-b
    distance2 = ust-b
    point1 = {distance, distance2}
    point2 = {X-distance, Y-distance2}
    G.rectangle(point1,point2)
  end
  
	if cabCoreExist then
    
		-- if X>xLimit and Y>yLimit then
		G.setLayer("K_Freze"..cW.."mm")  -- DEEP cleanup
		G.setThickness(-ad)
		distance1 = a+sunkenWidth+cW/2
		distance2 = ust+sunkenWidth+cW/2
		point1 = {distance1, distance2}
		point2 = {X-distance1, Y-distance2}
		G.rectangle(point1,point2)
		
		if h<cT or h<cW then
			print("Tool too large")
			return true
		end
      
		if h>cW then
			distance = a+sunkenWidth+h-cW/2
			point3 = {distance, distance}
			point4 = {X-distance, Y-distance}
			--G.rectangle(point3,point4)
			k = (h-cW)/(cW/2)
			for i=1, k, 1 do
				point1 = G.ptAdd(point1,{cW/2,cW/2})
				point2 = G.ptSubtract(point2, {cW/2,cW/2})
				if point1[1]>point3[1]-cW/2 then
					break
				end
			end
			G.rectangle(point1,point2)
		end
      
      --if h>cW then
      --  distance = a+sunkenWidth+h-cW/2
      --  point3 = {distance, distance}
      --  point4 = {X-distance, Y-distance}
      --  --G.rectangle(point3,point4)
      --  k = (h-cW)/(cW/2)
      --  for i=1, k, 1 do
      --    point5 = G.ptAdd(point3,{cW/2,cW/2})
      --    point6 = G.ptSubtract(point4, {cW/2,cW/2})
      --    if point5[1]>point3[1]-cW/2 then
      --      break
      --    end
      --    G.rectangle(point5,point6)
      --  end
      --end
      --G.setThickness(-(ad))
      
		if h ~= cW then
			distance = a+sunkenWidth+h-cW/2
			distance2 = ust+sunkenWidth+h-cW/2
			point10 = {distance, distance2}
			point11 = {X-distance, Y-distance2}
			G.rectangle(point10,point11)
		end
      
		if shapeToolExist > 0 then  ----Göbekte desen bıçağı varsa
			G.setLayer("K_Desen")  -- DEEP cleanup
			G.setThickness(-shapeToolExist)
			distance = a + sunkenWidth + h
			distance2 = ust + sunkenWidth + h
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
		else  
			G.setLayer("K_AciliV" .. aV)  --- Gobek
			G.setThickness(-ad)
			distance = a + sunkenWidth + h
			distance2 = ust + sunkenWidth + h
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
		end
      
		if intGrooveExist > 0 then      ------İç kanal varsa
			local checkX = 2*a + 2*h + 2*sunkenWidth
			local checkY = 2*ust + 2*h + 2*sunkenWidth
			if X > checkX and Y > checkY then
				-- print("Part dimension too small, check a + h + d value")
				-- return true
				G.setLayer("K_Kanal")  -- DEEP cleanup
				G.setThickness(-intGrooveExist)
				-- distance = a + sunkenWidth + h + d
				-- point1 = {distance, distance}
				-- point2 = {X-distance, Y-distance}
				-- G.rectangle(point1,point2)
				
				--burası ortadaki çizgiler
				--for i=0,stepX do
				--local x = e+i*aprxGaps
				--local offset = ust + sunkenWidth + 2*h/3
				--point3 = {x,offset}
				----point3 = {x,0}
				--point4 = {x,Y-offset}  
				----point4 = {x,Y}  
				--G.line(point3,point4,0)
				--i = i+1
				--end 
				--burası ortadaki çizgiler
				--burası ortadaki çizgi 1 ler
				for i=0,stepX-1 do
					local x = e+i*aprxGaps
					local offset = ust + sunkenWidth + 2*h/3
					point3 = {x,offset}
					point4 = {x,Y-offset}  
					G.line(point3,point4,0)
					i = i+1
				end 

				for i=0,stepX-1 do
					local x = e2+i*aprxGaps
					local offset = ust + sunkenWidth + 2*h/3
					point3 = {x,offset}
					point4 = {x,Y-offset}  
					G.line(point3,point4,0)
					i = i+1
				end
			end
			--burası ortadaki çizgi 2 ler
		end

	else ------------- bu araya 250 den küçükler için tarama gelebilir
		G.setLayer("Cep_Acma".. cW .. "mm")  -- DEEP cleanup
		G.setThickness(-ad)
		distance = a+sunkenWidth
		distance2 = ust+sunkenWidth
		point6 = {distance, distance2}
		point7 = {X-distance, Y-distance2}
		G.rectangle(point6,point7)
	end
	
	G.setLayer("K_TarKoseTemizl"..cT.."mm")
	
	if cW ~= cT then
		if h >= 3*cT then
			G.setThickness(0)
			G.cleanCorners(corner1,corner2,ad,cT)
		else
			G.setThickness(-ad)
			distance = a+sunkenWidth + cT/2
			distance2 = ust+sunkenWidth + cT/2
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
		end
	end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
