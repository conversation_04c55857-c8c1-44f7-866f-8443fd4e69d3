-- Simple test for door model color visualization
-- Demonstrates the enhanced color scheme with basic shapes

-- Global variables required by AdekoDebugMode
X = 400
Y = 600
materialThickness = 18

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode
    G = AdekoLib

    -- Create door panel (PANEL layer - should appear as filled brown rectangle)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Basic cutting operation (red spectrum)
    G.setLayer("K_Freze10mm")  -- Should appear in tomato color
    G.setThickness(-3)
    G.rectangle({50, 50}, {350, 100})

    -- 2. V-bit operation (purple spectrum)
    G.setLayer("K_AciliV90")  -- Should appear in dark orchid
    G.setThickness(-2)
    G.rectangle({50, 150}, {350, 200})

    -- 3. Ball nose operation (blue spectrum)
    G<PERSON>setLayer("K_Ballnose6mm")  -- Should appear in royal blue
    G.setThickness(-4)
    <PERSON><PERSON>circle({200, 300}, 40)

    -- 4. Numeric tool layer (HSL color generation)
    G.setLayer("8MM")  -- Should generate HSL color
    G.setThickness(-2)
    G.circle({100, 450}, 20)

    -- 5. Special operation (yellow/orange spectrum)
    G.setLayer("CLEANCORNERS")  -- Should appear in orange
    G.setThickness(-1)
    G.rectangle({0, 0}, {30, 30})
    G.rectangle({X-30, Y-30}, {X, Y})

    -- BOTTOM SURFACE OPERATIONS (should appear semi-transparent)
    G.setFace("bottom")

    -- Bottom face operation with _SF suffix
    G.setLayer("K_Freze10mm_SF")  -- Should appear semi-transparent fire brick
    G.setThickness(-3)
    G.rectangle({100, 400}, {300, 450})

    print("✓ Simple door color test completed")
    print("✓ PANEL: Filled brown door panel")
    print("✓ K_Freze10mm: Tomato color (top face)")
    print("✓ K_AciliV90: Dark orchid (V-bit)")
    print("✓ K_Ballnose6mm: Royal blue (ball nose)")
    print("✓ 8MM: HSL generated color (numeric tool)")
    print("✓ CLEANCORNERS: Orange (special operation)")
    print("✓ K_Freze10mm_SF: Semi-transparent (bottom face)")

    return true
end

require "ADekoDebugMode"
