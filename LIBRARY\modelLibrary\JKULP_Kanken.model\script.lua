-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  minimum = 250
  jk_side = 4				--(Sol-1,Ust-2,Sag-3,Alt-4,solsag-13,altust-24)
  
  a = 5				--<PERSON><PERSON><PERSON> mesafe
  b = 7					--<PERSON><PERSON><PERSON> mesafe
  
  m = 10					--Yaklasma
  
  Kj1Off = 10              -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  Kj1Opr = 1              --Yapilacak islem 0-Bos,1-K_Kanal, 2-K_Form
  Kj1Depth = 2           -- Kanal Derinligi
  Kj1ToolNumber = 41 				-- Cap veya Vbit Acisi
   
  Kj2Off = 5             -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  Kj2Opr = 2             --Yapilacak islem 0-Bo<PERSON>,1-K_<PERSON><PERSON>, 2-K_Form
  Kj2Depth = 5            -- Kanal Derinligi
  Kj2ToolNumber = 90 				-- Cap veya Vbit Acisi
   
  Kj3Off = 6              -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  Kj3Opr = 0              --Yapilacak islem 0-Bos,1-K_Kanal, 2-K_Form
  Kj3Depth = 5            -- Kanal Derinligi
  Kj3ToolNumber = 42 				-- Cap veya Vbit Acisi
  
  jNotchToolDia = 6		--Fazla malzeme kesen takım capı
  
  K1Off = 30              -- Kenardan Offset
  K1Opr = 1              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K1Depth = 2           -- Kanal Derinligi
  K1DiaAngle = 10 				-- Cap veya Vbit Acisi
 
  K2Off = 40              -- Kenardan Offset
  K2Opr = 2              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K2Depth = 4            -- Kanal Derinligi
  K2DiaAngle = 10 				-- Cap veya Vbit Acisi
  
  K3Off = 50              -- Kenardan Offset
  K3Opr = 3              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K3Depth = 6            -- Kanal Derinligi
  K3DiaAngle = 10 				-- Cap veya Vbit Acisi
  
  K4Off = 60              -- Kenardan Offset
  K4Opr = 4              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K4Depth = 5            -- Kanal Derinligi
  K4DiaAngle = 10 				-- Cap veya Vbit Acisi

  K5Off = 90             -- Kenardan Offset
  K5Opr = 4              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K5Depth = 5            -- Kanal Derinligi
  K5DiaAngle = 10 				-- Cap veya Vbit Acisi

  K6Off = 100             -- Kenardan Offset
  K6Opr = 6              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K6Depth = 6            -- Kanal Derinligi
  K6DiaAngle = 90 				-- Cap veya Vbit Acisi
  
  K7Off = 80              -- Kenardan Offset
  K7Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K7Depth = 7            -- Kanal Derinligi
  K7DiaAngle = 120 				-- Cap veya Vbit Acisi

  K8Off = 85              -- Kenardan Offset
  K8Opr = 0             --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K8Depth = 7            -- Kanal Derinligi
  K8DiaAngle = 150 				-- Cap veya Vbit Acisi
  
  K9Off = 100              -- Kenardan Offset
  K9Opr = 3             --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K9Depth = 7            -- Kanal Derinligi
  K9DiaAngle = 150 				-- Cap veya Vbit Acisi
  
  K10Off = 115              -- Kenardan Offset
  K10Opr = 2             --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K10Depth = 7            -- Kanal Derinligi
  K10DiaAngle = 150 				-- Cap veya Vbit Acisi
   
  K11Off = 120              -- Kenardan Offset
  K11Opr = 2             --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K11Depth = 7            -- Kanal Derinligi
  K11DiaAngle = 150 				-- Cap veya Vbit Acisi
   
  K12Off = 125              -- Kenardan Offset
  K12Opr = 1             --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  K12Depth = 7            -- Kanal Derinligi
  K12DiaAngle = 150 				-- Cap veya Vbit Acisi
  
  
  KLineAvailable = 1
  KLineRotation = 1   --yatay:0, dikey:1
  KLineOffset = 60
  KLineDepth = 1.4
  
  --ARKA YUZEY ISLEMLER BASLA--
  
  KB1Off = 30              -- Kenardan Offset
  KB1Opr = 1              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  KB1Depth = 2           -- Kanal Derinligi
  KB1DiaAngle = 7.5 				-- Cap veya Vbit Acisi
   
  KB2Off = 40              -- Kenardan Offset
  KB2Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  KB2Depth = 4            -- Kanal Derinligi
  KB2DiaAngle = 10 				-- Cap veya Vbit Acisi
   
  KB3Off = 50              -- Kenardan Offset
  KB3Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
  KB3Depth = 6            -- Kanal Derinligi
  KB3DiaAngle = 10 				-- Cap veya Vbit Acisi
  
  edgeCornerRExist          = 0   -- Kapak kose Radusu var mi? derinlik/0:yok
  widIncrExist  = 10  
	
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  if a < 0 or b < 0 then
    print("a veya b degeri 0 dan küçük olamaz")
    return true
  end
  
  function Decimals(W)
    W = string.gsub(W,"[.]",",")
    return W
  end
  
  if m < 0 then
	m=0
  end
  
  local extWidth = m  
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  local leftWidth = 0
  local rightWidth = 0
  local topWidth = 0
  local bottomWidth = 0
  local aSide = 0
  local bSide = 0
  local leftRight = false
  local bottomTop = false
  local aWider = false
  local aTank = 0
  local jNotchOffset = jNotchToolDia/2
  
	if widIncrExist >0 then											--BUYUTME YAPILACAKSA
		 
		if a >= 0 then                              --Uzatma yapılacak uzunluk secimi -a-
			if m == 0 then						
				aSide = 2*a
			elseif m > 0 then
				aSide = a+m
			end
			aWider = true
		end
		
		if b >= 0 then                     --Uzatma yapılacak uzunluk secimi -b-
			if m == 0 then
				bSide = 2*b
			elseif m > 0 then
				bSide = b+m
			end
			bWider = true
		end
		
		G.setLayer("K_JNotch")	
		G.setThickness(-materialThickness)
		if jk_side == 1 then               							 --parçayı büyüten kenara eklenecek çizgiler
			leftRight = true
			leftWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 3 then
			leftRight = true
			rightWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 13 then
			leftRight = true
			leftWidth = extWidth
			rightWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 2 then
			bottomTop = true
			bottomWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 4 then
			bottomTop = true
			topWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 24 then
			bottomTop = true
			topWidth = extWidth
			bottomWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		end
	end
  
  function Kanallar(offset,operation,ad,W)	-- KENARDAN MESAFE, İSLEM, DERİNLİK, CAP VEYA ACI
					--ust--
				   ----4----
				--			--
				--			--
				--1sol		--3sag
				--			--		
				--			--
				   ----2----
					--alt--
	W=Decimals(W)				
    G.setThickness(-ad)
    local xx = X
    local yy = Y
	local uzunluk = yy+b
    local side1 = offset
    local side2 = -a
    local side3 = offset
    local side4 = uzunluk
	

    if jk_side == 1 or jk_side == 3 or jk_side == 13 then								--sol sag kenarlarda mı
			points = {{side1,side2},{side3,side4}}
			if jk_side == 13 then														--sol ve sağ kenarlada mı
				points = {{side1,side2},{side3,side4}}
				points1 = G.mirror(points, "x", X, Y)
			elseif jk_side == 3 then
				points = G.mirror(points, "x", X, Y)
			end
    elseif jk_side == 4 or jk_side == 2 or jk_side == 24 then								--alt ust kenarlarda mı
		xx = Y
		yy = X
		uzunluk = yy+b
		side1 = -a
		side2 = offset
		side3 = uzunluk
		side4 = offset
		
			points = {{side1,side2},{side3,side4}}
			if jk_side == 24 then
				points = {{side1,side2},{side3,side4}}
				points1 = G.mirror(points, "y", X, Y)
			elseif jk_side == 4 then														--ust kenarda mı
				points = G.mirror(points, "y", X, Y)
			end
    end
    
    if operation == 1 then
      G.setLayer("K_Kanal_TN_"..W)		
      G.polylineimp(points)
	  if jk_side == 24 or jk_side == 13 then
		G.polylineimp(points1)
	  end
    end
    
    if operation == 2 then
      G.setLayer("K_Form_TN_"..W)		
      G.polylineimp(points)
	  if jk_side == 24 or jk_side == 13 then
		G.polylineimp(points1)
	  end
    end
    --return
  end
	
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
	Kanallar(Kj1Off,Kj1Opr,Kj1Depth,Kj1ToolNumber)
	
	if K2Opr>0 then
		Kanallar(Kj1Off-Kj2Off,Kj2Opr,Kj2Depth,Kj2ToolNumber)
		if K3Opr>0 then
			Kanallar(Kj1Off-Kj2Off-Kj3Off,Kj3Opr,Kj3Depth,Kj3ToolNumber)
		end
	end
	
	-------gobekteki kanallar
	
  function Kanallar2(offset,operation,ad,W)	-- KENARDAN MESAFE, İSLEM, DERİNLİK, CAP VEYA ACI
	local sunkenWidth = ad * math.tan((math.pi*W/180)/2.0)
	local vMidDiameter = 100
	local distance = offset
	G.setThickness(-ad)
	point1 = {distance, distance}
	point2 = {X-distance, Y-distance}
	
	if operation ==1 then
		G.setLayer("K_Freze"..W.."mm")		
		G.rectangle(point1,point2)
	end
	
	if operation ==2 then
		G.setLayer("K_Ballnose"..W.."mm")		
		G.rectangle(point1,point2)
	end
	
	if operation ==3 then
		G.setLayer("K_BalikSirti"..W.."mm")		
		G.rectangle(point1,point2)
	end
	
	if operation ==4 then
		G.setLayer("Cep_Acma")		
		G.rectangle(point1,point2)
	end
	
	if operation ==5 then
		G.setLayer("K_AciliV"..W)		
		G.rectangle(point1,point2)
	end
		
	if operation ==6 then
		G.setLayer("K_AciliV"..W)	
		G.setThickness(0)			
		G.sunkenFrame(point1, point2, ad, W, vMidDiameter)
	end
  
	if operation ==7 then
		G.setLayer("K_Desen"..W)		
		G.rectangle(point1,point2)
	end
 	--return
  end
  
  if KLineAvailable == 1 then
    G.setLayer("K_Cizgi")	
    G.setThickness(-KLineDepth)
    if KLineRotation == 0 then
      G.line({0, KLineOffset}, {X, KLineOffset})
      G.line({0, Y - KLineOffset}, {X, Y-KLineOffset})
    end
    if KLineRotation == 1 then
      G.line({KLineOffset, 0},{KLineOffset, Y})
      G.line({X-KLineOffset, 0}, {X-KLineOffset, Y})
    end
  end
  
  
	Kanallar2(K1Off,K1Opr,K1Depth,K1DiaAngle)
	if K2Opr>0 then
		if (X-2*K2Off)> 0 and (Y-2*K2Off)> 0 then
			Kanallar2(K2Off,K2Opr,K2Depth,K2DiaAngle)
		end
		if K3Opr>0 then
			if (X-2*K3Off)> 0 and (Y-2*K3Off)> 0 then
				Kanallar2(K3Off,K3Opr,K3Depth,K3DiaAngle)
			end
			if K4Opr>0 then
				if (X-2*K4Off)> 0 and (Y-2*K4Off)> 0 then
					Kanallar2(K4Off,K4Opr,K4Depth,K4DiaAngle)
				end
				if K5Opr>0 then
					if (X-2*K5Off)> 0 and (Y-2*K5Off)> 0 then
						Kanallar2(K5Off,K5Opr,K5Depth,K5DiaAngle)
					end
					if K6Opr>0 then
						if (X-2*K6Off)> 0 and (Y-2*K6Off)> 0 then
							Kanallar2(K6Off,K6Opr,K6Depth,K6DiaAngle)
						end
						if K7Opr>0 then
							if (X-2*K7Off)> 0 and (Y-2*K7Off)> 0 then
								Kanallar2(K7Off,K7Opr,K7Depth,K7DiaAngle)
							end
							if K8Opr>0 then
								if (X-2*K8Off)> 0 and (Y-2*K8Off)> 0 then
									Kanallar2(K8Off,K8Opr,K8Depth,K8DiaAngle)
								end
								if K9Opr>0 then
									if (X-2*K9Off)> 0 and (Y-2*K9Off)> 0 then
										Kanallar2(K9Off,K9Opr,K9Depth,K9DiaAngle)
									end
									if K10Opr>0 then
										if (X-2*K10Off)> 0 and (Y-2*K10Off)> 0 then
											Kanallar2(K10Off,K10Opr,K10Depth,K10DiaAngle)
										end
										if K11Opr>0 then
											if (X-2*K11Off)> 0 and (Y-2*K11Off)> 0 then
												Kanallar2(K11Off,K11Opr,K11Depth,K11DiaAngle)
											end
											if K12Opr>0 then
												if (X-2*K12Off)> 0 and (Y-2*K12Off)> 0 then
													Kanallar2(K12Off,K12Opr,K12Depth,K12DiaAngle)
												end
											end
										end
									end
								end
							end
						end
					end
				end
			end
		end
	end
	
	if KB1Opr>0 then
		G.setFace("bottom")
		if (X-2*KB1Off)> 0 and (Y-2*KB1Off)> 0 then
			Kanallar2(KB1Off,KB1Opr,KB1Depth,KB1DiaAngle)
		end
		if KB2Opr>0 then
			if (X-2*KB2Off)> 0 and (Y-2*KB2Off)> 0 then
				Kanallar2(KB2Off,KB2Opr,KB2Depth,KB2DiaAngle)
			end
			if KB3Opr>0 then
				if (X-2*KB3Off)> 0 and (Y-2*KB3Off)> 0 then
					Kanallar2(KB3Off,KB3Opr,KB3Depth,KB3DiaAngle)
				end
			end
		end
	end
	
	  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
 elseif widIncrExist>0 then
	if bottomTop then                     ---kanallar ustte ve altta
		G.makePartShape({-leftWidth-aSide,-bottomWidth,0},
		{X+rightWidth+bSide,-bottomWidth,0},         
		{X+rightWidth+bSide,Y+topWidth,0},           
		{-leftWidth-aSide,Y+topWidth,0},             
		{-leftWidth-aSide,-bottomWidth,0})
	end
	if leftRight then                       ---kanallar sag ve solda
		G.makePartShape({-leftWidth,-bottomWidth-aSide,0},
		{X+rightWidth,-bottomWidth-aSide,0},         
		{X+rightWidth,Y+topWidth+bSide,0},           
		{-leftWidth,Y+topWidth+bSide,0},             
		{-leftWidth,-bottomWidth-aSide,0})
	end
  else
    G.makePartShape()
  end
    
	
  return true
end


require "ADekoDebugMode"
