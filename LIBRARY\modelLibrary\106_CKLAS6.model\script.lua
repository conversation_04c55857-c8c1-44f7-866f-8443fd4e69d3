-- ADekoCAM, Model Script
--28membran ile benzer
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 50                -- Kenardan Vbite
  aa = 30		  -- Dar kapak için Kenardan mesafe
  h = 35                -- Vbitten göbeğe
  ad = 6                -- Vbit derinliği (sunkenDepth)
  cD = 15				-- Radus Veren form bıcak düz geniç çapı
  cR = 5				-- Radus Veren form bıcak düz uç dar çapı
  cW = 20               -- Tarama Bıçak Çapı
  cT = 6                -- İnce bıçak çapı (Köşe Temizleme)
      
  shapeToolExist     		= 5   -- Göbek Desen bıçağı var mı? derinlik/0:yok
  topGrooveExist      		= 2   -- Açılı V üzerinde kanal var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  --hd = 5                -- Göbek Desen Bıcak derinliği
  --local dd = 2          -- iç kanal derinliği
  --local bd = 2          -- Acili V Ustu kanal derinliği
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  

  local aV = 0              -- Vbit Açısı
  local extEdgeRtoolExist   = 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
  local cabCoreExist        = true   -- Göbek var mı?
  local d = 35          -- Göbekten iç kanala
  local  intGrooveExist     = 0   -- iç kanal var mı? derinlik/ 0:yok
  local D = edgeCornerRExist

  local B = math.tan(math.pi/8)
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  local vWideDiameter = 60
  -- local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
  local sunkenWidth = cD-cR

	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
  
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
	
	if cW <= 0 then
		cW = cT
	end
	

	G.setLayer("K_Raduslu_" .. cD .. "mm")  -- 
	G.setThickness(-ad)
	distance = a+sunkenWidth+cR/2
	distance2 = ust+sunkenWidth+cR/2
	point11 = {distance, distance2,0}
	point12 = {X-distance, Y-distance2,0}
	G.rectangle(point11,point12)
  
	if extEdgeRtoolExist > 0 then
    G.setLayer("h_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
	
	if topGrooveExist > 0 then           ---Acili V üstünce kanal var mı?
		--G.setLayer("K_Ballnose") 
		G.setLayer("K_Freze"..cT.."mm")  -- 
		G.setThickness(-topGrooveExist)
		distance = a + cT/2
		distance2 = ust + cT/2
		G.line({distance, 0}, {distance, Y})
		G.line({X-distance, 0}, {X-distance, Y})
		G.line({distance, distance2}, {X-distance, distance2})
		G.line({distance, Y-distance2}, {X-distance, Y-distance2})
	end
  
	if cabCoreExist then       ---göbek var mı?
  
		G.setLayer("K_Freze"..cW.."mm")  -- 
		G.setThickness(-ad)
		distance = a+sunkenWidth+cW/2
		distance2 = ust+sunkenWidth+cW/2
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
		
		if h<cT or h<cW then
			print("Tool too large")
			return true
		end
		
		if h>cW then
			distance = a+sunkenWidth+h-cW/2
			distance2 = ust+sunkenWidth+h-cW/2
			point3 = {distance, distance2}
			point4 = {X-distance, Y-distance2}
			--G.rectangle(point3,point4)
			k = (h-cW)/(cW/2)
			for i=1, k, 1 do
			point1 = G.ptAdd(point1,{cW/2,cW/2})
			point2 = G.ptSubtract(point2, {cW/2,cW/2})
			if point1[1]>point3[1]-cW/2 then
				break
			end
			G.rectangle(point1,point2)
			end
		end
		
		if h ~= cW then
			G.setThickness(-(ad))
			distance = a+sunkenWidth+h-cW/2
			distance2 = ust+sunkenWidth+h-cW/2
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
		end
		
		if shapeToolExist > 0 then  ----Göbekte desen bıçağı varsa
			G.setLayer("K_Desen")  -- 
			G.setThickness(-shapeToolExist)
			distance = a + sunkenWidth + h
			distance2 = ust + sunkenWidth + h
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
		else 
			G.setLayer("K_AciliV" .. aV)  --- Gobek
			G.setThickness(-ad)
			distance = a + sunkenWidth + h
			distance2 = ust + sunkenWidth + h
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
			
		end
		
		if intGrooveExist > 0 then      ------İç kanal varsa
			local check = 2*a + 2*h + 2*d
			if X < check or Y < check then
			print("Part dimension too small, check a + h + d value")
			return true
			end
			G.setLayer("K_Kanal")  -- 
			G.setThickness(-intGrooveExist)
			distance = a + sunkenWidth + h + d
			distance2 = ust + sunkenWidth + h + d
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
		end
	else    -------------- Göbek 0:yoksa tüm kapaklarda
		G.setLayer("Cep_Acma".. cW .. "mm")  -- 
		G.setThickness(-ad)
		distance = a+sunkenWidth
		distance2 = ust+sunkenWidth
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	end
  
	G.setLayer("K_Freze"..cT.."mm")

  if cW ~= cT and cR> cT then          --- bu islemi sadece büyük takım ve küçük takım ile birlikte tarama da yaparsa yapacak
	if h >= 3*cT then
		G.setThickness(0)
		distance1 = a + sunkenWidth
		distance2 = ust + sunkenWidth
		point1 = {distance1, distance2}
		point2 = {X-distance1, Y-distance2}
		G.cleanCorners(point1,point2,ad,cT)
    else
      G.setThickness(-ad)
      distance = a+sunkenWidth+cT/2
      distance2 = ust+sunkenWidth+cT/2
      point1 = {distance, distance2}
      point2 = {X-distance, Y-distance2}
      G.rectangle(point1,point2)
    end
  end
  return true
end

------------------------------------------------

require "ADekoDebugMode"
