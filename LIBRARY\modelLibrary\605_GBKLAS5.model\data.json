{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Freze_cW_mm\t\tcW Capli Freze bicagi \nK_Freze_cT_mm\t\tcT Capli Freze bicagi \nK_Raduslu__cD\t\tRaduslu Oyma bicagi \nH_Raduslu_Pah_DIS\tRaduslu pah bicagi \nK_Desen\t\tDesen-motif bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 25, "description": "Acili V kenardan gobege mesafe", "parameterName": "h"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 20, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 6, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 15, "description": "Radus veren bicak genis cap", "parameterName": "cD"}, {"defaultValue": 10, "description": "Radus veren bicak uc Duz capi", "parameterName": "cR"}, {"defaultValue": 5, "description": " vbit kanal radus-<PERSON><PERSON><PERSON> degeri", "parameterName": "R"}, {"defaultValue": 2, "description": "Ic kenar Ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeRtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}