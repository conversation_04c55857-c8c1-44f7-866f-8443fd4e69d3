-- Test script for STL download functionality
-- This script creates simple operations to test STL export

-- Create a rectangular pocket on the top face
pnlr("POCKET_TOP")  -- Pocketing operation
pnmv(200, 300)  -- Move to center area of door
pnln(300, 300)  -- Create a 100x100 rectangle
pnln(300, 400)
pnln(200, 400)
pnln(200, 300)

-- Create a circular hole on the top face
pnlr("DRILL_TOP")  -- Drilling operation
pncl(250, 350, 30)  -- Circle at center with radius 30

print("STL download test script completed")
print("Created rectangular pocket and circular hole")
print("To test STL download:")
print("1. Switch to 3D view")
print("2. Click 'CSG' or 'Advanced' to apply operations")
print("3. Click the purple 'STL' button to download the 3D model")
print("4. Check that 'door_model.stl' file is downloaded")
