-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  
  a = 30
  ad = 3
  spacing = 50  -- soft user setting
  
  extEdgeVtoolExist       	= 6  -- <PERSON><PERSON><PERSON> kenar <PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Ka<PERSON>k köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
	
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  local surface  = 0
  
  -- some math
  local howMany = math.floor((Y-2*a)/spacing)
  spacing = (Y-2*a)/howMany
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  G.setLayer("K_Kanal")
  G.setThickness(0)
  local i = 0
  
  for k=0, howMany-2, 1
  do
    i = i+1
    G.line({a, a+i*spacing, -ad}, {X/2, a+i*spacing, surface})
    G.line({X/2, a+i*spacing, surface}, {X-a, a+i*spacing, -ad})
  end
  
  G.setLayer("K_Kanal")
  G.rectangle({a, a, -ad}, {X-a, Y-a, -ad})
  return true
end

require "ADekoDebugMode"
