{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 0, "description": "----------JKULP HANGİ KENARLARDA----------", "parameterName": "-------"}, {"defaultValue": 4, "description": "<PERSON><PERSON> kena<PERSON>a?(<PERSON><PERSON>1,<PERSON><PERSON>2,<PERSON>g-3,<PERSON><PERSON>-4,solsag-13,altust-24)", "parameterName": "jk_side"}, {"defaultValue": 50, "description": "<PERSON><PERSON><PERSON> me<PERSON>", "parameterName": "a"}, {"defaultValue": 50, "description": "<PERSON><PERSON><PERSON> me<PERSON>", "parameterName": "b"}, {"defaultValue": 15, "description": "Yaklasma1", "parameterName": "j"}, {"defaultValue": 5, "description": "Yaklasma2", "parameterName": "k"}, {"defaultValue": 100, "description": "Uzunluk (<PERSON><PERSON> kulp olacaksa -b- degeri aktif olmasi icin 0 yap)", "parameterName": "l"}, {"defaultValue": 0, "description": "a ve b de<PERSON><PERSON><PERSON>n yerini degistirir(0-mevcut, 1-yer degistirir)", "parameterName": "transfer"}, {"defaultValue": 1, "description": "a ve b esit l jkulp yeri ortada(l>0 ise,0-ortalamaz 1-ortalar)", "parameterName": "ab_equal"}, {"defaultValue": 0, "description": "Panel olculerini k*2 kadar buy<PERSON>(0-<PERSON><PERSON>, 1-<PERSON><PERSON>)", "parameterName": "widIncrExist"}, {"defaultValue": 6, "description": "Kapak olcusune eklenen-fazlalık olan kenari kesen takim capi", "parameterName": "jNotchToolDia"}, {"defaultValue": 0, "description": "----------KOpr Parametsesi 0 Degilse Aktif Olur---------", "parameterName": "-----"}, {"defaultValue": 30, "description": "<PERSON><PERSON><PERSON> kanala mesafe", "parameterName": "<PERSON><PERSON><PERSON>"}, {"defaultValue": 1, "description": "Islem tipi(1-K_Kanal_TN)", "parameterName": "KOpr"}, {"defaultValue": 10, "description": "<PERSON><PERSON> der<PERSON>", "parameterName": "<PERSON><PERSON><PERSON><PERSON>"}, {"defaultValue": 41, "description": "<PERSON><PERSON>(TN)", "parameterName": "KToolNumber"}], "tags": [], "version": 1}