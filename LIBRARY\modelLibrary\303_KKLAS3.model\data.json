{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Freze_cT_mm\t\tcT Capli Freze bicagi \nCep_Acma\t\t Tarama duz Freze bicagi \nPANEL\t\tEbatlama bicagi\n----------------------------------------------------------------------------------------- \n Makro icindeki parametrelerden vbitOperation acili vbit veya rToolExist ile raduslu takim ile ic kenara islem ilave edilebilir \nK_Acili_aV\t\tAcili V bicagi \t*varsa IC kenar islemi icin* \nK_Raduslu__cD\t\tRaduslu bicagi  \t*varsa IC kenar islemi icin* \nH_Raduslu_Pah_DIS\tRaduslu pah bicagi \t*varsa DIS kenar raduslu pah islemi icin ", "modelParameters": [{"defaultValue": 150, "description": "X-minimum kenar u<PERSON>", "parameterName": "xMin"}, {"defaultValue": 150, "description": "Y-minimum kenar u<PERSON>u", "parameterName": "yMin"}, {"defaultValue": 250, "description": "X icin kucuk kapak deseni limit degeri", "parameterName": "xLimit"}, {"defaultValue": 250, "description": "Y icin kucuk kapak deseni limit degeri", "parameterName": "yLimit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 15, "description": "Acili V kenardan DIS kanala mesafe", "parameterName": "b"}, {"defaultValue": 15, "description": "DIS kenardan DIS kanala mesafe", "parameterName": "c"}, {"defaultValue": 20, "description": "<PERSON>t burun raduslu bicak genis capi", "parameterName": "cD"}, {"defaultValue": 10, "description": "<PERSON>t burun raduslu bicak dar capi", "parameterName": "cR"}, {"defaultValue": 6, "description": "Tara<PERSON> ve varsa vbit derinligi", "parameterName": "ad"}, {"defaultValue": 6, "description": "Ic kose finis ve kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 3, "description": "DIS kanal var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}