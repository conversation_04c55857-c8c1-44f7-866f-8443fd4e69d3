-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib 
  xMin = 140
  yMin = 140 
  a = 50  -- mm, distance from the edge on x axis
  b = 50  -- mm, distance from the edge on y axis
  d = 100 -- mm, diameter of the center circle
  aV = 90        -- Vbit Açısı
  ad = 12 			--Acili V derinligi
  cW = 20            -- Tarama Bıçak Çapı
  cT = 5            -- <PERSON>ç köşe temizleme ve pencere kesim bicagi
  ad = 12 	
  
  windowDepthFront = 14
  windowDepthBack = 6
  
  
  extEdgeVtoolExist       	= 0   -- Dış kenar <PERSON>h i<PERSON>lem<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok

  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end


  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  --if ((Y-2*b)/(X-2*a)<=1.5) then
  --  G.error("Must have vertical aspect ratio.")
  --  return false
  --end
  
  local debug = false

--  if (G.parseModelParameters(modelParameters)==false) then
--    return false
--  end
  
  local vMidDiameter = 40
  
  -- Set known points
  
  local p1 = {a, b}  
  local p2 = {X-a, b}
  local p3 = {X-a, Y-b}
  local p4 = {a, Y-b}
  local c0 = {X/2, Y/2}
  local s1 = G.ptAdd(c0, {-d/2, 0})
  local s2 = G.ptAdd(c0, {d/2, 0})
  local n1 = {a, Y/2}
  local n2 = {X/2, Y-b}
  local n3 = {X-a, Y/2}
  local n4 = {X/2, b}
  
  -- Calculate the unknown
  
  local bs1 = G.bulge(p4, s1, p1)
  local r = G.radius(p4, p1, bs1)
  comment, c1, dummy  = G.circleCircleIntersection(p4, r, p1, r )
  comment, dummy, c2  = G.circleCircleIntersection(p3, r, p2, r )
  comment, dummy, n12 = G.circleLineIntersection  (c1, r, n1, n2)
  comment, n23, dummy = G.circleLineIntersection  (c2, r, n3, n2)
  comment, n34, dummy = G.circleLineIntersection  (c2, r, n3, n4)
  comment, dummy, n14 = G.circleLineIntersection  (c1, r, n1, n4)
  local bn12 = G.bulge(p4, n12, s1)

  -- Draw the final polylines
 
  G.setLayer("K_AciliV" ..aV)
  G.setThickness(-0)
  
  p4[4] = bn12
  s1[4] = -0.99
  s2[4] = bn12
  
  local top = {}
  top[1] = p4
  top[2] = s1
  top[3] = s2
  top[4] = p3
  local bu = G.sunkenFrameAny(G.offSet(top, -cT), 20, ad, aV, vMidDiameter)
  
  G.setLayer("H_Freze_" ..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(bu)
  
  G.setLayer("K_AciliV" ..aV)
  G.setThickness(0)
  p2[4] = bn12
  s2[4] = -0.99
  s1[4] = bn12
  
  local bottom = {}
  bottom[1] = p1
  bottom[2] = p2
  bottom[3] = s2
  bottom[4] = s1
  bottom[5] = p1
  local buu = G.sunkenFrameAny(G.offSet(bottom, -cT), 20, ad, aV, vMidDiameter)
  
  G.setLayer("H_Freze_" ..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(buu)
  
  G.setLayer("K_AciliV" ..aV)
  G.setThickness(0)
  p4[4] = bs1
  p1[4] = -0.001
  
  local left = {}
  left[1] = p1
  left[2] = p4
  left[3] = p1
  buu = localSunkenFrameAny(G.offSet(left, cT), 20, -ad, aV, vMidDiameter)
  
  G.setLayer("H_Freze_" ..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(buu)
  
  p2[4] = bs1
  p3[4] = -0.001
  
  G.setLayer("K_AciliV" ..aV)
  G.setThickness(0)
  local right = {}
  right[1] = p2
  right[2] = p3
  right[3] = p2
  buu = localSunkenFrameAny(G.offSet(right, cT), 20, -ad, aV, vMidDiameter)
  
  G.setLayer("H_Freze_" ..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(buu)
  
  G.setLayer("H_AciliV" ..aV.."_Ic")
  G.setThickness(-ad)
  s1 = G.ptAdd(s1, {-cT,0})
  s2 = G.ptAdd(s2, {cT,0})
  s1[4] = 0.99
  s2[4] = 0.99
  G.polyline(s2, s1, s2)
  
  G.setLayer("H_Freze"..cT.."mm_Dis")
  G.setThickness(-windowDepthFront)
  local m = 24 * math.tan((math.pi*90/180)/2.0)
  ss1 = G.ptAdd(s1, {m,0})
  ss2 = G.ptAdd(s2, {-m,0})
  ss1[4] = 0.99
  ss2[4] = 0.99
  G.polyline(ss2, ss1, ss2)
  
  G.setFace("bottom")
   
  G.setLayer("H_Freze_" ..cW.."mm_Ic_SF")  -- arkadaki cam için olan çerçeve
  G.setThickness(-windowDepthBack)
  G.rectangle({a-cW/2, b-cW/2}, {X-a+cW/2, Y-b+cW/2})
  
  G.setLayer("K_Freze_" ..cW.."mm_SF")  -- arkadaki cam için çitalar üzerindeki hat
  G.line(p4, p1, bs1)
  G.line(p2, p3, bs1)
  G.circle(c0, d/2)
  
  
  if (debug) then
    G.line(s1, s2, 0.99)
    G.line(s1, s2, -0.99)
    G.line(p4, p1, bs1)
    G.line(p2, p3, bs1)
  end
  
  return true
end

-------------------------------------------
function localSunkenFrameAny(inputPoints, cornerArcDivider, depth, vToolAngle, vToolDiameter)
  
  local points = {}
  points = ADekoLib.deepcopy(inputPoints)
  
  local m = depth * math.tan((math.pi*vToolAngle/180)/2.0)
  if (m > vToolDiameter/2) then
    ADekoLib.error("Tool diameter too small")
    return false
  end
  
  -- get the points forming the sharp edge lines or curves by consecutive offsetting
  local offSets = {}
  local step = m/cornerArcDivider
  for i=1, cornerArcDivider+1, 1
  do
    offSets[i] = {}
    offSets[i] = ADekoLib.deepcopy(ADekoLib.offSet(points, -(i-1)*step))
  end
  
  -- get corners as separate polylines
  local n = #points
  corners = {}
  foldeds = {}
  for i=1, n, 1
  do
    corners[i] = {}
    foldeds[i] = {}
    for j=1, cornerArcDivider+1, 1
    do
      corners[i][j] = ADekoLib.deepcopy(offSets[j][i])
      corners[i][j][3] = (j-1)*depth/cornerArcDivider 
      corners[i][j][4] = 0
      foldeds[i][cornerArcDivider+2-j] = ADekoLib.deepcopy(corners[i][j])
      foldeds[i][cornerArcDivider+1+j] = ADekoLib.deepcopy(corners[i][j])
      if (j==cornerArcDivider+1) then
        foldeds[i][cornerArcDivider+1+j][4] = offSets[j][i][4]
      end
    end
  end
  
  -- form the resulting polyline/toolpath
  local result = {}
  local counter = 1
  for i=1, n, 1
  do
    for j=1, #foldeds[i], 1
    do
      table.insert(result, counter, foldeds[i][j])
      counter = counter + 1
    end
  end
  result[#result][4] = 0
  
  G.polylineimp(result)
  return offSets[cornerArcDivider+1]
end

require "ADekoDebugMode"