-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 350
 
  a = 50                    -- Kenardan Vbite
  aa = 20					-- Dar kapak için Kenardan mesafe
  c = -2.5						-- Dıştaki kanalın kapak kenarına mesafesi
  h = 30                    -- Vbitten göbeğe
  d = 5                    -- Göbekten iç kanala veya göbek vbit üstü kanal
  ad = 6                    -- Vbit derinliği (sunkenDepth)
  aV = 90                  -- Vbit Açısı
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 6                   -- İnce bıçak çapı (Köşe Temizleme)
  R = 25		        		-- Kose Radus
  
  extGrooveExist          	= 2   -- Dış kanal ve-veya V bit üstü var mı? derinlik/0:yok
	edgeCornerRExist          	= 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
	topGrooveExist				= 2
  
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
	
	 local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  --bd = 3                    -- dış kanal derinliği
  --local hd = 5              -- Göbek Desen Bıcak derinliği
  --local dd = 2              -- iç kanal derinliği
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
		
	local intGrooveExist        = topGrooveExist   -- Göbekte vbit üstü veya içeride kanal var mı? derinlik/ 0:yok
	local D = edgeCornerRExist
	
	
	local vWideDiameter = 60
	local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)     --indiği derinlikte yarısı oluyor
	
		
	local B = math.tan(math.pi/8)

	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist >0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	minXCalculated = 2*a + 2*sunkenWidth + 2*h
	minYCalculated = 2*ust + 2*sunkenWidth + 2*h
	
	if cW <= 0 then
		cW = cT
	end
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
	
	function BulgePoly(distance1, distance2, R)
		local points = {
			{distance1+R, distance2,0,0},			--{D,0},
			{X-distance1-R, distance2,0,B},		--{X-D,0,0,B},
			{X-distance1, distance2+R,0,0},			--{X,D},
			{X-distance1, Y-distance2-R,0,B},	--{X,Y-D,0,B},
			{X-distance1-R, Y-distance2,0,0},		--{X-D,Y},
			{distance1+R, Y-distance2,0,B},		--{D,Y,0,B},
			{distance1, Y-distance2-R,0,0},			--{0,Y-D},
			{distance1, distance2+R,0,B},		--{0,D,0,B},
			{distance1+R, distance2,0,0}			--{D,0}
			}	
			return points
	end
	
	if topGrooveExist > 0 then
		G.setLayer("H_Freze"..cT.."mm_Ic")  -- DEEP cleanup
		G.setThickness(-topGrooveExist)
		distance1 = a
		distance2 = ust
		points1 = BulgePoly(distance1,distance2,R)
		G.polylineimp(points1)
	end
	
	if extGrooveExist > 0 then
		G.setLayer("H_Freze_"..cT.."mm_DIS")  -- DEEP cleanup
		G.setThickness(-topGrooveExist)
		distance1 = c
		distance2 = c
		point1 = {distance1, distance2}
		point2 = {X-distance1, Y-distance2}
		G.rectangle(point1,point2)
	end
  
	local cCornersExist 		= false
	G.setLayer("K_AciliV" .. aV)  --- Gobek
	G.setThickness(-ad)	
	R1 = R-sunkenWidth
	distance1 = a + sunkenWidth
	distance2 = ust + sunkenWidth
	if R1 > 0 then
		points2 = BulgePoly(distance1,distance2,R1)
		G.polylineimp(points2)
	else
		point12 = {distance1, distance2,0}
		point13 = {X-distance1, Y-distance2,0}
		G.rectangle(point12,point13)
		cCornersExist = true
	end
	
	--G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	--G.setThickness(0)
	--local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	--corner1[3] = 0
	--corner2[3] = 0
	
	G.setLayer("K_AciliV" .. aV)  --- Gobek - Dıs Vbit
	G.setThickness(-ad)
	distance = a + sunkenWidth + h
	distance2 = ust + sunkenWidth + h
	point1 = {distance, distance2}
	point2 = {X-distance, Y-distance2}
	G.rectangle(point1,point2)
	
	
	if h<cT or h<cW then
		print("Tool too large")
		return true
	end
	
	
  if h==cW then
    G.setLayer("K_Freze"..cW.."mm") 
    G.setThickness(-ad)
    R2= R-sunkenWidth-cW/2
    if R2 > cW/2 then
      distance1 = a + sunkenWidth+cW/2
      distance2 = ust + sunkenWidth+cW/2
      points2 = BulgePoly(distance1,distance2,R2)
      G.polylineimp(points2)
    else
      distance1 = a+sunkenWidth+cW/2
      distance2 = ust+sunkenWidth+cW/2
      point1 = {distance1, distance2}
      point2 = {X-distance1, Y-distance2}
      G.rectangle(point1,point2)
    end
  else  
    G.setLayer("Cep_Acma")
    G.setThickness(-ad)
    if R1 > cW/2 then
      distance1 = a + sunkenWidth
      distance2 = ust + sunkenWidth
      points2 = BulgePoly(distance1,distance2,R1)
      G.polylineimp(points2)
    else 
      distance1 = a+sunkenWidth
      distance2 = ust+sunkenWidth
      point1 = {distance1, distance2}
      point2 = {X-distance1, Y-distance2}
      G.rectangle(point1,point2)    
    end
    distance = a+sunkenWidth+h
    distance2 = ust+sunkenWidth+h
    point10 = {distance, distance2}
    point11 = {X-distance, Y-distance2}
    G.rectangle(point10,point11)
  end

	if intGrooveExist > 0 then      ------İç kanal varsa
		local checkX = 2*a + 2*h + 2*d
		local checkY = 2*ust + 2*h + 2*d
		if X < checkX or Y < checkY then
			print("Part dimension too small, check a + h + d value")
			return true
		end
		G.setLayer("K_Freze"..cT.."mm") --içteki göbek vbit üstü kanal
		G.setThickness(-intGrooveExist)
		
		distance = a + 2*sunkenWidth + h + d
		distance2 = ust + 2*sunkenWidth + h + d
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	end
		
	G.setLayer("K_TarKoseTemizl"..cT.."mm")
	if cCornersExist then
		if cW ~= cT then
			if h >= 3*cT then
				G.setThickness(0)
				G.cleanCorners(point12,point13,ad,cT)
			end
		end
	else
		R2 = R1 - cT/2
        G.setThickness(-ad)
		distance1 = a + sunkenWidth + cT/2
		distance2 = ust + sunkenWidth + cT/2
		points3 = BulgePoly(distance1,distance2,R2)
		G.polylineimp(points3)
	end
	
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
