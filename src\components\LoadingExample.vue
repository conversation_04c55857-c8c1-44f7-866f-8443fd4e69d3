<template>
  <div class="loading-example">
    <h2>Loading Examples</h2>
    
    <div class="example-buttons">
      <button @click="showBasicLoader" class="btn">Basic Loader</button>
      <button @click="showProgressLoader" class="btn">Progress Loader</button>
      <button @click="showErrorLoader" class="btn">Error Loader</button>
      <button @click="showCustomLoader" class="btn">Custom Loader</button>
      <button @click="simulateFileOperation" class="btn">File Operation</button>
    </div>

    <!-- Basic Loader -->
    <AppLoader
      v-if="showBasic"
      :visible="showBasic"
      title="Loading Data"
      subtitle="Please wait while we fetch your data"
      :status-message="basicStatus"
      :show-progress="false"
      @hidden="showBasic = false"
    />

    <!-- Progress Loader -->
    <AppLoader
      v-if="showProgress"
      :visible="showProgress"
      title="Processing Files"
      subtitle="This may take a few moments"
      :status-message="progressStatus"
      :progress="progressValue"
      :show-progress="true"
      @hidden="showProgress = false"
    />

    <!-- Error Loader -->
    <AppLoader
      v-if="showError"
      :visible="showError"
      title="Operation Failed"
      subtitle="Something went wrong"
      :status-message="errorStatus"
      :progress="100"
      :show-progress="true"
      :has-error="true"
      @hidden="showError = false"
    >
      <template #content>
        <button @click="showError = false" class="btn btn-error">Close</button>
      </template>
    </AppLoader>

    <!-- Custom Loader -->
    <AppLoader
      v-if="showCustom"
      :visible="showCustom"
      title="Custom Operation"
      subtitle="With custom icon and styling"
      :status-message="customStatus"
      :progress="customProgress"
      :show-progress="true"
      icon-text="🚀"
      icon-class="custom-icon"
      @hidden="showCustom = false"
    >
      <template #content>
        <div class="custom-content">
          <p>Additional information can go here</p>
          <button @click="showCustom = false" class="btn">Cancel</button>
        </div>
      </template>
    </AppLoader>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppLoader from './AppLoader.vue'
import { useLoading } from '@/composables/useLoading'

const { withLoading } = useLoading()

// Basic loader state
const showBasic = ref(false)
const basicStatus = ref('Loading...')

// Progress loader state
const showProgress = ref(false)
const progressStatus = ref('Starting...')
const progressValue = ref(0)

// Error loader state
const showError = ref(false)
const errorStatus = ref('Failed to connect to server')

// Custom loader state
const showCustom = ref(false)
const customStatus = ref('Launching...')
const customProgress = ref(0)

// Example functions
const showBasicLoader = async () => {
  showBasic.value = true
  basicStatus.value = 'Connecting to server...'
  
  setTimeout(() => {
    basicStatus.value = 'Fetching data...'
  }, 1000)
  
  setTimeout(() => {
    basicStatus.value = 'Processing results...'
  }, 2000)
  
  setTimeout(() => {
    basicStatus.value = 'Complete!'
    setTimeout(() => {
      showBasic.value = false
    }, 500)
  }, 3000)
}

const showProgressLoader = async () => {
  showProgress.value = true
  progressStatus.value = 'Initializing...'
  progressValue.value = 0
  
  const interval = setInterval(() => {
    progressValue.value += 10
    
    if (progressValue.value === 30) {
      progressStatus.value = 'Processing files...'
    } else if (progressValue.value === 60) {
      progressStatus.value = 'Applying changes...'
    } else if (progressValue.value === 90) {
      progressStatus.value = 'Finalizing...'
    } else if (progressValue.value >= 100) {
      progressStatus.value = 'Complete!'
      clearInterval(interval)
      setTimeout(() => {
        showProgress.value = false
      }, 500)
    }
  }, 300)
}

const showErrorLoader = () => {
  showError.value = true
}

const showCustomLoader = async () => {
  showCustom.value = true
  customStatus.value = 'Preparing launch sequence...'
  customProgress.value = 0
  
  const interval = setInterval(() => {
    customProgress.value += 5
    
    if (customProgress.value === 25) {
      customStatus.value = 'Checking systems...'
    } else if (customProgress.value === 50) {
      customStatus.value = 'Igniting engines...'
    } else if (customProgress.value === 75) {
      customStatus.value = 'Final countdown...'
    } else if (customProgress.value >= 100) {
      customStatus.value = 'Liftoff! 🚀'
      clearInterval(interval)
      setTimeout(() => {
        showCustom.value = false
      }, 1000)
    }
  }, 200)
}

const simulateFileOperation = async () => {
  try {
    await withLoading(
      async () => {
        // Simulate file processing
        await new Promise(resolve => setTimeout(resolve, 2000))
      },
      {
        message: 'Processing files...',
        duration: 2000,
        showProgress: true
      }
    )
    console.log('File operation completed!')
  } catch (error) {
    console.error('File operation failed:', error)
  }
}
</script>

<style scoped>
.loading-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.example-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  background: #007acc;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.btn:hover {
  background: #005a9e;
}

.btn-error {
  background: #ff4757;
}

.btn-error:hover {
  background: #ff3742;
}

.custom-content {
  text-align: center;
  color: #b0b0b0;
}

.custom-content p {
  margin-bottom: 15px;
  font-size: 14px;
}

:deep(.custom-icon .default-icon) {
  background: linear-gradient(45deg, #ff6b7a, #ff9ff3);
  font-size: 40px;
}
</style>
