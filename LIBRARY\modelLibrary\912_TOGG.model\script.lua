-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 240
  
  a = 50
  aa = 30
  ad = 8
  aV = 120
  surface  = 0		
  
  extEdgeVtoolExist       	= 6  -- <PERSON><PERSON><PERSON> kenar <PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kö<PERSON>e radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end

  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
	
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end

  G.setThickness(-materialThickness)
  G.makePartShape()

  local points1 = {{X/2,ust,-surface},{a+sunkenWidth,Y/2,-ad},{a,Y/2,-surface},{a+sunkenWidth,Y/2,-ad},{X/2,Y-ust,-surface}} --sol
  local points2 = {{X/2,ust,-surface},{(X-a)-sunkenWidth,Y/2,-ad},{(X-a),Y/2,-surface},{(X-a)-sunkenWidth,Y/2,-ad},{X/2,Y-ust,-surface}} --sag
  
  --local points11 = {{a, Y/2, surface},{a,Y-ust,-ad},{a,Y-b,surface}} --sol yukarı
  --local points2 = {{X-a, Y/2, surface},{X-a,Y-ust,-ad},{X-a,Y-b,surface}} --sag yukarı
  --local points22 = {{X-a, Y/2, surface},{X-a,ust,-ad},{X-a, b,surface}} --sag asagı
  --local points3 = {{X/2, ust, surface},{X-a,ust,-ad},{X-b,ust,surface}} --alt saga
  --local points33 = {{X/2, ust, surface},{a,ust,-ad},{b,ust,surface}} --alt sola
  --local points4 = {{b, Y-ust, surface},{a,Y-ust,-ad},{X/2,Y-ust,surface}} --ust sola
  --local points44 = {{X-b, Y-ust, surface},{X-a,Y-ust,-ad},{X/2,Y-ust,surface}} --ust saga
  
  G.setLayer("K_AciliV" ..aV)
  G.setThickness(0)
  --G.rectangle({a, a, -ad}, {X-a, Y-a, -ad})
  
  G.polylineimp (points1)
  G.polylineimp (points2)
  
  --G.polylineimp (points11)
    --G.polylineimp (points22)
  --G.polylineimp (points3)
  --G.polylineimp (points33)
  --G.polylineimp (points4)
  --G.polylineimp (points44)
  return true
end

require "ADekoDebugMode"
