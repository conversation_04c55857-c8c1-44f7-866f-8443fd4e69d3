-- <PERSON>ek<PERSON>CA<PERSON>, Model Script - Fillet Corner Frame Test
-- Converted from C# azCAM macro: Fillet Corner Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Fillet Corner Frame parameters (from original C# macro)
  A = 50    -- Corner radius/fillet size
  B = 100   -- Frame margin/inset
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * B + 2 * A + 20   -- minimum required width
  local minHeight = 2 * B + 2 * A + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Fillet Corner Frame function (converted from C# macro)
  local function fillet_corner_frame(A, B, Z)
    -- Use default values if parameters not provided
    A = A or 50
    B = B or 100
    Z = Z or -5
    
    print(string.format("Creating fillet corner frame"))
    print(string.format("Parameters: A=%d (corner radius), B=%d (frame margin), Z=%d", A, B, -Z))
    
    -- Calculate all points (following C# logic exactly)
    -- Arc/line connection points
    local p1 = {A, B}                    -- Bottom-left arc start
    local p2 = {B, A}                    -- Bottom-left arc end / bottom line start
    local p3 = {width - B, A}            -- Bottom line end / bottom-right arc start
    local p4 = {width - A, B}            -- Bottom-right arc end / right line start
    local p5 = {width - A, height - B}   -- Right line end / top-right arc start
    local p6 = {width - B, height - A}   -- Top-right arc end / top line start
    local p7 = {B, height - A}           -- Top line end / top-left arc start
    local p8 = {A, height - B}           -- Top-left arc end / left line start
    
    -- Corner centers for arcs
    local c1 = {A, A}                    -- Bottom-left corner center
    local c2 = {width - A, A}            -- Bottom-right corner center
    local c3 = {width - A, height - A}   -- Top-right corner center
    local c4 = {A, height - A}           -- Top-left corner center
    
    -- Validate that frame fits within panel bounds
    if B < A then
      print("Warning: Frame margin (B) is smaller than corner radius (A)")
    end
    
    -- Set layer and thickness for frame
    G.setLayer("Default")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the filleted frame using polyline with bulges for arcs
    -- Calculate bulge for 90-degree arc: bulge = tan(angle/4) = tan(90°/4) = tan(22.5°) ≈ 0.414
    local bulge = math.tan(math.pi / 8)  -- ≈ 0.414
    
    -- Create the frame as a continuous polyline with bulges at corners
    -- Start from p1 and go clockwise around the frame
    local framePoints = {
      {p1[1], p1[2], 0, bulge},  -- p1 with bulge to create arc to p2
      {p2[1], p2[2], 0, 0},      -- p2 (straight line to p3)
      {p3[1], p3[2], 0, bulge},  -- p3 with bulge to create arc to p4
      {p4[1], p4[2], 0, 0},      -- p4 (straight line to p5)
      {p5[1], p5[2], 0, bulge},  -- p5 with bulge to create arc to p6
      {p6[1], p6[2], 0, 0},      -- p6 (straight line to p7)
      {p7[1], p7[2], 0, bulge},  -- p7 with bulge to create arc to p8
      {p8[1], p8[2], 0, 0},      -- p8 (straight line back to p1)
      {p1[1], p1[2], 0, 0}       -- Close the frame
    }
    
    -- Create the filleted frame
    G.polyline(table.unpack(framePoints))
    
    print(string.format("Filleted frame key points:"))
    print(string.format("  Corner centers: (%.1f,%.1f), (%.1f,%.1f), (%.1f,%.1f), (%.1f,%.1f)", 
          c1[1], c1[2], c2[1], c2[2], c3[1], c3[2], c4[1], c4[2]))
    print(string.format("  Frame bounds: (%.1f,%.1f) to (%.1f,%.1f)", 
          p2[1], p2[2], p6[1], p6[2]))
    print(string.format("  Corner radius: %.1f mm", A))
    print(string.format("  Frame margin: %.1f mm", B))
    
    return true
  end
  
  -- Call the fillet corner frame function
  local success = fillet_corner_frame(A, B, -Z)
  
  if success then
    print(string.format("Fillet corner frame created with parameters:"))
    print(string.format("  A (corner radius): %d mm", A))
    print(string.format("  B (frame margin): %d mm", B))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a rectangular frame with rounded corners")
    print("  - Four 90-degree arcs at each corner")
    print("  - Four straight line segments connecting the arcs")
    print("  - Frame is inset by margin B from panel edges")
    print("  - Corner radius A determines the roundness")
    print("")
    print("Applications:")
    print("  - Modern furniture with soft corners")
    print("  - Safety-conscious designs (no sharp corners)")
    print("  - Decorative frames with contemporary appearance")
    print("  - Window or door frames with rounded corners")
  end
  
  return true
end

require "ADekoDebugMode"
