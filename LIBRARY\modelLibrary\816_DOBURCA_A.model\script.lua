-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib	
	minimum = 150
	limit = 300
	
  
  a = 60  
  aa = 30
  ad = 6                -- Vbit derinliği (sunkenDepth)
  aV = 120              -- Vbit Açısı
  yy = 25				-- yay kısmının yüksekliği
  h = 24
  m = 40
  cT = 8                -- İnce bıçak çapı (Köşe Temizleme)
  
  topGrooveExist   				= 2   -- Vbit Ustu kanal var mı? derinlik/0:yok
  topGrvExtExist				= 1 --Ust kanal uzatma var mı? (Var:1/Yok:0)
  shapeToolExist 				= 5   -- Göbek Desen bıçağı var mı? derinlik/0:yok
  edgeCornerRExist          	= 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  extEdgeVtoolExist      		= 0   -- <PERSON><PERSON><PERSON> kenar Pah işlemi var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  local gaps = 30
  
  local intVerticalGrooveExist 	= 0   -- iç kanal var mı? dik çizgiler  derinlik/ 0:yok
  
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)

  local Dd = edgeCornerRExist
  local Bb = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape(        ----chamfered part shape
      {Dd,0},		
      {X-Dd,0,0,Bb},
      {X,Dd},
      {X,Y-Dd,0,Bb},
      {X-Dd,Y},
      {Dd,Y,0,Bb},
      {0,Y-Dd},
      {0,Dd,0,Bb},
      {Dd,0}
      )
  else
    G.makePartShape()
  end
  
	if extEdgeVtoolExist > 0 then
		-- G.setLayer("K_AciliV" ..aV)	
		G.setLayer("K_AciliV_Pah")	
		G.setThickness(-extEdgeVtoolExist)
		G.rectangle({0,0},{X,Y})
		
	end

	if X < xMin or Y < yMin then
	print("Part dimension too small")
	return true
	end
	
	if aa == 0 then
		aa = a
	end
	
	local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
	
	local points = {{X-a, Y-yy-ust},{X-a-m, Y-yy-ust},{X-a-m-yy, Y-ust}, {a+m+yy, Y-ust}, {a+m, Y-yy-ust}, {a, Y-yy-ust}, {a, yy+ust}, {a+m, yy+ust}, {a+m+yy, ust}, {X-a-m-yy , ust}, {X-a-m , yy+ust}, {X-a , yy+ust}, {X-a, Y-yy-ust}}
	
	if topGrooveExist > 0 then
		G.setLayer("H_Freze"..cT.."mm_Ic")
  --		 if Y>yLimit and X>xLimit then
		G.setThickness(-topGrooveExist)
		G.polylineimp(points)
		
		if topGrvExtExist == 1 then
			G.setLayer("K_Freze"..cT.."mm")
			G.line({X-a-cT/2, Y-yy-ust-cT/2},{X-a-cT/2, Y})
			G.line({a+cT/2, Y-yy-ust-cT/2},{a+cT/2, Y})
			G.line({a+cT/2, yy+ust+cT/2},{a+cT/2, 0})
			G.line({X-a-cT/2, yy+ust+cT/2},{X-a-cT/2, 0})
		end
	end
	
	G.setLayer("K_AciliV" ..aV)
  -- if Y>yLimit and X>xLimit then
    G.setThickness(-0)
    G.sunkenFrameAny(points, 30,ad,aV,vWideDiameter)
	
	if h >= cT then
		
		if shapeToolExist >0 then
			G.setLayer("K_Desen")
			G.setThickness(-shapeToolExist)
		else
			G.setLayer("K_AciliV" ..aV)
			G.setThickness(-ad)
		end
		G.polylineimp (G.offSet(points, -(sunkenWidth+h)))
		
		G.setLayer("K_Freze"..cT.."mm")
  --	 if Y>yLimit and X>xLimit then
		G.setThickness(-ad)
		point1 = G.offSet(points, -(sunkenWidth+cT/2))
		G.polylineimp(point1)
		
		if h > cT then
			point2 = G.offSet(points, -(sunkenWidth+h-cT/2))
			G.polylineimp(point2)
			
			k = (h-cT)/(cT/2)
		
			for i=1, k, 1 do
				point1 = G.offSet(point1, -2*cT/3)
				if point1[1][1]<point2[1][1]-cT/2 then
				break
				end
				G.polylineimp(point1)
			end
		end
	end
	
  return true
end


----------------------------------------------
require "ADekoDebugMode"