-- <PERSON>ek<PERSON>CA<PERSON>, Model Script - Ellipse Frame Test
-- Converted from C# azCAM macro: Ellipse Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Ellipse Frame parameters (from original C# macro)
  A = 1     -- Left margin
  B = 1     -- Right margin
  C = 1     -- Top margin
  D = 1     -- Bottom margin
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + B + 20   -- minimum required width
  local minHeight = C + D + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Ellipse Frame function (converted from C# macro)
  local function ellipse_frame(A, B, C, D, Z)
    -- Use default values if parameters not provided
    A = A or 1
    B = B or 1
    C = C or 1
    D = D or 1
    Z = Z or -5
    
    -- Calculate ellipse parameters (following C# logic exactly)
    -- Center: (A+(width-A-B)/2, D+(height-C-D)/2)
    -- X-radius: (width-A-B)/2
    -- Y-radius: (height-C-D)/2
    
    local ellipseWidth = width - A - B
    local ellipseHeight = height - C - D
    local centerX = A + ellipseWidth / 2
    local centerY = D + ellipseHeight / 2
    local xRadius = ellipseWidth / 2
    local yRadius = ellipseHeight / 2
    
    -- Validate ellipse dimensions
    if ellipseWidth <= 0 or ellipseHeight <= 0 then
      print("Error: Ellipse dimensions are invalid")
      print(string.format("Ellipse size: %.1f x %.1f", ellipseWidth, ellipseHeight))
      return false
    end
    
    print(string.format("Creating ellipse frame"))
    print(string.format("Center: (%.1f, %.1f)", centerX, centerY))
    print(string.format("Radii: X=%.1f, Y=%.1f", xRadius, yRadius))
    print(string.format("Ellipse size: %.1f x %.1f", ellipseWidth, ellipseHeight))
    
    -- Set layer and thickness for ellipse
    G.setLayer("C_5MM_Inner")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create ellipse using ADekoLib.ellipse function
    -- ADekoLib.ellipse(centerPoint, width, height, noOfSegments)
    local noOfSegments = 64  -- High resolution for smooth ellipse
    local ellipsePoints = G.ellipse({centerX, centerY}, ellipseWidth, ellipseHeight, noOfSegments)
    
    -- Create the ellipse using polyline
    if ellipsePoints and #ellipsePoints > 0 then
      -- Add the first point at the end to close the ellipse
      table.insert(ellipsePoints, ellipsePoints[1])
      G.polyline(table.unpack(ellipsePoints))
      
      print(string.format("Ellipse created with %d segments", noOfSegments))
    else
      print("Error: Failed to generate ellipse points")
      return false
    end
    
    return true
  end
  
  -- Call the ellipse frame function
  local success = ellipse_frame(A, B, C, D, -Z)
  
  if success then
    print(string.format("Ellipse frame created with parameters:"))
    print(string.format("  A (left margin): %d mm", A))
    print(string.format("  B (right margin): %d mm", B))
    print(string.format("  C (top margin): %d mm", C))
    print(string.format("  D (bottom margin): %d mm", D))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a perfect elliptical frame")
    print("  - Ellipse is centered within the specified margins")
    print("  - Automatically scales to fit available space")
    print("  - Uses high-resolution segments for smooth curves")
    print("")
    print("Applications:")
    print("  - Oval windows or openings")
    print("  - Decorative elliptical frames")
    print("  - Artistic oval design elements")
    print("  - Furniture with curved openings")
  end
  
  return true
end

require "ADekoDebugMode"
