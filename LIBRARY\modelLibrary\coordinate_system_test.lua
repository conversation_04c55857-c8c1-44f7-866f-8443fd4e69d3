-- Coordinate System Test Script
-- This script tests both simple turtle graphics (center origin) and ADekoLib face layout (top-left origin)

function modelMain()
    print("=== Coordinate System Test ===")
    
    -- Test 1: Simple turtle graphics (should appear at center)
    print("Drawing simple turtle graphics at center...")
    
    -- Clear screen and set up
    wipe()
    pndn()  -- Put pen down
    pncl("blue")  -- Set pen color to blue
    pnsz(2)  -- Set pen size to 2
    
    -- Draw a simple square at origin (0,0)
    for i = 1, 4 do
        move(50)  -- Move forward 50 pixels
        turn(90)   -- Turn 90 degrees
    end
    
    -- Draw a circle
    pnup()
    posn(100, 0)
    pndn()
    pncl("red")
    crcl(0, 0, 30)
    
    -- Add some text
    pnup()
    posn(-50, 100)
    text("Simple Turtle Graphics", 0, 0, 0)
    
    print("Simple turtle graphics completed!")
end
