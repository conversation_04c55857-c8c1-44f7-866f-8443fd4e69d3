-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  
  a = 60
  ad = 5
  n = 2
  m = 10    --_ glass margin
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 10
  
  windowDepthFront = 14
  windowDepthBack = 6
  
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> i<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
    
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> radü<PERSON><PERSON> yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  local radius = 0.24
  local origX = X
  local origY = Y
  -- X = 500
  -- Y = 700
  
  if (X<xMin or Y<yMin) then
    return true
  end
  
  G.setLayer("K_Desen")
  G.setThickness(-ad)
  local p1 = {a, Y}
  local p2 = {X/2, Y-(1-2*radius)*X}
  local p3 = {X-a, Y}
  comment, dp12, p12 = G.circleLineIntersection(p1, radius*X, p1, p2)
  comment, p23, dp23 = G.circleLineIntersection(p3, radius*X, p2, p3)
  local rP2 = G.distance(p2, p12)
  comment, tepe, dik2 = G.circleLineIntersection(p2, rP2, p2, {p2[1], Y})
  local bulge = G.bulge(p12, tepe, p23)
  --G.line(p12, p23, bulge)   --Middle line
  p22 = G.ptAdd(p2, {-X/4, 0})
  p222 = G.ptAdd(p2, {X/4, 0})
  comment, dtP1, tP1 = G.circleLineIntersection(p1, radius*X, p1, p22)
  comment, tP3, dtP3 = G.circleLineIntersection(p3, radius*X, p3, p222)
  local n1, n2 = {a, Y-radius*X}, {radius*X, Y}
  bulge = G.bulge(n1, tP1, p12)
  --G.line(n1, p12, bulge)    --Left line
  local m1, m2 = {X-a, Y-radius*X}, {X-radius*X-a, Y}
  bulge = G.bulge(m1, tP3, p23)
  --G.line(m1, p23, bulge)    --Right line
  
  local pointsOrta = G.circularArc(p2, 2*rP2, 500, 56, 127)
  local pointsSol  = G.circularArc(p1, 2*radius*X, 100, 270, 305)
  local pointsSag  = G.circularArc(p3, 2*radius*X, 100, 230, 270)

  local arcs  = G.joinPolylines(G.joinPolylines(pointsOrta, pointsSag), pointsSol)
  local scaledArcsTop = G.scaleVertical(G.scaleHorizontal(arcs, 2*a, origX-2*a), origY-n*a, origY-a)
  local final = G.joinPolylines(scaledArcsTop, {{origX-a, origY-n*a}, {origX-a, a}, {a, a}, {a, origY-n*a}, scaledArcsTop[1]})
  G.polylineimp(final)
  
  G.showPar({0, Y/2}, {a, Y/2}, "a")
  G.showPar({X/2, 0}, {X/2, a}, "a")
  G.showPar({X/2, Y}, {X/2, Y-a}, "a")
  G.showPar({1.1*a, Y}, {1.1*a, Y-n*a}, "n*a")
  
  G.setLayer("H_Freze"..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.polylineimp(final)
  
  G.setFace("bottom")
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
    G.setThickness(-windowDepthBack)
  G.polylineimp(G.offSet(final, -m))
  
  G.showPar({0, Y/2}, {a-m, Y/2}, "a-m")
  G.showPar({X/2, Y}, {X/2, Y-a+m}, "a-m")
  G.showPar({X/2, 0}, {X/2, a-m}, "a-m")
  G.showPar({1.1*(a-m), Y}, {1.1*(a-m), Y-n*a+m}, "n*a-m")
  
  return true
end

require "ADekoDebugMode"