{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV*\t\t*aV* Derece V Bicak \nH_Freze*cT*mm_Ic\t*cT*mm Freze Bicagi \nH_Freze*cW*mm_Ic_SF\t*cW*mm Freze Bicagi \nK_Freze*cW*mm_SF\t*cW*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 50, "description": "offset from the edges", "parameterName": "a"}, {"defaultValue": 40, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 120, "description": "Acili vbit acisi", "parameterName": "aV"}, {"defaultValue": 5, "description": "width of the block laths", "parameterName": "c"}, {"defaultValue": 2, "description": "width of every square block (*0* for using the limits in the script)", "parameterName": "blockNoX"}, {"defaultValue": 4, "description": "length of every square block (*0* for using the limits in the script)", "parameterName": "blockNoY"}, {"defaultValue": 10, "description": "Cam yeri kesim bicak capi", "parameterName": "cT"}, {"defaultValue": 20, "description": "Cam yeri tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 14, "description": "mm, window depth from top", "parameterName": "windowDepthFront"}, {"defaultValue": 6, "description": "mm, window depth from bottom", "parameterName": "windowDepthBack"}, {"defaultValue": 6, "description": "Ic kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}