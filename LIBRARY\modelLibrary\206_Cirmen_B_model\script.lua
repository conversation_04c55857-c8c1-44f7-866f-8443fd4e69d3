-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  marginX = 10         -- Hard user setting
  marginY = 10
  structureSizeX = 20  -- Soft user setting
  structureSizeY = 20
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local a = 82
  local firstDepth = 3
  local finalDepth = 10
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  G.setLayer("K_AciliV60")
  G.setThickness(-firstDepth)
  circularMenderesA(marginX, marginY, structureSizeX, structureSizeY)
  
  G.setLayer("K_AciliV30")
  G.setThickness(-finalDepth)
  local p  = {}
  local D, B = 50, math.tan(math.pi/8)
  p[1] = {a+D,a}
  p[2] = {X-D-a,a,0,B}
  p[3] = {X-a,a+D}
  p[4] = {X-a,Y-D-a,0,B}
  p[5] = {X-D-a,Y-a}
  p[6] = {a+D,Y-a,0,B}
  p[7] = {a,Y-D-a}
  p[8] = {a,a+D,0,B}
  p[9] = {a+D,a}
  G.polylineimp(p)
  
  G.setLayer("K_Freze5mm")
  G.setThickness(-windowDepthFront)
  G.polylineimp(p)
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  D, a = 50, a-10
  p[1] = {a+D,a}
  p[2] = {X-D-a,a,0,B}
  p[3] = {X-a,a+D}
  p[4] = {X-a,Y-D-a,0,B}
  p[5] = {X-D-a,Y-a}
  p[6] = {a+D,Y-a,0,B}
  p[7] = {a,Y-D-a}
  p[8] = {a,a+D,0,B}
  p[9] = {a+D,a}
  G.polylineimp(p)
  
  return true
end

------------------------------------------------
function menderesA(marginX, marginY, structureSizeX, structureSizeY)
  
  -- do some math
  local howManyX = math.floor((X-2*marginX)/structureSizeX)
  local howManyY = math.floor((Y-2*marginY)/structureSizeY)
  uX = (X-2*marginX)/(3*howManyX)
  uY = (Y-2*marginY)/(3*howManyY)
  local startPoint = {marginX+4*uX, marginY+2*uY}
  
  -- loop over tiles & turns
  local refPoint = startPoint
  for angleOffset=0, 270, 90  -- 4 edges and corresponding turns 
  do
    if (angleOffset==0 or angleOffset==180) then
      for i=1, howManyX-2, 1 -- Tile N times the repeating unit
      do 
        refPoint = menderesAtile(refPoint, angleOffset) 
      end
      refPoint = menderesAcorner(refPoint, angleOffset)
    else
      for i=1, howManyY-2, 1 -- Tile N times the repeating unit
      do 
        refPoint = menderesAtile(refPoint, angleOffset) 
      end
      refPoint = menderesAcorner(refPoint, angleOffset)
    end
  end
end

------------------------------------------------
function circularMenderesA(marginX, marginY, structureSizeX, structureSizeY)
  
  -- do some math
  local howManyX = math.floor((X-2*marginX)/structureSizeX)
  local howManyY = math.floor((Y-2*marginY)/structureSizeY)
  uX = (X-2*marginX)/(3*howManyX)
  uY = (Y-2*marginY)/(3*howManyY)
  local startPoint = {marginX+4*uX, marginY+2*uY}
  
  -- this part is not yet parametric
  local adetX, adetY = 20, 10
  refPoint = {X-140, 54}
  for angleOffset=0, 90, 15 do refPoint = circularMenderesAtile(refPoint, angleOffset) end
  for angleOffset=0, adetX, 1 do  refPoint = circularMenderesAtile(refPoint, 90) end
  for angleOffset=90, 180, 15 do refPoint = circularMenderesAtile(refPoint, angleOffset) end
  for angleOffset=0, adetY, 1 do  refPoint = circularMenderesAtile(refPoint, 180) end
  for angleOffset=180, 270, 15 do refPoint = circularMenderesAtile(refPoint, angleOffset) end
  for angleOffset=0, adetX, 1 do  refPoint = circularMenderesAtile(refPoint, 270) end
  for angleOffset=270, 360, 15 do refPoint = circularMenderesAtile(refPoint, angleOffset) end
  for angleOffset=0, adetY, 1 do  refPoint = circularMenderesAtile(refPoint, 0) end
end

------------------------------------------------
function circularMenderesAtile(startPoint, angleOffset)
  local p1 = startPoint
  local p2 = G.polar(p1, angleOffset + 0,   1*uX)
  local p3 = G.polar(p2, angleOffset + 270, 1*uY)
  local p4 = G.polar(p3, angleOffset + 180, 1*uX)
  local p5 = G.polar(p4, angleOffset + 270, 1*uY)
  local p6 = G.polar(p5, angleOffset + 0,   2*uX)
  local p7 = G.polar(p6, angleOffset + 90,  2*uY)
  local p8 = G.polar(p7, angleOffset + 0,   1*uX)
  G.polyline(p1, p2, p3, p4, p5, p6, p7, p8)
  return p8
end

------------------------------------------------
function menderesAtile(startPoint, angleOffset)
  if (angleOffset==90 or angleOffset==270) then
    swap()
  end
  local p1 = startPoint
  local p2 = G.polar(p1, angleOffset + 0,   1*uX)
  local p3 = G.polar(p2, angleOffset + 270, 1*uY)
  local p4 = G.polar(p3, angleOffset + 180, 1*uX)
  local p5 = G.polar(p4, angleOffset + 270, 1*uY)
  local p6 = G.polar(p5, angleOffset + 0,   2*uX)
  local p7 = G.polar(p6, angleOffset + 90,  2*uY)
  local p8 = G.polar(p7, angleOffset + 0,   1*uX)
  G.polyline(p1, p2, p3, p4, p5, p6, p7, p8)
  if (angleOffset==90 or angleOffset==270) then
    swap()
  end
  return p8
end

------------------------------------------------
function menderesAcorner(startPoint, angleOffset)
  if (angleOffset==90 or angleOffset==270) then
    swap()
  end
  local p1 = startPoint
  local p2 = G.polar(p1, angleOffset + 0,   1*uX)
  local p3 = G.polar(p2, angleOffset + 270, 1*uY)
  local p4 = G.polar(p3, angleOffset + 180, 1*uX)
  local p5 = G.polar(p4, angleOffset + 270, 1*uY)
  local p6 = G.polar(p5, angleOffset + 0,   2*uX)
  local p7 = G.polar(p6, angleOffset + 90,  3*uY)
  local p8 = G.polar(p7, angleOffset + 180, 2*uX)
  local p9 = G.polar(p8, angleOffset + 90,  1*uY)
  G.polyline(p1, p2, p3, p4, p5, p6, p7, p8, p9)
  if (angleOffset==90 or angleOffset==270) then
    swap()
  end
  return p9
end

------------------------------------------------
function swap()
  local tmp = uX
  uX = uY
  uY = tmp
end

require "ADekoDebugMode"
