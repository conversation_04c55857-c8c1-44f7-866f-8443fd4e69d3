-- Test script for layer toggle functionality

-- Load ADekoLib
ADekoLib = require("ADekoLib")
require("turtle")

-- Start ADekoLib
ADekoLib.start()

-- Layer 1: Panel operations
ADekoLib.setLayer("PANEL")
ADekoLib.setFace("top")
line(50, 50, 150, 150)
crcl(100, 100, 25)
rect(200, 200, 100, 100)

-- Layer 2: Drilling operations
ADekoLib.setLayer("K_Freze10mm")
ADekoLib.setFace("top")
crcl(75, 75, 5)
crcl(125, 125, 5)
crcl(225, 225, 5)

-- Layer 3: Bottom face operations
ADekoLib.setLayer("H_Freze20mm_Ic_SF")
ADekoLib.setFace("bottom")
line(50, 50, 150, 150)
crcl(100, 100, 15)

-- Layer 4: Edge operations
ADekoLib.setLayer("K_AciliV45")
ADekoLib.setFace("top")
line(0, 0, 300, 0)
line(300, 0, 300, 300)
line(300, 300, 0, 300)
line(0, 300, 0, 0)

-- Layer 5: Text layer (non-machinable)
ADekoLib.setLayer("LUA_COMMENTS")
text(150, 150, "Test Layer Toggle")

print("Layer toggle test script completed!")
print("You should see 5 different layers in the layer panel:")
print("1. PANEL - Basic shapes")
print("2. K_Freze10mm - Drilling operations")
print("3. H_Freze20mm_Ic_SF - Bottom face operations")
print("4. K_AciliV45 - Edge operations")
print("5. LUA_COMMENTS - Text layer")
