-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script - Double Rustic Frame Test
-- Converted from C# azCAM macro: Double Rustic Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Double Rustic Frame parameters (from original C# macro)
  A = 50    -- Side margin
  B = 150   -- Vertical position for side lines
  C = 50    -- Curve depth/offset
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * A + 50   -- minimum required width
  local minHeight = 2 * B + 50  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Double Rustic Frame function (converted from C# macro)
  local function double_rustic_frame(A, B, C, Z)
    -- Use default values if parameters not provided
    A = A or 50
    B = B or 150
    C = C or 50
    Z = Z or -5
    
    print(string.format("Creating double rustic frame"))
    print(string.format("Parameters: A=%d, B=%d, C=%d, Z=%d", A, B, C, -Z))
    
    -- Calculate key points for the rustic frame
    local leftTop = {A, height - B}        -- Left line top
    local leftBottom = {A, B}              -- Left line bottom
    local rightBottom = {width - A, B}     -- Right line bottom
    local rightTop = {width - A, height - B}  -- Right line top
    
    -- Arc control points
    local bottomArcPeak = {width / 2, C}           -- Bottom arc peak (curves inward)
    local topArcPeak = {width / 2, height - C}     -- Top arc peak (curves inward)
    
    -- Validate curve points are within bounds
    if bottomArcPeak[2] < 0 or bottomArcPeak[2] > height then
      print(string.format("Warning: Bottom arc peak (%.1f) is outside panel height (0-%.1f)", bottomArcPeak[2], height))
    end
    
    if topArcPeak[2] < 0 or topArcPeak[2] > height then
      print(string.format("Warning: Top arc peak (%.1f) is outside panel height (0-%.1f)", topArcPeak[2], height))
    end
    
    -- Set layer and thickness for frame
    G.setLayer("Default")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the rustic frame using polyline with arc approximations
    local framePoints = {}
    
    -- Start with left vertical line
    table.insert(framePoints, leftTop)
    table.insert(framePoints, leftBottom)
    
    -- Bottom arc (leftBottom -> bottomArcPeak -> rightBottom)
    -- Create arc approximation using multiple points
    local bottomArcPoints = G.circularArc({(leftBottom[1] + rightBottom[1])/2, (leftBottom[2] + bottomArcPeak[2])/2}, 
                                          math.abs(bottomArcPeak[2] - leftBottom[2]) * 2, 8, 180, 360)
    
    for i, point in ipairs(bottomArcPoints) do
      -- Adjust points to create inward-curving arc
      local t = (i - 1) / (#bottomArcPoints - 1)  -- Parameter from 0 to 1
      local adjustedPoint = {
        leftBottom[1] + (rightBottom[1] - leftBottom[1]) * t,
        leftBottom[2] + (bottomArcPeak[2] - leftBottom[2]) * math.sin(math.pi * t)
      }
      table.insert(framePoints, adjustedPoint)
    end
    
    -- Right vertical line
    table.insert(framePoints, rightBottom)
    table.insert(framePoints, rightTop)
    
    -- Top arc (rightTop -> topArcPeak -> leftTop)
    -- Create arc approximation using multiple points
    local topArcPoints = G.circularArc({(rightTop[1] + leftTop[1])/2, (rightTop[2] + topArcPeak[2])/2}, 
                                       math.abs(topArcPeak[2] - rightTop[2]) * 2, 8, 0, 180)
    
    for i, point in ipairs(topArcPoints) do
      -- Adjust points to create inward-curving arc
      local t = (i - 1) / (#topArcPoints - 1)  -- Parameter from 0 to 1
      local adjustedPoint = {
        rightTop[1] + (leftTop[1] - rightTop[1]) * t,
        rightTop[2] + (topArcPeak[2] - rightTop[2]) * math.sin(math.pi * t)
      }
      table.insert(framePoints, adjustedPoint)
    end
    
    -- Close the frame
    table.insert(framePoints, leftTop)
    
    -- Create the frame using polyline
    G.polyline(table.unpack(framePoints))
    
    print(string.format("Rustic frame key points:"))
    print(string.format("  Left line: (%.1f,%.1f) to (%.1f,%.1f)", 
          leftTop[1], leftTop[2], leftBottom[1], leftBottom[2]))
    print(string.format("  Bottom arc: (%.1f,%.1f) -> (%.1f,%.1f) -> (%.1f,%.1f)", 
          leftBottom[1], leftBottom[2], bottomArcPeak[1], bottomArcPeak[2], rightBottom[1], rightBottom[2]))
    print(string.format("  Right line: (%.1f,%.1f) to (%.1f,%.1f)", 
          rightBottom[1], rightBottom[2], rightTop[1], rightTop[2]))
    print(string.format("  Top arc: (%.1f,%.1f) -> (%.1f,%.1f) -> (%.1f,%.1f)", 
          rightTop[1], rightTop[2], topArcPeak[1], topArcPeak[2], leftTop[1], leftTop[2]))
    
    return true
  end
  
  -- Call the double rustic frame function
  local success = double_rustic_frame(A, B, C, -Z)
  
  if success then
    print(string.format("Double rustic frame created with parameters:"))
    print(string.format("  A (side margin): %d mm", A))
    print(string.format("  B (vertical position): %d mm", B))
    print(string.format("  C (curve depth): %d mm", C))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a rustic-style frame with curved edges")
    print("  - Top and bottom edges curve inward for organic appearance")
    print("  - Left and right edges are straight vertical lines")
    print("  - Perfect for rustic, handcrafted, or natural wood designs")
  end
  
  return true
end

require "ADekoDebugMode"
