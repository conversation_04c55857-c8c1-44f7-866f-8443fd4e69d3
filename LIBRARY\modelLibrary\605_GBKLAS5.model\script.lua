-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 50                    -- Kenardan Vbite
  aa = 20					-- Dar kapak için Kenardan mesafe
  h = 25                    -- Vbitten göbeğe
  ad = 6                    -- Vbit derinliği (sunkenDepth)
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 6                   -- İnce bıçak çapı (Köşe Temizleme)
  cD = 15					-- Radus Veren form bıcak düz geniç çapı
  cR = 10		  			-- Radus Veren form bıcak düz uç dar çapı
  R = 3		        		-- Kose Radus
    
	extEdgeRtoolExist			= 2		-- Dış Radus Bicak Kenar Pah islemi Var mi? derinlik/0:yok
	edgeCornerRExist          	= 0   	-- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
	topGrooveExist				= 2	  	-- Vbit Ustu kanal var mı? derinlik/0:yok
  	
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local aV = 90                  -- Vbit Açısı
  --bd = 3                    -- dış kanal derinliği
  --local hd = 5              -- Göbek Desen Bıcak derinliği
  --local dd = 2              -- iç kanal derinliği
    
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end

	local shapeToolExist 		= 5   	-- Göbek Desen bıçağı var mı? derinlik/0:yok
	
  local D = edgeCornerRExist
	
	
	local vWideDiameter = 60
	--local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)     --indiği derinlikte yarısı oluyor
	local sunkenWidth = cD-cR
		
	local B = math.tan(math.pi/8)
	
	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist >0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	minXCalculated = 2*a + 2*sunkenWidth + 2*h
	minYCalculated = 2*ust + 2*sunkenWidth + 2*h
	
	if cW <= 0 then
		cW = cT
	end
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
	
	function BulgePoly(distance1, distance2, R)
		local points = {
			{distance1+R, distance2,0,0},			--{D,0},
			{X-distance1-R, distance2,0,B},		--{X-D,0,0,B},
			{X-distance1, distance2+R,0,0},			--{X,D},
			{X-distance1, Y-distance2-R,0,B},	--{X,Y-D,0,B},
			{X-distance1-R, Y-distance2,0,0},		--{X-D,Y},
			{distance1+R, Y-distance2,0,B},		--{D,Y,0,B},
			{distance1, Y-distance2-R,0,0},			--{0,Y-D},
			{distance1, distance2+R,0,B},		--{0,D,0,B},
			{distance1+R, distance2,0,0}			--{D,0}
			}	
			return points
	end
	
	if topGrooveExist > 0 then
		G.setLayer("H_Freze"..cT.."mm_Ic")  -- Vbit Ustu Duz kanal
		G.setThickness(-topGrooveExist)
		distance1 = a
		distance2 = ust
		points1 = BulgePoly(distance1,distance2,R)
		G.polylineimp(points1)
	end
	
	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
	
	local cCornersExist = false
	
	G.setLayer("K_Raduslu_" .. cD .. "mm")  -- 
	G.setThickness(-ad)
	distance1 = a+sunkenWidth+cR/2
	distance2 = ust+sunkenWidth+cR/2
	R1 = R-sunkenWidth
  if R1 > 0 then
		points2 = BulgePoly(distance1,distance2,R1)
		G.polylineimp(points2)
	else
		point12 = {distance1, distance2,0}
		point13 = {X-distance1, Y-distance2,0}
		G.rectangle(point12,point13)
		cCornersExist = true
	end
	
	--G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	--G.setThickness(0)
	--local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	--corner1[3] = 0
	--corner2[3] = 0
	
	if shapeToolExist > 0 then  				          ----Göbekte desen bıçağı varsa
		G.setLayer("K_Desen")
		G.setThickness(-shapeToolExist)
	else
		G.setLayer("K_AciliV"..aV)
		G.setThickness(-ad)
	end
	
	distance = a + sunkenWidth + h
	distance2 = ust + sunkenWidth + h
	point1 = {distance, distance2}
	point2 = {X-distance, Y-distance2}
	G.rectangle(point1,point2)
	
	
	
	if h<cT or h<cW then
		print("Tool too large")
		return true
	end
	
  if h==cW then
    G.setLayer("K_Freze"..cW.."mm") 
    G.setThickness(-ad)
    R2= R-sunkenWidth-cW/2
    if R2 > cW/2 then
      distance1 = a + sunkenWidth+cW/2
      distance2 = ust + sunkenWidth+cW/2
      points2 = BulgePoly(distance1,distance2,R2)
      G.polylineimp(points2)
    else
      distance1 = a+sunkenWidth+cW/2
      distance2 = ust+sunkenWidth+cW/2
      point1 = {distance1, distance2}
      point2 = {X-distance1, Y-distance2}
      G.rectangle(point1,point2)
    end
  else  
    G.setLayer("Cep_Acma")
    G.setThickness(-ad)
    if R1 > cW/2 then
      distance1 = a + sunkenWidth
      distance2 = ust + sunkenWidth
      points2 = BulgePoly(distance1,distance2,R1)
      G.polylineimp(points2)
    else 
      distance1 = a+sunkenWidth
      distance2 = ust+sunkenWidth
      point1 = {distance1, distance2}
      point2 = {X-distance1, Y-distance2}
      G.rectangle(point1,point2)    
    end
      distance = a+sunkenWidth+h
      distance2 = ust+sunkenWidth+h
      point10 = {distance, distance2}
      point11 = {X-distance, Y-distance2}
      G.rectangle(point10,point11)
  end
 
 

	G.setLayer("K_TarKoseTemizl"..cT.."mm")
	if cCornersExist then
		if cW ~= cT and cR> cT then
			if h >= 3*cT then
				G.setThickness(0)
				distance1 = a + sunkenWidth
				distance2 = ust + sunkenWidth
				point1 = {distance1, distance2}
				point2 = {X-distance1, Y-distance2}
				G.cleanCorners(point1,point2,ad,cT)
			end
		end
	else
		R2 = R1 - cT/2
		distance1 = a + sunkenWidth + cT/2
		distance2 = ust + sunkenWidth + cT/2
		points3 = BulgePoly(distance1,distance2,R2)
		G.polylineimp(points3)
	end
	
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
