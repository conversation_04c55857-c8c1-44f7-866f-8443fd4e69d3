-- Test script to verify print and printError functions work correctly

function modelMain()
    print("This is a regular print message")
    print("Testing multiple arguments:", 123, true, nil)
    
    printError("This is an error message")
    printError("Error with multiple args:", 456, false)
    
    printErrorUrgent("This is an urgent error message")
    printErrorUrgent("Urgent error with data:", 789)
    
    print("Script execution completed successfully")
end

-- Call the main function
modelMain()
