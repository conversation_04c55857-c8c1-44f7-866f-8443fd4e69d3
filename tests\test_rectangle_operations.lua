-- Test script for rectangle operations
-- Tests various rectangle creation methods and parameters

-- Set up basic variables - using larger door for better 3D visibility
X = 500
Y = 400
materialThickness = 18

function modelMain()
    print("=== RECTANGLE OPERATIONS TEST ===")
    
    -- Initialize ADekoLib
    G = ADekoLib
    
    -- Create door panel (PANEL layer)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    print("✓ Door panel created (500x400mm)")
    
    -- TOP SURFACE OPERATIONS
    G.setFace("top")
    
    -- Test 1: Large visible rectangle (sharp corners)
    print("Creating large visible rectangle...")
    G.setLayer("20MM")  -- 20mm cylindrical tool for better visibility
    G.setThickness(-10)  -- Deeper cut for better visibility
    G.rectangle({100, 100}, {400, 300})
    print("✓ Large rectangle: (100,100) to (400,300), depth -10mm")

    -- Test 2: Medium rectangle with rounded corners (bulge)
    print("Creating rounded rectangle...")
    <PERSON><PERSON>setLayer("15MM")   -- 15mm cylindrical tool
    G.setThickness(-8)
    G.rectangle({50, 50}, {200, 150}, 15)  -- bulge = 15 for rounded corners
    print("✓ Rounded rectangle: (50,50) to (200,150), bulge=15, depth -8mm")

    -- Test 3: Center pocket operation
    print("Creating center pocket rectangle...")
    G.setLayer("25MM")  -- 25mm cylindrical tool
    G.setThickness(-12)
    G.rectangle({150, 150}, {350, 250})
    print("✓ Center pocket: (150,150) to (350,250), depth -12mm")
    
    -- Test 4: Corner rectangle
    print("Creating corner rectangle...")
    G.setLayer("12MM")   -- 12mm cylindrical tool
    G.setThickness(-6)
    G.rectangle({300, 300}, {450, 380})
    print("✓ Corner rectangle: (300,300) to (450,380), depth -6mm")
    
    -- BOTTOM SURFACE OPERATIONS
    print("Creating bottom surface rectangles...")
    G.setFace("bottom")

    -- Test 5: Bottom face rectangle
    G.setLayer("18MM_SF")  -- SF suffix for bottom face
    G.setThickness(-8)
    G.rectangle({100, 100}, {400, 200})
    print("✓ Bottom face rectangle: (100,100) to (400,200), depth -8mm")
    
    print("=== RECTANGLE TEST COMPLETED ===")
    print("Created large, visible rectangles with:")
    print("  - Large door panel (500x400mm)")
    print("  - Deep cuts (-6 to -12mm) for better 3D visibility")
    print("  - Large tool sizes (12MM to 25MM)")
    print("  - Both top and bottom faces")
    print("Switch to 3D visualization to see the cut rectangles in the door!")
    print("Use camera controls to rotate and inspect the cuts")
    
    return true
end

require "ADekoDebugMode"
