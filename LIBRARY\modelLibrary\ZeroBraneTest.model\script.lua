-- ZeroBrane-Style Debug Test Script
-- This script demonstrates the ZeroBrane-style debugging functionality
-- System variables (binding box dimensions): "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  
  -- Set up the working face
  G.setFace("top")
  G.setThickness(-materialThickness)
  
  -- Create a simple rectangular part outline
  G.makePartShape()
  
  -- Add some basic geometry to demonstrate the debugging
  -- Create a simple rectangle in the center
  local centerX = X / 2
  local centerY = Y / 2
  local rectWidth = 100
  local rectHeight = 80
  
  G.setThickness(-5) -- 5mm deep cut
  G.rectangle(
    {centerX - rectWidth/2, centerY - rectHeight/2}, 
    {centerX + rectWidth/2, centerY + rectHeight/2}
  )
  
  -- Add a circle in the center
  G.setThickness(-3) -- 3mm deep cut
  G.circle({centerX, centerY}, 20)
  
  -- Add some corner holes
  local margin = 50
  local holeRadius = 5
  G.setThickness(-materialThickness) -- Through holes
  
  -- Corner holes
  G.circle({margin, margin}, holeRadius)
  G.circle({X - margin, margin}, holeRadius)
  G.circle({X - margin, Y - margin}, holeRadius)
  G.circle({margin, Y - margin}, holeRadius)
  
  -- Add a groove along one edge
  G.setThickness(-2) -- 2mm deep groove
  G.groove({20, Y/2}, {X-20, Y/2}, 2)
  
  -- Switch to front face and add edge details
  G.setFace("front")
  G.setThickness(-1) -- 1mm deep edge detail
  G.rectangle({10, 5}, {X-10, 10})
  
  -- Switch to right face
  G.setFace("right")
  G.setThickness(-1)
  G.circle({Y/2, materialThickness/2}, 8)
  
  -- Return to top face for final operations
  G.setFace("top")
  
  -- Add some text or labels (if supported)
  if G.labelPoint then
    G.labelPoint({centerX, centerY + 40}, "Center")
    G.labelPoint({margin, margin}, "Corner 1")
  end
  
  -- List the operations
  G.list()
  
  print("✅ ZeroBrane-style debug test completed successfully!")
  print("📐 Part dimensions: " .. X .. " x " .. Y .. " x " .. materialThickness)
  print("🔧 Created geometry on multiple faces (top, front, right)")
  
  return true
end

-- This line would normally load ADekoDebugMode, but in our app it's handled automatically
-- require "ADekoDebugMode"
