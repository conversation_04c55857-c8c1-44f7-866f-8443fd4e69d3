-- <PERSON>ek<PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  a = 30
  b = 30
  scale = 70
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local vNarrowAngle = 60
  local vNarrowDiameter = 30
  local sunkenDepth = 8
  local cThinDiameter = 5
  local windowDepthFront = 14
  local windowDepthBack = 6
  
  G.setLayer("K_AciliV60")
  G.setThickness(-3)
  local point1, point2 = cell(a,b,scale)
  
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame(point1,point2,sunkenDepth,vNarrowAngle,vNarrowDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G<PERSON>setLayer("K_Freze5mm")
  G.setThickness(0)
  <PERSON><PERSON>cleanCorners(corner1,corner2,sunkenDepth,cThinDiameter)
  
  G.setLayer("H_Freze10mm_Ic")
  G.setThickness(-windowDepthFront)
  G.rectangle(corner1,corner2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle(corner1,corner2)
  
  return true
end

function cell(offsetX,offsetY, scale)
    local howManyX = math.floor((X-2*offsetX) / scale) 
    local howManyY = math.floor((Y-2*offsetY) / scale)
    local originalScaleX = (X-2*offsetX) / howManyX
    local originalScaleY = (Y-2*offsetY) / howManyY
    
    for i=0, 2*(howManyX)-1 do
      if i < howManyX then
        newOriginX = offsetX + i*originalScaleX
        newOriginY = offsetY
      else
        newOriginX = offsetX + (i-howManyX)*originalScaleX
        newOriginY = (Y-offsetY-originalScaleY)
      end
      
      pts = {
        {0.207107 , 0},
        {0.09467 , 0.271447},
        {0.375 , 0.551777},
        {0.375 , 0.948223},
        {0.5 , 1},
        {0.792893 , 0},
        {0.90533 , 0.271447},
        {0.625 , 0.551777},
        {0.625 , 0.948223},
        {0.5 , 1},
        {0.5 , 0},
        {0.375 , 0.051777},
        {0.375 , 0.448223},
        {0.09467 , 0.728553},
        {0.207107 , 1},
        {0.5 , 0},
        {0.625 , 0.051777},
        {0.625 , 0.448223},
        {0.90533 , 0.728553},
        {0.792893 , 1},
        {0 , 0.792893},
        {0.271447 , 0.90533},
        {0.551777 , 0.625},
        {0.948223 , 0.625},
        {1 , 0.5},
        {0 , 0.207107},
        {0.271447 , 0.09467},
        {0.551777 , 0.375},
        {0.948223 , 0.375},
        {1 , 0.5},
        {-0 , 0.5},
        {0.051777 , 0.625},
        {0.448223 , 0.625},
        {0.728553 , 0.90533},
        {1 , 0.792893},
        {-0 , 0.5},
        {0.051777 , 0.375},
        {0.448223 , 0.375},
        {0.728553 , 0.09467},
        {1 , 0.207107}
      }
      for i = 1 , 40 do
        pts[i][1] = newOriginX + pts[i][1] * originalScaleX
        pts[i][2] = newOriginY + pts[i][2] * originalScaleY
      end
      
      G.polyline(pts[1], pts[2], pts[3], pts[4], pts[5])
      G.polyline(pts[6], pts[7], pts[8], pts[9], pts[10])
      G.polyline(pts[11], pts[12], pts[13], pts[14], pts[15])
      G.polyline(pts[16], pts[17], pts[18], pts[19], pts[20])
      G.polyline(pts[21], pts[22], pts[23], pts[24], pts[25])
      G.polyline(pts[26], pts[27], pts[28], pts[29], pts[30])
      G.polyline(pts[31], pts[32], pts[33], pts[34], pts[35])
      G.polyline(pts[36], pts[37], pts[38], pts[39], pts[40])
    end
    
    for i=0, 2*(howManyY-2) do
      if i < howManyY-2 then
        newOriginX = offsetX
        newOriginY = offsetY + (i+1)*originalScaleY
      else
        newOriginX = (X-offsetX-originalScaleX)
        newOriginY = offsetY + (i-(howManyY-2))*originalScaleY
      end
      
      pts = {
        {0.207107 , 0},
        {0.09467 , 0.271447},
        {0.375 , 0.551777},
        {0.375 , 0.948223},
        {0.5 , 1},
        {0.792893 , 0},
        {0.90533 , 0.271447},
        {0.625 , 0.551777},
        {0.625 , 0.948223},
        {0.5 , 1},
        {0.5 , 0},
        {0.375 , 0.051777},
        {0.375 , 0.448223},
        {0.09467 , 0.728553},
        {0.207107 , 1},
        {0.5 , 0},
        {0.625 , 0.051777},
        {0.625 , 0.448223},
        {0.90533 , 0.728553},
        {0.792893 , 1},
        {0 , 0.792893},
        {0.271447 , 0.90533},
        {0.551777 , 0.625},
        {0.948223 , 0.625},
        {1 , 0.5},
        {0 , 0.207107},
        {0.271447 , 0.09467},
        {0.551777 , 0.375},
        {0.948223 , 0.375},
        {1 , 0.5},
        {-0 , 0.5},
        {0.051777 , 0.625},
        {0.448223 , 0.625},
        {0.728553 , 0.90533},
        {1 , 0.792893},
        {-0 , 0.5},
        {0.051777 , 0.375},
        {0.448223 , 0.375},
        {0.728553 , 0.09467},
        {1 , 0.207107}
      }
      for i = 1 , 40 do
        pts[i][1] = newOriginX + pts[i][1] * originalScaleX
        pts[i][2] = newOriginY + pts[i][2] * originalScaleY
      end
      
      G.polyline(pts[1], pts[2], pts[3], pts[4], pts[5])
      G.polyline(pts[6], pts[7], pts[8], pts[9], pts[10])
      G.polyline(pts[11], pts[12], pts[13], pts[14], pts[15])
      G.polyline(pts[16], pts[17], pts[18], pts[19], pts[20])
      G.polyline(pts[21], pts[22], pts[23], pts[24], pts[25])
      G.polyline(pts[26], pts[27], pts[28], pts[29], pts[30])
      G.polyline(pts[31], pts[32], pts[33], pts[34], pts[35])
      G.polyline(pts[36], pts[37], pts[38], pts[39], pts[40])
    end
    
    local innerPoint1 = {offsetX+originalScaleX+10,offsetY+originalScaleY+10}
    local innerPoint2 = {X-(offsetX+originalScaleX+10),Y-(offsetY+originalScaleY+10)}
    G.rectangle({offsetX,offsetY},{X-offsetX,Y-offsetY})
    G.rectangle({offsetX+originalScaleX,offsetY+originalScaleY},{X-(offsetX+originalScaleX),Y-(offsetY+originalScaleY)})
    return innerPoint1, innerPoint2
end

------------------------------------------------

require "ADekoDebugMode"
