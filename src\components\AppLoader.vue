<template>
  <Transition name="loader-fade">
    <div v-if="visible" class="app-loader" :class="{ 'overlay': isOverlay }">
      <div class="loader-content">
        <!-- Logo/Icon -->
        <div class="loader-icon" :class="iconClass">
          <slot name="icon">
            <div class="default-icon">{{ iconText }}</div>
          </slot>
        </div>

        <!-- Title and Subtitle -->
        <div class="loader-text">
          <h1 class="loader-title">{{ title }}</h1>
          <p v-if="subtitle" class="loader-subtitle">{{ subtitle }}</p>
        </div>

        <!-- Progress Bar -->
        <div v-if="showProgress" class="loader-progress">
          <div 
            class="loader-progress-bar" 
            :style="{ width: `${progress}%` }"
            :class="{ 'error': hasError }"
          ></div>
        </div>

        <!-- Status Message -->
        <div class="loader-status" :class="{ 'error': hasError }">
          {{ statusMessage }}
          <span v-if="!hasError && !statusMessage.includes('Ready!')" class="loader-dots"></span>
        </div>

        <!-- Additional Content -->
        <div v-if="$slots.content" class="loader-additional">
          <slot name="content"></slot>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
interface Props {
  visible?: boolean
  title?: string
  subtitle?: string
  statusMessage?: string
  progress?: number
  showProgress?: boolean
  hasError?: boolean
  isOverlay?: boolean
  iconText?: string
  iconClass?: string
}

withDefaults(defineProps<Props>(), {
  visible: true,
  title: 'Loading',
  subtitle: '',
  statusMessage: 'Please wait...',
  progress: 0,
  showProgress: true,
  hasError: false,
  isOverlay: false,
  iconText: 'L',
  iconClass: ''
})

defineEmits<{
  hidden: []
}>()
</script>

<style scoped>
.app-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-loader.overlay {
  background: rgba(30, 30, 30, 0.95);
  backdrop-filter: blur(5px);
}

.loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.loader-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 30px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: iconFloat 2s ease-in-out infinite alternate;
}

.default-icon {
  background: linear-gradient(45deg, #007acc, #00a8ff);
  width: 100%;
  height: 100%;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 122, 204, 0.3);
}

@keyframes iconFloat {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-10px); }
}

.loader-text {
  margin-bottom: 40px;
}

.loader-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #007acc, #00a8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: #007acc; /* Fallback for browsers that don't support background-clip */
}

.loader-subtitle {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0;
}

.loader-progress {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 20px;
}

.loader-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #007acc, #00a8ff);
  border-radius: 2px;
  transition: width 0.3s ease;
  animation: progressGlow 2s ease-in-out infinite alternate;
}

.loader-progress-bar.error {
  background: linear-gradient(90deg, #ff4757, #ff6b7a);
}

@keyframes progressGlow {
  0% { box-shadow: 0 0 5px rgba(0, 122, 204, 0.5); }
  100% { box-shadow: 0 0 20px rgba(0, 122, 204, 0.8); }
}

.loader-status {
  font-size: 14px;
  color: #888;
  min-height: 20px;
}

.loader-status.error {
  color: #ff6b7a;
}

.loader-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

.loader-additional {
  margin-top: 20px;
}

/* Transition animations */
.loader-fade-enter-active,
.loader-fade-leave-active {
  transition: opacity 0.5s ease;
}

.loader-fade-enter-from,
.loader-fade-leave-to {
  opacity: 0;
}
</style>
