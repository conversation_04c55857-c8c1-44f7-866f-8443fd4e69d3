-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib	
	minimum = 150
	limit = 300
	
  
  a = 60  
  aa = 30
  ad = 6                -- Vbit derinliği (sunkenDepth)
  aV = 120              -- Vbit Açısı
  yy = 25				-- yay kısmının yüksekliği
  h = 25
  m = 40
  cW = 20              -- Tarama Bıçak Çapı
  cT = 6                -- İnce bıçak çapı (Köşe Temizleme)
  b = 15
  
  topGrooveExist   				= 2   -- Vbit Ustu kanal var mı? derinlik/0:yok
  shapeToolExist 				= 5   -- Göbek Desen bıçağı var mı? derinlik/0:yok
  edgeCornerRExist          	= 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  extEdgeVtoolExist      	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON>h işlemi var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  local gaps = 30
  
  local intVerticalGrooveExist 	= 0   -- iç kanal var mı? dik çizgiler  derinlik/ 0:yok
  
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
  local sunkenWidth2 = sunkenWidth              --göbekte desen bıçağı yoksa eşittir. Varsa shapetoolexist den bak

  local Dd = edgeCornerRExist
  local Bb = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape(        ----chamfered part shape
      {Dd,0},		
      {X-Dd,0,0,Bb},
      {X,Dd},
      {X,Y-Dd,0,Bb},
      {X-Dd,Y},
      {Dd,Y,0,Bb},
      {0,Y-Dd},
      {0,Dd,0,Bb},
      {Dd,0}
      )
  else
    G.makePartShape()
  end

	if X < xMin or Y < yMin then
	print("Part dimension too small")
	return true
	end
	
	if cW <= 0 then
		cW = cT
	end
  
	if h<cW then
		print("Tool too large")
		return true
	end
  
  --G.setLayer("H_Freze10mm_Dis")
  --G.setThickness(-notchDepth)
  --G.rectangle ({notchWidth,notchWidth},{X-notchWidth,Y-notchWidth})
	
	if aa == 0 then
		aa = a
	end
	
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
	yh = ust+yy
	
	local kenardan_mesafe1 = a + sunkenWidth + h
	local kenardan_mesafe2 = ust + sunkenWidth + h				--
	local kenardan_mesafe3 = yh + sunkenWidth + h/2				--içteki kanalların  üst yay başlangıç noktası mesafesi
	
	local gobekGenislik = X-2*a-4*sunkenWidth-2*h
	local stepX = math.floor(gobekGenislik/gaps)
	  
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	--local bulge1 = G.bulge(
	--	{X-a-m, Y-yh, 0, 0},
	--	{X/2, Y-ust}, {a+m, Y-yh}
	--	)
	--local points = {
 --     {X-a-m, Y-yh, 0, bulge1},  --1                2---1-7  
 --   	{a+m, Y-yh, 0, 0},       --2                3-------6
 --   	{a, Y-yh, 0, 0},       --3                  ---------  
 --   	{a, ust, 0, 0},         --4                 ---------
 --   	{X-a, ust, 0},             --5              ---------  
 --   	{X-a, Y-yh, 0, 0},      --6                 4-------5
 --     {X-a-m, Y-yh, 0, bulge1} --7
 --   }
  m = m + sunkenWidth
	function OffsetHesap(h)
		local kiris = (X-2*a-2*m)/2
		local orta = a + m + kiris
		local yaricap = ((kiris*kiris)+(yy*yy))/(2*yy);
		local R = yaricap;
		local radian1 = math.asin((R-yy)/R);
		local angle1 = radian1 * (180/math.pi);
		local angle2 = (90-angle1)/2;
		local radian2 = angle2 * (math.pi/180);
		local bb = (math.tan(radian2)) * h;
		--// var ofkiris = (yaricap/(yaricap+H))*kiris; //ofsetlenen kiris boyu yarisi
		local m1 = m-h+bb;
		return m1
	end
    
    
	function BulgePoly(distance1, distance2, ust, m, offset)        --- distance1 soldan mesafe, distance2 6numaranın yukarıdan mesafesi, ust-ust den yaya mesafe, m- 2 ile 3 numara arası mesafe
		local bulge = G.bulge(
		{X-distance1-m-offset, Y-distance2-offset, 0, 0},
		{X/2, Y-ust-offset}, {distance1+m-offset, Y-distance2-offset}
		)
		local points = {
		{X-distance1-m-offset, Y-distance2-offset, 0, bulge},  --1                	 	 2---1-7  
			{distance1+m+offset, Y-distance2-offset, 0, 0},       --2               	3-------6
			{distance1+offset, Y-distance2-offset, 0, 0},       --3                  	---------  
			{distance1+offset, ust+offset, 0, 0},         --4                 			---------
			{X-distance1-offset, ust+offset, 0},             --5              			---------  
			{X-distance1-offset, Y-distance2-offset, 0, 0},      --6                	4-------5
			{X-distance1-m-offset, Y-distance2-offset, 0, bulge} --7
			}  
			return points
	end
  
	G.setLayer("VOyuk_AciliV"..aV)					                ----Dış Sunken
	G.setThickness(-ad)
	G.polylineimp(BulgePoly(a-sunkenWidth, yh-sunkenWidth, ust-sunkenWidth, OffsetHesap(sunkenWidth), sunkenWidth))
	local offset = sunkenWidth+h

	if extEdgeVtoolExist > 0 then                   --kapak kenarı pah var mı?
		-- G.setLayer("K_AciliV"..aV)					                ----Dış Sunken
		G.setLayer("K_AciliV_Pah")		
		G.setThickness(-extEdgeVtoolExist)    
		G.rectangle({0,0},{X,Y})
	end
	
	if shapeToolExist > 0 then  				          ----Göbekte desen bıçağı varsa
		G.setLayer("K_Desen")
		G.setThickness(-shapeToolExist)
	else
		G.setLayer("K_AciliV"..aV)
		G.setThickness(-ad)
	end
		G.polylineimp(BulgePoly(a-sunkenWidth, yh-sunkenWidth, ust-sunkenWidth, OffsetHesap(sunkenWidth+offset), sunkenWidth + offset))
		sunkenWidth2 = 10       --ustunde kanal işlemi varsa taramadan kaydırılacak mesafe
	

	if h > cW then
		G.setLayer("Cep_Acma")             		--göbek kanal dibi düz TARAMA
		G.setThickness(-ad)
		local offset1 = sunkenWidth
		local offset2 = sunkenWidth+h
		G.polylineimp(BulgePoly(a-sunkenWidth, yh-sunkenWidth, ust-sunkenWidth, OffsetHesap(sunkenWidth+offset), sunkenWidth + offset))
		G.polylineimp(BulgePoly(a-sunkenWidth, yh-sunkenWidth, ust-sunkenWidth, OffsetHesap(2*sunkenWidth), 2*sunkenWidth))
	elseif h== cW then
		G.setLayer("K_Freze"..cW.."mm")          			--göbek kanal diBi düz kanal   
		G.setThickness(-ad)
		local offset3 = sunkenWidth + cW/2
		G.polylineimp(BulgePoly(a-sunkenWidth, yh-sunkenWidth, ust-sunkenWidth, OffsetHesap(sunkenWidth+offset3), sunkenWidth+offset3))
	end
	
	if cW ~= cT then
		G.setLayer("K_Freze"..cT.."mm")          			--göbek kanal diBi düz kanal   
		G.setThickness(-ad)
		local offset3 = sunkenWidth + cT/2
		G.polylineimp(BulgePoly(a-sunkenWidth, yh-sunkenWidth, ust-sunkenWidth, OffsetHesap(sunkenWidth+offset3), sunkenWidth+offset3))
	end
	  
	--burası ortadaki çizgiler
	--çizgiler
	if intVerticalGrooveExist > 0 then            --içerde dik kanallar var mı?
	
		local point1 = {X-kenardan_mesafe1-m/2, Y-kenardan_mesafe3, 0, 0}		--
		local bulge = G.bulge(point1, {X/2, Y-kenardan_mesafe2}, {kenardan_mesafe1+m/2, Y-kenardan_mesafe3+h/2})
		if (bulge>1) then
			print("Bulge too large")
			return true
		end
		point1[4] = bulge
		
		local point2 = {kenardan_mesafe1+m/2, Y-kenardan_mesafe3, 0, 0}
		local aprxGaps = gobekGenislik/(stepX)
		local e = a+sunkenWidth*2+aprxGaps
		local radius = G.radius(point1, point2, bulge)
		
		for i=1, (stepX-1), 1		-- loop for the vertical lines --i=0, stepX di
		do
			local Lp1 = {kenardan_mesafe1 + sunkenWidth + i*aprxGaps, kenardan_mesafe2}		-- imaginary lines to intersect the above arc
			local Lp2 = {kenardan_mesafe1 + sunkenWidth + i*aprxGaps, Y}
			comment, pc1, pc2 = G.circleCircleIntersection(point1, radius, point2, radius)		--find center of the arc
			if (comment=='tangent' or comment=='intersection') then
				comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		--find line-arc intersection point
				if (comment2=='tangent' or comment2=='secant') then
				if (i==0 or i==stepX) then 
				--G.setLayer("K_Freze10mm")      -- GELMEYECEK AMA KALSIN
				else
				G.setLayer("K_Ballnose")
				end
				G.setThickness(-intVerticalGrooveExist)
					G.line(intersection1, Lp1)
				end
			else
				G.error()
			return false
			end
		end
	end

--  --çizgiler bitti
  
	 if topGrooveExist > 0 then              --dışarda kanal işlemi var mı?
		-- extGrooveOffset = b + cT/2
		G.setLayer("K_Freze"..cT.."mm")
		G.setThickness(-topGrooveExist)
		G.polylineimp(BulgePoly(a-sunkenWidth, yh-sunkenWidth, ust-sunkenWidth, OffsetHesap(sunkenWidth-b), sunkenWidth-b))
	 end
  
  return true
end


----------------------------------------------
require "ADekoDebugMode"