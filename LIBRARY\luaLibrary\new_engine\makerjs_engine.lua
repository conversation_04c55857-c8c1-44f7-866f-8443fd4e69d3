--[[
Lua-based Parametric Modeling Engine inspired by Maker.js
Copyright (C) 2025

A parametric modeling engine designed to replace Turtle.lua macros.
Generates structured 2D model data (paths/shapes) suitable for export to 
Maker.js-compatible JSON, with future support for 3D sweep operations.

Usage:
  local engine = require('makerjs_engine')
  
  engine.model_def("my_model", function()
    engine.layer("cut")
    engine.rect("frame", 0, 0, 100, 200)
    engine.circle("hole", 50, 100, 10)
  end)
  
  local json = engine.export_model()
  print(json)
--]]

-- Module table
local M = {}

-- Internal state
local current_model = nil
local current_layer = "default"
local models = {}

-- Error checking flag
local strict_mode = true

-- Utility functions
local function round(num, decimals)
    local mult = 10^(decimals or 0)
    return math.floor(num * mult + 0.5) / mult
end

local function validate_name(name, context)
    if not name or type(name) ~= "string" or name == "" then
        error(string.format("Invalid name in %s: name must be a non-empty string", context))
    end
end

local function validate_number(value, name, context)
    if not value or type(value) ~= "number" then
        error(string.format("Invalid %s in %s: must be a number", name, context))
    end
end

local function validate_points(points, context)
    if not points or type(points) ~= "table" or #points < 2 then
        error(string.format("Invalid points in %s: must be a table with at least 2 points", context))
    end
    for i, point in ipairs(points) do
        if type(point) ~= "table" or #point ~= 2 then
            error(string.format("Invalid point %d in %s: must be a table with 2 coordinates", i, context))
        end
        validate_number(point[1], "x coordinate", context)
        validate_number(point[2], "y coordinate", context)
    end
end

-- Internal data structure management
local function ensure_model()
    if not current_model then
        -- Create a minimal model structure without going through model_def
        print("Warning: No active model found. Creating temporary model structure.")
        current_model = {
            name = "temp_model",
            layers = {},
            metadata = {
                created = os.date("%Y-%m-%d %H:%M:%S"),
                engine = "lua_makerjs_engine",
                version = "1.0.0",
                temporary = true
            }
        }
        models["temp_model"] = current_model
    end
end

local function ensure_layer()
    ensure_model()
    if not current_model.layers[current_layer] then
        current_model.layers[current_layer] = {
            paths = {}
        }
    end
end

local function check_duplicate_name(name)
    if strict_mode then
        ensure_layer()
        if current_model.layers[current_layer].paths[name] then
            error(string.format("Duplicate shape name '%s' in layer '%s'", name, current_layer))
        end
    end
end

-- Core API Functions

--[[
Define a new model and execute the provided function to build it.
@param name string: Name of the model
@param func function: Function that defines the model content
--]]
function M.model_def(name, func)
    validate_name(name, "model_def")
    if type(func) ~= "function" then
        error("model_def: func must be a function")
    end
    
    -- Create new model structure
    local model = {
        name = name,
        layers = {},
        metadata = {
            created = os.date("%Y-%m-%d %H:%M:%S"),
            engine = "lua_makerjs_engine",
            version = "1.0.0"
        }
    }
    
    -- Set as current model and execute the function
    local previous_model = current_model
    local previous_layer = current_layer
    current_model = model
    current_layer = "default"
    
    -- Execute the model definition function
    local success, err = pcall(func)
    if not success then
        current_model = previous_model
        current_layer = previous_layer
        error("Error in model definition: " .. tostring(err))
    end
    
    -- Store the completed model
    models[name] = model
    
    -- Restore previous state
    current_model = previous_model
    current_layer = previous_layer
    
    return model
end

--[[
Set the current layer for subsequent shape operations.
@param name string: Name of the layer
--]]
function M.layer(name)
    validate_name(name, "layer")
    current_layer = name
    ensure_layer()
end

--[[
Create a rectangle shape.
@param name string: Name of the shape
@param x number: X coordinate of top-left corner
@param y number: Y coordinate of top-left corner  
@param width number: Width of the rectangle
@param height number: Height of the rectangle
--]]
function M.rect(name, x, y, width, height)
    validate_name(name, "rect")
    validate_number(x, "x", "rect")
    validate_number(y, "y", "rect")
    validate_number(width, "width", "rect")
    validate_number(height, "height", "rect")
    
    if width <= 0 or height <= 0 then
        error("rect: width and height must be positive")
    end
    
    check_duplicate_name(name)
    ensure_layer()
    
    -- Create rectangle as a closed polyline (Maker.js compatible)
    local points = {
        {round(x, 3), round(y, 3)},
        {round(x + width, 3), round(y, 3)},
        {round(x + width, 3), round(y + height, 3)},
        {round(x, 3), round(y + height, 3)},
        {round(x, 3), round(y, 3)}  -- Close the path
    }
    
    current_model.layers[current_layer].paths[name] = {
        type = "rect",
        origin = {round(x, 3), round(y, 3)},
        width = round(width, 3),
        height = round(height, 3),
        points = points
    }
end

--[[
Create a circle shape.
@param name string: Name of the shape
@param cx number: X coordinate of center
@param cy number: Y coordinate of center
@param radius number: Radius of the circle
--]]
function M.circle(name, cx, cy, radius)
    validate_name(name, "circle")
    validate_number(cx, "cx", "circle")
    validate_number(cy, "cy", "circle")
    validate_number(radius, "radius", "circle")
    
    if radius <= 0 then
        error("circle: radius must be positive")
    end
    
    check_duplicate_name(name)
    ensure_layer()
    
    current_model.layers[current_layer].paths[name] = {
        type = "circle",
        origin = {round(cx, 3), round(cy, 3)},
        radius = round(radius, 3)
    }
end

--[[
Create a line shape with optional 3D coordinates.
@param name string: Name of the shape
@param x1 number: X coordinate of start point
@param y1 number: Y coordinate of start point
@param x2 number: X coordinate of end point
@param y2 number: Y coordinate of end point
@param z1 number: Optional Z coordinate of start point
@param z2 number: Optional Z coordinate of end point
--]]
function M.line(name, x1, y1, x2, y2, z1, z2)
    validate_name(name, "line")
    validate_number(x1, "x1", "line")
    validate_number(y1, "y1", "line")
    validate_number(x2, "x2", "line")
    validate_number(y2, "y2", "line")

    -- Validate Z coordinates if provided
    if z1 ~= nil then
        validate_number(z1, "z1", "line")
    end
    if z2 ~= nil then
        validate_number(z2, "z2", "line")
    end

    check_duplicate_name(name)
    ensure_layer()

    local line_data = {
        type = "line",
        origin = {round(x1, 3), round(y1, 3)},
        end_point = {round(x2, 3), round(y2, 3)}
    }

    -- Add 3D coordinates if provided
    if z1 ~= nil then
        line_data.origin[3] = round(z1, 3)
    end
    if z2 ~= nil then
        line_data.end_point[3] = round(z2, 3)
    end

    current_model.layers[current_layer].paths[name] = line_data
end

--[[
Create a polyline shape.
@param name string: Name of the shape
@param points table: Array of {x, y} coordinate pairs
--]]
function M.polyline(name, points)
    validate_name(name, "polyline")
    validate_points(points, "polyline")

    check_duplicate_name(name)
    ensure_layer()

    -- Round all coordinates
    local rounded_points = {}
    for i, point in ipairs(points) do
        rounded_points[i] = {round(point[1], 3), round(point[2], 3)}
    end

    current_model.layers[current_layer].paths[name] = {
        type = "polyline",
        points = rounded_points
    }
end

-- Helper function to determine current face from ADekoLib
local function get_current_face()
    -- Check if ADekoLib is available and has the necessary variables
    if not _G.ADekoLib then
        return "top" -- Default to top if ADekoLib not available
    end

    -- Access global variables from ADekoLib
    local currentRotation = _G.currentRotation
    local __topFace = _G.__topFace
    local __bottomFace = _G.__bottomFace
    local __leftFace = _G.__leftFace
    local __rightFace = _G.__rightFace
    local __frontFace = _G.__frontFace
    local __rearFace = _G.__rearFace

    if not currentRotation then
        return "top" -- Default to top if no rotation set
    end

    -- Use ADekoLib's areRotationsEqual function if available
    if _G.ADekoLib.areRotationsEqual then
        if __topFace and _G.ADekoLib.areRotationsEqual(currentRotation, __topFace) then
            return "top"
        elseif __bottomFace and _G.ADekoLib.areRotationsEqual(currentRotation, __bottomFace) then
            return "bottom"
        elseif __leftFace and _G.ADekoLib.areRotationsEqual(currentRotation, __leftFace) then
            return "left"
        elseif __rightFace and _G.ADekoLib.areRotationsEqual(currentRotation, __rightFace) then
            return "right"
        elseif __frontFace and _G.ADekoLib.areRotationsEqual(currentRotation, __frontFace) then
            return "front"
        elseif __rearFace and _G.ADekoLib.areRotationsEqual(currentRotation, __rearFace) then
            return "rear"
        end
    end

    return "top" -- Default fallback
end

--[[
Create an arc path (following Maker.js convention: arcs are paths).
MODIFIED: Now renders the complementary arc instead of the original arc
MODIFIED: Reverses complementary logic when face is "bottom"
@param name string: Name of the shape
@param cx number: X coordinate of center
@param cy number: Y coordinate of center
@param radius number: Radius of the arc
@param start_angle number: Start angle in degrees
@param end_angle number: End angle in degrees
@param clockwise boolean: Direction of arc (optional, defaults to false)
--]]
function M.arc(name, cx, cy, radius, start_angle, end_angle, clockwise)
    validate_name(name, "arc")
    validate_number(cx, "cx", "arc")
    validate_number(cy, "cy", "arc")
    validate_number(radius, "radius", "arc")
    validate_number(start_angle, "start_angle", "arc")
    validate_number(end_angle, "end_angle", "arc")

    if radius <= 0 then
        error("arc: radius must be positive")
    end

    clockwise = clockwise or false

    check_duplicate_name(name)
    ensure_layer()

    -- Get current face to determine arc behavior
    local current_face = get_current_face()
    local use_complementary = (current_face ~= "bottom")

    -- Debug output for original arc parameters
    print("DEBUG: Original arc '" .. name .. "' with start_angle=" .. start_angle .. " end_angle=" .. end_angle .. " clockwise=" .. tostring(clockwise) .. " face=" .. current_face)

    local finalStartAngle, finalEndAngle, finalClockwise

    if use_complementary then
        -- Normal complementary arc logic (for top and other faces)
        if clockwise then
            -- For clockwise arcs, the complementary arc goes from endAngle to startAngle counterclockwise
            finalStartAngle = end_angle
            finalEndAngle = start_angle
            finalClockwise = false
        else
            -- For counterclockwise arcs, the complementary arc goes from endAngle to startAngle clockwise
            finalStartAngle = end_angle
            finalEndAngle = start_angle
            finalClockwise = true
        end

        -- Handle angle wrapping for proper 360-degree completion
        if finalClockwise then
            -- For clockwise complementary arc
            if finalEndAngle > finalStartAngle then
                finalEndAngle = finalEndAngle - 360
            end
        else
            -- For counterclockwise complementary arc
            if finalEndAngle < finalStartAngle then
                finalEndAngle = finalEndAngle + 360
            end
        end

        print("DEBUG: Using complementary arc logic")
    else
        -- Bottom face: use original arc (opposite of complementary)
        finalStartAngle = start_angle
        finalEndAngle = end_angle
        finalClockwise = clockwise

        print("DEBUG: Bottom face detected - using original arc logic")
    end

    -- Debug output for final arc parameters
    print("DEBUG: Final arc '" .. name .. "' with start_angle=" .. finalStartAngle .. " end_angle=" .. finalEndAngle .. " clockwise=" .. tostring(finalClockwise))

    -- Store the final arc
    current_model.layers[current_layer].paths[name] = {
        type = "arc",
        origin = {round(cx, 3), round(cy, 3)},
        radius = round(radius, 3),
        start_angle = round(finalStartAngle, 3),
        end_angle = round(finalEndAngle, 3),
        clockwise = finalClockwise
    }

    -- Debug output for stored arc
    local stored_arc = current_model.layers[current_layer].paths[name]
    print("DEBUG: Stored arc has start_angle=" .. tostring(stored_arc.start_angle) .. " end_angle=" .. tostring(stored_arc.end_angle) .. " clockwise=" .. tostring(stored_arc.clockwise))
end



--[[
Create an ellipse shape.
@param name string: Name of the shape
@param cx number: X coordinate of center
@param cy number: Y coordinate of center
@param rx number: X radius (semi-major axis)
@param ry number: Y radius (semi-minor axis)
@param rotation number: Rotation angle in degrees (optional, defaults to 0)
--]]
function M.ellipse(name, cx, cy, rx, ry, rotation)
    validate_name(name, "ellipse")
    validate_number(cx, "cx", "ellipse")
    validate_number(cy, "cy", "ellipse")
    validate_number(rx, "rx", "ellipse")
    validate_number(ry, "ry", "ellipse")

    if rx <= 0 or ry <= 0 then
        error("ellipse: rx and ry must be positive")
    end

    rotation = rotation or 0
    if rotation ~= 0 then
        validate_number(rotation, "rotation", "ellipse")
    end

    check_duplicate_name(name)
    ensure_layer()

    current_model.layers[current_layer].paths[name] = {
        type = "ellipse",
        origin = {round(cx, 3), round(cy, 3)},
        rx = round(rx, 3),
        ry = round(ry, 3),
        rotation = round(rotation, 3)
    }
end



--[[
Create an elliptical arc model (following Maker.js convention: elliptical arcs are models).
@param name string: Name of the model
@param cx number: X coordinate of center
@param cy number: Y coordinate of center
@param rx number: X radius (semi-major axis)
@param ry number: Y radius (semi-minor axis)
@param start_angle number: Start angle in degrees
@param end_angle number: End angle in degrees
@param rotation number: Rotation angle in degrees (optional, defaults to 0)
@param clockwise boolean: Direction of arc (optional, defaults to false)
--]]
function M.elliptical_arc(name, cx, cy, rx, ry, start_angle, end_angle, rotation, clockwise)
    validate_name(name, "elliptical_arc")
    validate_number(cx, "cx", "elliptical_arc")
    validate_number(cy, "cy", "elliptical_arc")
    validate_number(rx, "rx", "elliptical_arc")
    validate_number(ry, "ry", "elliptical_arc")
    validate_number(start_angle, "start_angle", "elliptical_arc")
    validate_number(end_angle, "end_angle", "elliptical_arc")

    if rx <= 0 or ry <= 0 then
        error("elliptical_arc: rx and ry must be positive")
    end

    rotation = rotation or 0
    clockwise = clockwise or false

    if rotation ~= 0 then
        validate_number(rotation, "rotation", "elliptical_arc")
    end

    check_duplicate_name(name)
    ensure_layer()

    -- Elliptical arcs are models in Maker.js, not paths
    current_model.layers[current_layer].models = current_model.layers[current_layer].models or {}
    current_model.layers[current_layer].models[name] = {
        type = "elliptical_arc",
        origin = {round(cx, 3), round(cy, 3)},
        rx = round(rx, 3),
        ry = round(ry, 3),
        start_angle = round(start_angle, 3),
        end_angle = round(end_angle, 3),
        rotation = round(rotation, 3),
        clockwise = clockwise
    }
end

-- Configuration functions

--[[
Enable or disable strict mode for error checking.
@param enabled boolean: Whether to enable strict mode
--]]
function M.set_strict_mode(enabled)
    strict_mode = enabled and true or false
end

--[[
Get the current strict mode setting.
@return boolean: Current strict mode setting
--]]
function M.get_strict_mode()
    return strict_mode
end

--[[
Get the current active model.
@return table: Current model or nil if none active
--]]
function M.get_current_model()
    return current_model
end

--[[
Set the current active model by name.
@param model_name string: Name of the model to set as current
--]]
function M.set_current_model(model_name)
    validate_name(model_name, "set_current_model")

    if not models[model_name] then
        error("Model '" .. model_name .. "' not found")
    end

    current_model = models[model_name]
    current_layer = "default"  -- Reset to default layer
end

--[[
Get all defined models.
@return table: Table of all models indexed by name
--]]
function M.get_all_models()
    return models
end

--[[
Clear all models and reset state.
--]]
function M.clear_all()
    models = {}
    current_model = nil
    current_layer = "default"
end

-- JSON Export Functions

--[[
Simple JSON encoder for Lua tables.
Handles basic types: string, number, boolean, table (array/object), nil
--]]
local function encode_json_value(value, indent_level)
    indent_level = indent_level or 0
    local indent = string.rep("  ", indent_level)
    local next_indent = string.rep("  ", indent_level + 1)

    if value == nil then
        return "null"
    elseif type(value) == "boolean" then
        return value and "true" or "false"
    elseif type(value) == "number" then
        return tostring(value)
    elseif type(value) == "string" then
        -- Basic string escaping
        local escaped = value:gsub("\\", "\\\\"):gsub('"', '\\"'):gsub("\n", "\\n"):gsub("\r", "\\r"):gsub("\t", "\\t")
        return '"' .. escaped .. '"'
    elseif type(value) == "table" then
        -- Check if it's an array (consecutive integer keys starting from 1)
        local is_array = true
        local max_index = 0
        for k, v in pairs(value) do
            if type(k) ~= "number" or k ~= math.floor(k) or k < 1 then
                is_array = false
                break
            end
            max_index = math.max(max_index, k)
        end

        -- Verify consecutive indices for arrays
        if is_array then
            for i = 1, max_index do
                if value[i] == nil then
                    is_array = false
                    break
                end
            end
        end

        if is_array then
            -- Encode as JSON array
            local parts = {}
            for i = 1, max_index do
                table.insert(parts, next_indent .. encode_json_value(value[i], indent_level + 1))
            end
            if #parts == 0 then
                return "[]"
            else
                return "[\n" .. table.concat(parts, ",\n") .. "\n" .. indent .. "]"
            end
        else
            -- Encode as JSON object
            local parts = {}
            for k, v in pairs(value) do
                local key_str = encode_json_value(tostring(k), 0)
                local value_str = encode_json_value(v, indent_level + 1)
                table.insert(parts, next_indent .. key_str .. ": " .. value_str)
            end
            if #parts == 0 then
                return "{}"
            else
                table.sort(parts) -- Sort for consistent output
                return "{\n" .. table.concat(parts, ",\n") .. "\n" .. indent .. "}"
            end
        end
    else
        error("Cannot encode value of type " .. type(value))
    end
end

--[[
Convert internal model structure to Maker.js compatible format.
@param model table: Internal model structure
@return table: Maker.js compatible model structure
--]]
local function convert_to_makerjs_format(model)
    local makerjs_model = {
        models = {},
        paths = {}
    }

    -- Add metadata
    if model.metadata then
        makerjs_model.notes = model.metadata
    end

    -- Convert layers to Maker.js format
    for layer_name, layer_data in pairs(model.layers) do
        local layer_model = {
            paths = {}
        }

        -- Convert each path in the layer
        for path_name, path_data in pairs(layer_data.paths) do
            if path_data.type == "line" then
                layer_model.paths[path_name] = {
                    type = "line",
                    origin = path_data.origin,
                    ["end"] = path_data.end_point
                }
            elseif path_data.type == "circle" then
                layer_model.paths[path_name] = {
                    type = "circle",
                    origin = path_data.origin,
                    radius = path_data.radius
                }
            elseif path_data.type == "arc" then
                layer_model.paths[path_name] = {
                    type = "arc",
                    origin = path_data.origin,
                    radius = path_data.radius,
                    startAngle = path_data.start_angle,
                    endAngle = path_data.end_angle,
                    clockwise = path_data.clockwise
                }
            elseif path_data.type == "rect" then
                -- Convert rectangle to paths (4 lines)
                local rect_model = {
                    paths = {}
                }
                local x, y = path_data.origin[1], path_data.origin[2]
                local w, h = path_data.width, path_data.height

                rect_model.paths.top = {
                    type = "line",
                    origin = {x, y},
                    ["end"] = {x + w, y}
                }
                rect_model.paths.right = {
                    type = "line",
                    origin = {x + w, y},
                    ["end"] = {x + w, y + h}
                }
                rect_model.paths.bottom = {
                    type = "line",
                    origin = {x + w, y + h},
                    ["end"] = {x, y + h}
                }
                rect_model.paths.left = {
                    type = "line",
                    origin = {x, y + h},
                    ["end"] = {x, y}
                }

                layer_model.models = layer_model.models or {}
                layer_model.models[path_name] = rect_model
            elseif path_data.type == "polyline" then
                -- Convert polyline to connected line segments
                local polyline_model = {
                    paths = {}
                }

                for i = 1, #path_data.points - 1 do
                    local segment_name = "segment_" .. i
                    polyline_model.paths[segment_name] = {
                        type = "line",
                        origin = path_data.points[i],
                        ["end"] = path_data.points[i + 1]
                    }
                end

                layer_model.models = layer_model.models or {}
                layer_model.models[path_name] = polyline_model
            end
        end

        -- Convert each model in the layer (like elliptical arcs)
        if layer_data.models then
            for model_name, model_data in pairs(layer_data.models) do
                if model_data.type == "elliptical_arc" then
                    layer_model.models = layer_model.models or {}
                    layer_model.models[model_name] = {
                        type = "EllipticArc",
                        origin = model_data.origin,
                        radiusX = model_data.rx,
                        radiusY = model_data.ry,
                        startAngle = model_data.startAngle,
                        endAngle = model_data.endAngle,
                        rotation = model_data.rotation,
                        clockwise = model_data.clockwise
                    }
                end
            end
        end

        -- Add layer to main model
        if layer_name == "default" then
            -- Merge default layer into root
            for k, v in pairs(layer_model.paths) do
                makerjs_model.paths[k] = v
            end
            if layer_model.models then
                for k, v in pairs(layer_model.models) do
                    makerjs_model.models[k] = v
                end
            end
        else
            makerjs_model.models[layer_name] = layer_model
        end
    end

    return makerjs_model
end

--[[
Export the current model to JSON format compatible with Maker.js.
@param model_name string: Optional name of specific model to export. If nil, exports current model.
@return string: JSON representation of the model
--]]
function M.export_model(model_name)
    local model_to_export

    if model_name then
        model_to_export = models[model_name]
        if not model_to_export then
            error("Model '" .. model_name .. "' not found")
        end
    else
        if not current_model then
            error("No active model to export. Call model_def() first or specify model_name.")
        end
        model_to_export = current_model
    end

    -- Convert to Maker.js format
    local makerjs_model = convert_to_makerjs_format(model_to_export)

    -- Encode to JSON
    return encode_json_value(makerjs_model, 0)
end

--[[
Export the current model to SVG format.
@param model_name string: Optional name of specific model to export. If nil, exports current model.
@param options table: Optional SVG export options (width, height, stroke_width, etc.)
@return string: SVG representation of the model
--]]
function M.export_svg(model_name, options)
    local model_to_export

    if model_name then
        model_to_export = models[model_name]
        if not model_to_export then
            error("Model '" .. model_name .. "' not found")
        end
    else
        if not current_model then
            error("No active model to export. Call model_def() first or specify model_name.")
        end
        model_to_export = current_model
    end

    -- Default SVG options
    local svg_options = options or {}
    local stroke_width = svg_options.stroke_width or 1
    local default_stroke = svg_options.default_stroke or "#000000"
    local background = svg_options.background or "none"

    -- Calculate bounding box
    local min_x, min_y, max_x, max_y = calculate_model_bounds(model_to_export)

    -- Add padding
    local padding = svg_options.padding or 10
    min_x = min_x - padding
    min_y = min_y - padding
    max_x = max_x + padding
    max_y = max_y + padding

    local width = max_x - min_x
    local height = max_y - min_y

    -- Start SVG
    local svg = string.format([[<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="%.3f" height="%.3f" viewBox="%.3f %.3f %.3f %.3f">
  <defs>
    <style>
      .default-path { stroke: %s; fill: none; stroke-width: %.3f; }
      .cut-path { stroke: #ff0000; fill: none; stroke-width: %.3f; }
      .engrave-path { stroke: #0000ff; fill: none; stroke-width: %.3f; }
      .score-path { stroke: #00ff00; fill: none; stroke-width: %.3f; }
    </style>
  </defs>
]], width, height, min_x, min_y, width, height, default_stroke, stroke_width, stroke_width, stroke_width, stroke_width)

    if background ~= "none" then
        svg = svg .. string.format('  <rect x="%.3f" y="%.3f" width="%.3f" height="%.3f" fill="%s" />\n',
            min_x, min_y, width, height, background)
    end

    -- Add coordinate system flip for Y-axis (SVG Y+ is down, CAD Y+ is up)
    svg = svg .. string.format('  <g transform="scale(1,-1) translate(0,%.3f)">\n', -(max_y + min_y))

    -- Export each layer
    for layer_name, layer_data in pairs(model_to_export.layers) do
        svg = svg .. string.format('    <g class="%s-path" data-layer="%s">\n', layer_name:lower(), layer_name)

        -- Export paths in this layer
        for path_name, path_data in pairs(layer_data.paths) do
            svg = svg .. convert_path_to_svg(path_data, svg_options)
        end

        -- Export models in this layer
        if layer_data.models then
            for model_name, model_data in pairs(layer_data.models) do
                svg = svg .. convert_model_to_svg(model_data, svg_options)
            end
        end

        svg = svg .. '    </g>\n'
    end

    svg = svg .. '  </g>\n</svg>'

    return svg
end

-- Helper function to calculate model bounds
function calculate_model_bounds(model)
    local min_x, min_y, max_x, max_y = math.huge, math.huge, -math.huge, -math.huge

    for layer_name, layer_data in pairs(model.layers) do
        for path_name, path_data in pairs(layer_data.paths) do
            local bounds = calculate_path_bounds(path_data)
            if bounds then
                min_x = math.min(min_x, bounds.min_x)
                min_y = math.min(min_y, bounds.min_y)
                max_x = math.max(max_x, bounds.max_x)
                max_y = math.max(max_y, bounds.max_y)
            end
        end

        if layer_data.models then
            for model_name, model_data in pairs(layer_data.models) do
                local bounds = calculate_model_bounds_recursive(model_data)
                if bounds then
                    min_x = math.min(min_x, bounds.min_x)
                    min_y = math.min(min_y, bounds.min_y)
                    max_x = math.max(max_x, bounds.max_x)
                    max_y = math.max(max_y, bounds.max_y)
                end
            end
        end
    end

    return min_x, min_y, max_x, max_y
end

-- Helper function to calculate path bounds
function calculate_path_bounds(path)
    if path.type == "line" then
        local x1, y1 = path.origin[1], path.origin[2]
        local x2, y2 = path.end_point[1], path.end_point[2]
        return {
            min_x = math.min(x1, x2),
            min_y = math.min(y1, y2),
            max_x = math.max(x1, x2),
            max_y = math.max(y1, y2)
        }
    elseif path.type == "circle" then
        local cx, cy, r = path.origin[1], path.origin[2], path.radius
        return {
            min_x = cx - r,
            min_y = cy - r,
            max_x = cx + r,
            max_y = cy + r
        }
    elseif path.type == "arc" then
        local cx, cy, r = path.origin[1], path.origin[2], path.radius
        -- Simplified bounds calculation for arcs
        return {
            min_x = cx - r,
            min_y = cy - r,
            max_x = cx + r,
            max_y = cy + r
        }
    elseif path.type == "rect" then
        local x, y = path.origin[1], path.origin[2]
        return {
            min_x = x,
            min_y = y,
            max_x = x + path.width,
            max_y = y + path.height
        }
    end
    return nil
end

-- Helper function to convert path to SVG
function convert_path_to_svg(path, options)
    local svg = ""

    if path.type == "line" then
        local x1, y1 = path.origin[1], path.origin[2]
        local x2, y2 = path.end_point[1], path.end_point[2]
        svg = string.format('      <line x1="%.3f" y1="%.3f" x2="%.3f" y2="%.3f" />\n', x1, y1, x2, y2)
    elseif path.type == "circle" then
        local cx, cy, r = path.origin[1], path.origin[2], path.radius
        svg = string.format('      <circle cx="%.3f" cy="%.3f" r="%.3f" />\n', cx, cy, r)
    elseif path.type == "arc" then
        local cx, cy, r = path.origin[1], path.origin[2], path.radius
        local start_angle = path.startAngle or 0
        local end_angle = path.endAngle or 360

        -- Convert to SVG arc
        local start_rad = math.rad(start_angle)
        local end_rad = math.rad(end_angle)
        local x1 = cx + r * math.cos(start_rad)
        local y1 = cy + r * math.sin(start_rad)
        local x2 = cx + r * math.cos(end_rad)
        local y2 = cy + r * math.sin(end_rad)

        local large_arc = math.abs(end_angle - start_angle) > 180 and 1 or 0
        local sweep = end_angle > start_angle and 1 or 0

        svg = string.format('      <path d="M %.3f %.3f A %.3f %.3f 0 %d %d %.3f %.3f" />\n',
            x1, y1, r, r, large_arc, sweep, x2, y2)
    elseif path.type == "rect" then
        local x, y = path.origin[1], path.origin[2]
        svg = string.format('      <rect x="%.3f" y="%.3f" width="%.3f" height="%.3f" />\n',
            x, y, path.width, path.height)
    end

    return svg
end

-- Helper function to convert model to SVG (for nested models)
function convert_model_to_svg(model, options)
    local svg = ""
    -- This would handle nested models if needed
    return svg
end

--[[
Export all models to JSON format.
@return string: JSON representation of all models
--]]
function M.export_all_models()
    local all_models = {}
    for name, model in pairs(models) do
        all_models[name] = convert_to_makerjs_format(model)
    end
    return encode_json_value(all_models, 0)
end

-- 3D Sweep Preparation (Future Extension Stubs)

--[[
Define a 2D profile for 3D sweep operations.
This is a stub for future 3D support.
@param name string: Name of the profile
@param model_name string: Name of the 2D model to use as profile
--]]
function M.define_profile(name, model_name)
    validate_name(name, "define_profile")
    validate_name(model_name, "define_profile")

    if not models[model_name] then
        error("Profile model '" .. model_name .. "' not found")
    end

    -- Store profile definition for future use
    if not current_model then
        error("No active model. Call model_def() first.")
    end

    current_model.profiles = current_model.profiles or {}
    current_model.profiles[name] = {
        model = model_name,
        type = "2d_profile"
    }

    print("Profile '" .. name .. "' defined (3D sweep support coming in future version)")
end

--[[
Define a 3D path for sweep operations.
This is a stub for future 3D support.
@param name string: Name of the 3D path
@param path_type string: Type of path ("line", "arc", "spline")
@param points table: Array of 3D points {x, y, z}
--]]
function M.define_3d_path(name, path_type, points)
    validate_name(name, "define_3d_path")

    if not path_type or type(path_type) ~= "string" then
        error("define_3d_path: path_type must be a string")
    end

    if not points or type(points) ~= "table" then
        error("define_3d_path: points must be a table")
    end

    -- Validate 3D points
    for i, point in ipairs(points) do
        if type(point) ~= "table" or #point ~= 3 then
            error(string.format("Invalid 3D point %d: must be a table with 3 coordinates", i))
        end
        for j = 1, 3 do
            validate_number(point[j], "coordinate " .. j, "define_3d_path")
        end
    end

    if not current_model then
        error("No active model. Call model_def() first.")
    end

    current_model.paths_3d = current_model.paths_3d or {}
    current_model.paths_3d[name] = {
        type = path_type,
        points = points
    }

    print("3D path '" .. name .. "' defined (3D sweep support coming in future version)")
end

--[[
Define a sweep operation binding a profile to a 3D path.
This is a stub for future 3D support.
@param name string: Name of the sweep operation
@param profile_name string: Name of the profile to sweep
@param path_name string: Name of the 3D path to sweep along
@param options table: Optional sweep parameters
--]]
function M.define_sweep(name, profile_name, path_name, options)
    validate_name(name, "define_sweep")
    validate_name(profile_name, "define_sweep")
    validate_name(path_name, "define_sweep")

    options = options or {}

    if not current_model then
        error("No active model. Call model_def() first.")
    end

    -- Validate profile exists
    if not current_model.profiles or not current_model.profiles[profile_name] then
        error("Profile '" .. profile_name .. "' not found")
    end

    -- Validate 3D path exists
    if not current_model.paths_3d or not current_model.paths_3d[path_name] then
        error("3D path '" .. path_name .. "' not found")
    end

    current_model.sweeps = current_model.sweeps or {}
    current_model.sweeps[name] = {
        profile = profile_name,
        path = path_name,
        options = options
    }

    print("Sweep operation '" .. name .. "' defined (3D sweep support coming in future version)")
end

-- Transformation Functions

--[[
Apply translation transformation to a shape.
@param shape_name string: Name of the shape to transform
@param dx number: Translation in X direction
@param dy number: Translation in Y direction
@param layer_name string: Optional layer name (uses current layer if nil)
--]]
function M.translate(shape_name, dx, dy, layer_name)
    validate_name(shape_name, "translate")
    validate_number(dx, "dx", "translate")
    validate_number(dy, "dy", "translate")

    ensure_model()
    layer_name = layer_name or current_layer

    if not current_model.layers[layer_name] then
        error("Layer '" .. layer_name .. "' not found")
    end

    local shape = current_model.layers[layer_name].paths[shape_name]
    if not shape then
        error("Shape '" .. shape_name .. "' not found in layer '" .. layer_name .. "'")
    end

    -- Apply translation based on shape type
    if shape.origin then
        shape.origin[1] = round(shape.origin[1] + dx, 3)
        shape.origin[2] = round(shape.origin[2] + dy, 3)
    end

    if shape.end_point then
        shape.end_point[1] = round(shape.end_point[1] + dx, 3)
        shape.end_point[2] = round(shape.end_point[2] + dy, 3)
    end

    if shape.points then
        for i, point in ipairs(shape.points) do
            point[1] = round(point[1] + dx, 3)
            point[2] = round(point[2] + dy, 3)
        end
    end
end

--[[
Apply rotation transformation to a shape around a point.
@param shape_name string: Name of the shape to transform
@param angle number: Rotation angle in degrees
@param cx number: X coordinate of rotation center (default: 0)
@param cy number: Y coordinate of rotation center (default: 0)
@param layer_name string: Optional layer name (uses current layer if nil)
--]]
function M.rotate(shape_name, angle, cx, cy, layer_name)
    validate_name(shape_name, "rotate")
    validate_number(angle, "angle", "rotate")

    cx = cx or 0
    cy = cy or 0
    validate_number(cx, "cx", "rotate")
    validate_number(cy, "cy", "rotate")

    ensure_model()
    layer_name = layer_name or current_layer

    if not current_model.layers[layer_name] then
        error("Layer '" .. layer_name .. "' not found")
    end

    local shape = current_model.layers[layer_name].paths[shape_name]
    if not shape then
        error("Shape '" .. shape_name .. "' not found in layer '" .. layer_name .. "'")
    end

    -- Convert angle to radians
    local rad = math.rad(angle)
    local cos_a = math.cos(rad)
    local sin_a = math.sin(rad)

    -- Helper function to rotate a point
    local function rotate_point(x, y)
        local dx = x - cx
        local dy = y - cy
        local new_x = dx * cos_a - dy * sin_a + cx
        local new_y = dx * sin_a + dy * cos_a + cy
        return round(new_x, 3), round(new_y, 3)
    end

    -- Apply rotation based on shape type
    if shape.origin then
        shape.origin[1], shape.origin[2] = rotate_point(shape.origin[1], shape.origin[2])
    end

    if shape.end_point then
        shape.end_point[1], shape.end_point[2] = rotate_point(shape.end_point[1], shape.end_point[2])
    end

    if shape.points then
        for i, point in ipairs(shape.points) do
            point[1], point[2] = rotate_point(point[1], point[2])
        end
    end
end

--[[
Apply scaling transformation to a shape.
@param shape_name string: Name of the shape to transform
@param sx number: Scale factor in X direction
@param sy number: Scale factor in Y direction (defaults to sx if nil)
@param cx number: X coordinate of scale center (default: 0)
@param cy number: Y coordinate of scale center (default: 0)
@param layer_name string: Optional layer name (uses current layer if nil)
--]]
function M.scale(shape_name, sx, sy, cx, cy, layer_name)
    validate_name(shape_name, "scale")
    validate_number(sx, "sx", "scale")

    sy = sy or sx
    cx = cx or 0
    cy = cy or 0
    validate_number(sy, "sy", "scale")
    validate_number(cx, "cx", "scale")
    validate_number(cy, "cy", "scale")

    if sx <= 0 or sy <= 0 then
        error("scale: scale factors must be positive")
    end

    ensure_model()
    layer_name = layer_name or current_layer

    if not current_model.layers[layer_name] then
        error("Layer '" .. layer_name .. "' not found")
    end

    local shape = current_model.layers[layer_name].paths[shape_name]
    if not shape then
        error("Shape '" .. shape_name .. "' not found in layer '" .. layer_name .. "'")
    end

    -- Helper function to scale a point
    local function scale_point(x, y)
        local new_x = (x - cx) * sx + cx
        local new_y = (y - cy) * sy + cy
        return round(new_x, 3), round(new_y, 3)
    end

    -- Apply scaling based on shape type
    if shape.origin then
        shape.origin[1], shape.origin[2] = scale_point(shape.origin[1], shape.origin[2])
    end

    if shape.end_point then
        shape.end_point[1], shape.end_point[2] = scale_point(shape.end_point[1], shape.end_point[2])
    end

    if shape.points then
        for i, point in ipairs(shape.points) do
            point[1], point[2] = scale_point(point[1], point[2])
        end
    end

    -- Scale radius for circles
    if shape.radius then
        shape.radius = round(shape.radius * math.min(sx, sy), 3)
    end

    -- Scale dimensions for rectangles
    if shape.width then
        shape.width = round(shape.width * sx, 3)
    end
    if shape.height then
        shape.height = round(shape.height * sy, 3)
    end
end

-- Utility Functions

--[[
Create an offset path for tool compensation or kerf adjustment.
@param shape_name string: Name of the shape to offset
@param offset number: Offset distance (positive = outward, negative = inward)
@param new_name string: Name for the new offset shape
@param layer_name string: Optional layer name (uses current layer if nil)
--]]
function M.offset_path(shape_name, offset, new_name, layer_name)
    validate_name(shape_name, "offset_path")
    validate_number(offset, "offset", "offset_path")
    validate_name(new_name, "offset_path")

    ensure_model()
    layer_name = layer_name or current_layer

    if not current_model.layers[layer_name] then
        error("Layer '" .. layer_name .. "' not found")
    end

    local shape = current_model.layers[layer_name].paths[shape_name]
    if not shape then
        error("Shape '" .. shape_name .. "' not found in layer '" .. layer_name .. "'")
    end

    check_duplicate_name(new_name)

    -- Create offset based on shape type
    if shape.type == "circle" then
        local new_radius = shape.radius + offset
        if new_radius <= 0 then
            error("offset_path: offset would result in negative radius for circle")
        end

        current_model.layers[layer_name].paths[new_name] = {
            type = "circle",
            origin = {shape.origin[1], shape.origin[2]},
            radius = round(new_radius, 3)
        }
    elseif shape.type == "rect" then
        local new_width = shape.width + 2 * offset
        local new_height = shape.height + 2 * offset
        if new_width <= 0 or new_height <= 0 then
            error("offset_path: offset would result in negative dimensions for rectangle")
        end

        local new_x = shape.origin[1] - offset
        local new_y = shape.origin[2] - offset

        M.rect(new_name, new_x, new_y, new_width, new_height)
    elseif shape.type == "arc" then
        local new_radius = shape.radius + offset
        if new_radius <= 0 then
            error("offset_path: offset would result in negative radius for arc")
        end

        current_model.layers[layer_name].paths[new_name] = {
            type = "arc",
            origin = {shape.origin[1], shape.origin[2]},
            radius = round(new_radius, 3),
            startAngle = shape.startAngle,
            endAngle = shape.endAngle,
            clockwise = shape.clockwise
        }
    else
        -- For lines and polylines, create a simple parallel offset
        -- This is a basic implementation - more sophisticated offsetting would require
        -- proper geometric algorithms for complex shapes
        print("Warning: Basic offset implementation for " .. shape.type .. " shapes")

        if shape.type == "line" then
            -- Calculate perpendicular offset direction
            local dx = shape.end_point[1] - shape.origin[1]
            local dy = shape.end_point[2] - shape.origin[2]
            local length = math.sqrt(dx*dx + dy*dy)

            if length > 0 then
                local nx = -dy / length * offset  -- Normal vector X
                local ny = dx / length * offset   -- Normal vector Y

                current_model.layers[layer_name].paths[new_name] = {
                    type = "line",
                    origin = {round(shape.origin[1] + nx, 3), round(shape.origin[2] + ny, 3)},
                    end_point = {round(shape.end_point[1] + nx, 3), round(shape.end_point[2] + ny, 3)}
                }
            end
        end
    end
end

--[[
Copy a shape to a new name, optionally in a different layer.
@param shape_name string: Name of the shape to copy
@param new_name string: Name for the copied shape
@param source_layer string: Optional source layer name (uses current layer if nil)
@param target_layer string: Optional target layer name (uses current layer if nil)
--]]
function M.copy_shape(shape_name, new_name, source_layer, target_layer)
    validate_name(shape_name, "copy_shape")
    validate_name(new_name, "copy_shape")

    ensure_model()
    source_layer = source_layer or current_layer
    target_layer = target_layer or current_layer

    if not current_model.layers[source_layer] then
        error("Source layer '" .. source_layer .. "' not found")
    end

    local shape = current_model.layers[source_layer].paths[shape_name]
    if not shape then
        error("Shape '" .. shape_name .. "' not found in layer '" .. source_layer .. "'")
    end

    -- Ensure target layer exists
    if not current_model.layers[target_layer] then
        current_model.layers[target_layer] = { paths = {} }
    end

    -- Check for duplicate name in target layer
    if current_model.layers[target_layer].paths[new_name] then
        if strict_mode then
            error("Shape '" .. new_name .. "' already exists in layer '" .. target_layer .. "'")
        end
    end

    -- Deep copy the shape
    local function deep_copy(obj)
        if type(obj) ~= "table" then return obj end
        local copy = {}
        for k, v in pairs(obj) do
            copy[k] = deep_copy(v)
        end
        return copy
    end

    current_model.layers[target_layer].paths[new_name] = deep_copy(shape)
end

--[[
Get information about a shape.
@param shape_name string: Name of the shape
@param layer_name string: Optional layer name (uses current layer if nil)
@return table: Shape information including bounds, area, etc.
--]]
function M.get_shape_info(shape_name, layer_name)
    validate_name(shape_name, "get_shape_info")

    ensure_model()
    layer_name = layer_name or current_layer

    if not current_model.layers[layer_name] then
        error("Layer '" .. layer_name .. "' not found")
    end

    local shape = current_model.layers[layer_name].paths[shape_name]
    if not shape then
        error("Shape '" .. shape_name .. "' not found in layer '" .. layer_name .. "'")
    end

    local info = {
        name = shape_name,
        type = shape.type,
        layer = layer_name
    }

    -- Calculate bounds and other properties based on shape type
    if shape.type == "circle" then
        local cx, cy, r = shape.origin[1], shape.origin[2], shape.radius
        info.center = {cx, cy}
        info.radius = r
        info.bounds = {
            min_x = cx - r,
            min_y = cy - r,
            max_x = cx + r,
            max_y = cy + r
        }
        info.area = math.pi * r * r
        info.perimeter = 2 * math.pi * r
    elseif shape.type == "arc" then
        local cx, cy, r = shape.origin[1], shape.origin[2], shape.radius
        local start_angle = math.rad(shape.startAngle)
        local end_angle = math.rad(shape.endAngle)
        local angle_diff = math.abs(end_angle - start_angle)

        info.center = {cx, cy}
        info.radius = r
        info.start_angle = shape.startAngle
        info.end_angle = shape.endAngle
        info.clockwise = shape.clockwise

        -- Calculate arc bounds (simplified - could be more precise)
        info.bounds = {
            min_x = cx - r,
            min_y = cy - r,
            max_x = cx + r,
            max_y = cy + r
        }
        info.arc_length = r * angle_diff
    elseif shape.type == "rect" then
        local x, y, w, h = shape.origin[1], shape.origin[2], shape.width, shape.height
        info.origin = {x, y}
        info.width = w
        info.height = h
        info.bounds = {
            min_x = x,
            min_y = y,
            max_x = x + w,
            max_y = y + h
        }
        info.area = w * h
        info.perimeter = 2 * (w + h)
    elseif shape.type == "line" then
        local x1, y1 = shape.origin[1], shape.origin[2]
        local x2, y2 = shape.end_point[1], shape.end_point[2]
        info.start = {x1, y1}
        info.end_point = {x2, y2}
        info.bounds = {
            min_x = math.min(x1, x2),
            min_y = math.min(y1, y2),
            max_x = math.max(x1, x2),
            max_y = math.max(y1, y2)
        }
        info.length = math.sqrt((x2-x1)*(x2-x1) + (y2-y1)*(y2-y1))
    elseif shape.type == "polyline" then
        local min_x, min_y = math.huge, math.huge
        local max_x, max_y = -math.huge, -math.huge
        local total_length = 0

        for i, point in ipairs(shape.points) do
            min_x = math.min(min_x, point[1])
            min_y = math.min(min_y, point[2])
            max_x = math.max(max_x, point[1])
            max_y = math.max(max_y, point[2])

            if i > 1 then
                local prev = shape.points[i-1]
                local dx = point[1] - prev[1]
                local dy = point[2] - prev[2]
                total_length = total_length + math.sqrt(dx*dx + dy*dy)
            end
        end

        info.points = shape.points
        info.point_count = #shape.points
        info.bounds = {
            min_x = min_x,
            min_y = min_y,
            max_x = max_x,
            max_y = max_y
        }
        info.length = total_length
    end

    return info
end

--[[
List all shapes in a layer or all layers.
@param layer_name string: Optional layer name (lists all layers if nil)
@return table: List of shape names and their types
--]]
function M.list_shapes(layer_name)
    ensure_model()

    local shapes = {}

    if layer_name then
        if not current_model.layers[layer_name] then
            error("Layer '" .. layer_name .. "' not found")
        end

        for name, shape in pairs(current_model.layers[layer_name].paths) do
            table.insert(shapes, {
                name = name,
                type = shape.type,
                layer = layer_name
            })
        end
    else
        for layer, layer_data in pairs(current_model.layers) do
            for name, shape in pairs(layer_data.paths) do
                table.insert(shapes, {
                    name = name,
                    type = shape.type,
                    layer = layer
                })
            end
        end
    end

    return shapes
end

--[[
Delete a shape from a layer.
@param shape_name string: Name of the shape to delete
@param layer_name string: Optional layer name (uses current layer if nil)
--]]
function M.delete_shape(shape_name, layer_name)
    validate_name(shape_name, "delete_shape")

    ensure_model()
    layer_name = layer_name or current_layer

    if not current_model.layers[layer_name] then
        error("Layer '" .. layer_name .. "' not found")
    end

    if not current_model.layers[layer_name].paths[shape_name] then
        error("Shape '" .. shape_name .. "' not found in layer '" .. layer_name .. "'")
    end

    current_model.layers[layer_name].paths[shape_name] = nil
end

return M
