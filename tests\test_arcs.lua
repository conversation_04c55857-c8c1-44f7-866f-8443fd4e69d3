-- Test script to verify arc drawing functionality
-- This script creates various arcs to test the arc rendering

print("Testing arc drawing functionality...")

-- Test 1: Simple full circle (should use circle command)
currentLayerName = "TEST_CIRCLES"
currentThickness = 5
crcl(100, 100, 50)  -- Full circle - no angles

-- Test 2: Quarter arc (should use arc command)
currentLayerName = "TEST_ARCS"
currentThickness = 3
crcl(200, 100, 50, nil, 0, 90)  -- Quarter arc from 0° to 90°

-- Test 3: Half arc (should use arc command)
crcl(300, 100, 50, nil, 180, 270)  -- Quarter arc from 180° to 270°

-- Test 4: Three-quarter arc (should use arc command)
crcl(400, 100, 50, nil, 45, 315)  -- Large arc from 45° to 315°

-- Test 5: Small arc (should use arc command)
crcl(100, 200, 30, nil, 30, 60)  -- Small arc from 30° to 60°

-- Test 6: Reverse arc (should use arc command)
crcl(200, 200, 30, nil, 270, 90)  -- Arc that crosses 0°

print("Arc test script completed!")
