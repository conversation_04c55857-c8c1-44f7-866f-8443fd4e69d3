-- Automatic Tool Detection Demo
-- This script demonstrates how layer names automatically determine CNC tools

function modelMain()
  G = AdekoLib
  
  -- Door dimensions
  local doorWidth = 600
  local doorHeight = 800
  local doorThickness = 18
  
  G.setThickness(-doorThickness)
  
  -- Create door outline
  G.makePart<PERSON>hape({0, 0}, {doorWidth, 0}, {doorWidth, doorHeight}, {0, doorHeight}, {0, 0})
  
  -- TOP SURFACE OPERATIONS
  G.setFace("top")
  
  -- 1. K = Kanal (Groove) operations
  -- K_Freze10mm - Automatically selects 10mm cylindrical end mill
  G.setLayer("K_Freze10mm")
  G.setThickness(-3)
  G.rectangle({50, 50}, {550, 150})

  -- K_Ballnose6mm - Automatically selects 6mm ball nose end mill
  G.setLayer("K_Ballnose6mm")
  G.setThickness(-2)
  G.circle({300, 400}, 80)

  -- K_AciliV90 - Automatically selects 90° V-bit
  G.setLayer("K_AciliV90")
  G.setThickness(-2)
  <PERSON><PERSON>rectangle({100, 600}, {500, 650})

  -- Test different diameter patterns
  G<PERSON>setLayer("Freze3mm")  -- Should detect 3mm cylindrical
  G.setThickness(-1)
  G.line({50, 200}, {150, 200})

  G.setLayer("20mmFreze")  -- Should detect 20mm cylindrical
  G.setThickness(-4)
  G.line({50, 250}, {150, 250})

  G.setLayer("Ballnose_12")  -- Should detect 12mm ballnose (virtual)
  G.setThickness(-2)
  G.line({50, 300}, {150, 300})
  
  -- 2. H = Contour operations
  -- H_Freze20mm_DIS - Outer contour with 20mm end mill
  G.setLayer("H_Freze20mm_DIS")
  G.setThickness(-5)
  G.rectangle({20, 20}, {580, 780})

  -- H_Ballnose6mm_IC - Inner contour with 6mm ball nose
  G.setLayer("H_Ballnose6mm_IC")
  G.setThickness(-3)
  G.circle({150, 200}, 50)

  -- Test different angle patterns
  G.setLayer("V45")  -- Should detect 45° V-bit
  G.setThickness(-2)
  G.line({200, 350}, {300, 350})

  G.setLayer("AciliV120")  -- Should detect 120° V-bit (virtual)
  G.setThickness(-3)
  G.line({200, 400}, {300, 400})

  G.setLayer("Oyuk30")  -- Should detect 30° V-bit (virtual)
  G.setThickness(-1)
  G.line({200, 450}, {300, 450})
  
  -- BOTTOM SURFACE OPERATIONS
  G.setFace("bottom")
  
  -- Bottom surface operations with _SF suffix
  -- H_Freze10mm_IC_SF - Inner contour on bottom surface
  G.setLayer("H_Freze10mm_IC_SF")
  G.setThickness(-8)
  
  -- Hinge pockets
  G.rectangle({20, 100}, {100, 200})  -- Top hinge
  G.rectangle({20, 350}, {100, 450})  -- Middle hinge
  G.rectangle({20, 600}, {100, 700})  -- Bottom hinge
  
  -- K_Freze20mm_SF - Groove on bottom surface
  G.setLayer("K_Freze20mm_SF")
  G.setThickness(-6)
  G.rectangle({150, 50}, {450, 100})
  
  -- 3. V = V-tool operations
  -- V_Oyuk45 - 45° V-groove
  G.setLayer("V_Oyuk45")
  G.setThickness(-3)
  G.line({200, 200}, {400, 200})
  G.line({200, 250}, {400, 250})
  
  -- 4. Special operations
  -- CEP_ACMA - Pocket clearing (automatically selects appropriate end mill)
  G.setLayer("CEP_ACMA")
  G.setThickness(-10)
  G.rectangle({450, 300}, {550, 500})
  
  -- PANEL - General panel machining
  G.setLayer("PANEL")
  G.setThickness(-1)
  G.rectangle({0, 0}, {doorWidth, doorHeight})
  
  return true
end

-- Layer naming conventions demonstrated:
--
-- K = Kanal (Groove from center)
-- - K_Freze10mm    -> 10mm cylindrical end mill
-- - K_Ballnose6mm  -> 6mm ball nose end mill  
-- - K_AciliV90     -> 90° V-bit
--
-- H = Contour operations
-- - H_Freze20mm_DIS -> 20mm end mill, outer contour
-- - H_Ballnose6mm_IC -> 6mm ball nose, inner contour
-- - H_Freze10mm_IC_SF -> 10mm end mill, inner contour, bottom surface
--
-- V = V-tool operations
-- - V_Oyuk45 -> 45° V-bit
--
-- Suffixes:
-- - _SF = Bottom surface (SF = Sıfır Face / Bottom Face)
-- - _DIS = Outer contour (DIS = Dış / Outer)
-- - _IC = Inner contour (IC = İç / Inner)
--
-- The automatic tool detection system will:
-- 1. Parse the layer name
-- 2. Identify the operation type (K/H/V)
-- 3. Extract tool diameter or angle
-- 4. Determine surface (top/bottom)
-- 5. Select the most appropriate tool from the library
-- 6. Recommend cutting parameters
