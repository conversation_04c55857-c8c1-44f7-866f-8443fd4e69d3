{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nH_Freze*cT*mm_Ic\t*cT*mm Freze Bicagi \nK_AciliV*aV*\t\t*aV* Acili V bit \nK_Freze*cW*mm_SF\t*cW*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 140, "description": "minimum dimension limit on the x axis", "parameterName": "xMin"}, {"defaultValue": 140, "description": "minimum dimension limit on the y axis", "parameterName": "yMin"}, {"defaultValue": 6, "description": "offset from the edges", "parameterName": "a"}, {"defaultValue": 3, "description": "width of the block laths", "parameterName": "c"}, {"defaultValue": 9, "description": "offset from the outer notch on the x axis", "parameterName": "marginX"}, {"defaultValue": 9, "description": "offset from the outer notch on the y axis", "parameterName": "marginY"}, {"defaultValue": 30, "description": "size of the pattern on the x axis", "parameterName": "structureSizeX"}, {"defaultValue": 30, "description": "size of the pattern on the y axis", "parameterName": "structureSizeY"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 90, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 14, "description": "mm, window depth from top", "parameterName": "windowDepthFront"}, {"defaultValue": 5, "description": "Cam yeri kesim bicak capi", "parameterName": "cT"}, {"defaultValue": 20, "description": "Cam yeri tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}