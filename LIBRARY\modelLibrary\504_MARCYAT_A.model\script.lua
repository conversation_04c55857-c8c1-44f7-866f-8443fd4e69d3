-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  xMin = 200
  yMin = 200
  
  a = 0
  b = 10
  c = 30
  y = 10 -- yaklaşık
  z = 5
  d = 1		--1 yatay - 0 dikey
 
  extEdgeVtoolExist       	= 0   -- Dis kenar <PERSON>h islemi var mi? derinlik/0:yok
  intEdgeVtoolExist			= 0	  -- Ic kenar <PERSON>h islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Ka<PERSON><PERSON> kose Radus<PERSON> var mi? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
    
  if X < xMin or Y < yMin then
    G.error("Dimension too small.")
    return false
  end
  
  G.setLayer("Cep_Acma")
  G.setThickness(-z)
  if (d == 1) then 
    local n = math.floor((Y-2*b+c)/(y+c))
	
	if (((Y-2*b+c)/(y+c))-n) > 0.5 then
		n = n + 1
	end
	
    local mX = X-2*a
    local mY = (Y-2*b-(n-1)*c)/(n)
    for i=0, n-1, 1
    do
      local rpX = a+mX/2
      local rpY = b+(i*mY)+(i*c)+mY/2;
      G.setLayer("Cep_Acma")
	  G.setThickness(-z)
      G.rectangle({rpX-mX/2, rpY-mY/2}, {rpX+mX/2, rpY+mY/2})
	  if intEdgeVtoolExist > 0 then
		G.setLayer("K_AciliV")
		G.setThickness(-intEdgeVtoolExist)
		G.rectangle({rpX-mX/2, rpY-mY/2}, {rpX+mX/2, rpY+mY/2})
	  end
    end
  else
    local n = math.floor((X-2*a+c)/(y+c))
	
	if (((X-2*a+c)/(y+c))-n) > 0.5 then
		n = n + 1
	end
	
    local mY = Y-2*a
    local mX = (X-2*b-(n-1)*c)/(n)
    for i=0, n-1, 1
    do
      local rpY = a+mY/2
      local rpX = b+(i*mX)+(i*c)+mX/2;
      G.setLayer("Cep_Acma")
	  G.setThickness(-z)
      G.rectangle({rpX-mX/2, rpY-mY/2}, {rpX+mX/2, rpY+mY/2})
	  if intEdgeVtoolExist > 0 then
		G.setLayer("K_AciliV")
		G.setThickness(-intEdgeVtoolExist)
		G.rectangle({rpX-mX/2, rpY-mY/2}, {rpX+mX/2, rpY+mY/2})
	  end
    end
  end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
