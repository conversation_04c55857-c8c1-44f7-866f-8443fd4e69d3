-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  
  a = 10 --Dış Vbitten iç kanala
  b = 10 --iç kanaldan en iç kanala
  howMany = 4 --Vbitten Dıs kanal 2 ye
  aV = 120    --Vbit Açısı
  vd = 5					--pah ve acılı ve işlemlerinin derinliği
  dB = 10
  local dF = 10
  topDist = 100
  gStartGap = 20
  ad = 3					-- ortadaki kanal çizgilerinin derinliği ve varsa yatay kanal çizgi derinliği
  
   doTopShapeGrv = 2.5 --Varsa ust alttaki desen derinlik yoksa 0 --ad ile aynı yarlanmalı (Var: Derinlik / Yok: 0)
   horLineExist       = 1
   extEdgeToolExist 	= 1  --<PERSON><PERSON><PERSON> kenar Pah işlemi var mı? (Var: 1 / Yok: 0)
   edgeCornerRExist 	= 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
   
   local extEdgeBorForV 		= 2  --Dış kenar Pah işlemi Ballnose mu? Duz mu? V mi? (B:0/F:1/V:2)
   local topChamferExist	  = 0
   local bottomPartExist    = 0 
   local midChamferExist	  = 1
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
	local bottomDist = 0
	if bottomPartExist > 0 then
		bottomDist = topDist
    end
	
  --local toolType = "Form" -- "Freze" , "Ballnose" or "Acili_V"
  --local layerNameEd ="K_" .. toolType .. tostring(toolDiameter) .. "mm"
  -- local cR = 5--Raduslu ıc kenar pah bıçak uç düz çapı 
  
  local vWideDiameter = 60
  local chamferWidth = 0
  local ballnoseGrooveWidth = (((dB/2)^2 - ((dB/2)-ad)^2)^0.5) * 2	--kanal çizgileri kalınlık hesabı -TAM-
  
  local D = edgeCornerRExist
  local B = math.tan(math.pi/8)
  local topGrooveWidth =  ((dB/2)^2 - ((dB/2)-doTopShapeGrv)^2)^0.5 --varsa üstteki top burun kanal bıçak genişliği
  
  local doBottomLine = true
  local doTopLine = true
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist > 0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end

	
	local layerName = ""
	if extEdgeToolExist  > 0 then
		if extEdgeBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			layerName = "K_Ballnose"..dB.."mm"
			if aV > dB/2 then
				chamferWidth = dB/2
			else
				chamferWidth = ((dB/2)^2 - ((dB/2)-aV)^2)^0.5 --pah ballnose ile olursa genişlik hesabı -yarım-
			end
		elseif extEdgeBorForV == 1 then
			layerName = "K_Freze"..dF.."mm"  
			chamferWidth = dF/2
		elseif extEdgeBorForV == 2 then
			layerName = "K_AciliV"..aV 
			chamferWidth = vd*math.tan((math.pi*aV/180)/2)
		end	

		if topChamferExist ==1 then	
			point11 = {0, Y}
			point12 = {X, Y-topDist}
			if bottomPartExist >0 then
				point21 = {0, 0}
				point22 = {X,bottomDist}
			end
		else 
			G.setLayer(layerName)
			G.setThickness(-vd)
			point11 = {0, 0}
			point12 = {X, Y}
			G.rectangle(point11,point12)
		end
	
	end	
	
  local distance = a + chamferWidth + topGrooveWidth					--üst ve alt bölüm içlerine kanallar sığıyor mu diye kontrol ve kanalın kendisi için gerekli
  local distance2 = chamferWidth + a + topGrooveWidth*2 + b + topGrooveWidth --üst ve alt bölüm içlerine kanallar sığıyor mu diye kontrol ve kanalın kendisi için gerekli
  local distance3 = a + chamferWidth									--sunken için kenardan mesafe
  
  local equalGapCalc = (X-(chamferWidth*2 + ballnoseGrooveWidth*howMany)) / (howMany+1)  --orta kanal aralıklarını hesaplama
	
  
	G.setLayer("K_Ballnose"..dB.."mm")
	G.setThickness(-ad)
	
	for i=0, howMany-1, 1		-- loop for the vertical lines
    do
      local Lp1 = {chamferWidth+equalGapCalc+ballnoseGrooveWidth/2+i*(equalGapCalc+ballnoseGrooveWidth), Y-topDist-gStartGap}	
      local Lp2 = {chamferWidth+equalGapCalc+ballnoseGrooveWidth/2+i*(equalGapCalc+ballnoseGrooveWidth), bottomDist+gStartGap}	
      G.line(Lp1, Lp2)
	end
	  
	if topDist >0 then --üstteki şekilli alan
		G.setLayer(layerName)
		if topChamferExist ==1 then					--ust bolum pah
			G.setThickness(-vd)
			G.rectangle(point11,point12)
			doTopLine = false
			
			if bottomPartExist >0 then					--alt bolum pah
				G.rectangle(point21,point22)
				doBottomLine = false 
			end
			if midChamferExist ==1 and bottomPartExist>0 then	-- orta kısım alt bolume kadar kenar pah
				G.line({X, Y-topDist}, {X,bottomDist})
				G.line({0, Y-topDist}, {0,bottomDist})
			elseif midChamferExist ==1 and bottomPartExist==0 then	--orta kısım en alta kadar pah
				G.line({X, Y-topDist}, {X,0})
				G.line({0, Y-topDist}, {0,0})
				G.line({0, 0}, {X,0})
			end
		end
		
		if doTopShapeGrv > 0 and topDist>2*distance then
			G.setLayer("K_Ballnose"..dB.."mm")
			G.setThickness(-doTopShapeGrv)
			point3 = {0+distance, Y-distance}
			point4 = {X-distance , Y-(topDist-distance)}
			G.rectangle(point3,point4)
			point5 = {0+distance2, Y-distance2}
			point6 = {X-distance2 , Y-(topDist-distance2)}
			if topDist>2*distance2 and b>0 then
				G.rectangle(point5,point6)
			end
		else
			if topDist >= (4*chamferWidth+2*a) then
				G.setLayer("K_AciliV"..aV)
				G.setThickness(0)
				point3 = {0+distance3, Y-distance3}
				point4 = {X-distance3 , Y-topDist+distance3}
				G.sunkenFrame(point3,point4, vd, aV, 60)
			end
		end
		if bottomPartExist > 0 then
					
			--bu olursa alt yatay çizgiye gerek yok
			if doTopShapeGrv > 0 and topDist>2*distance then
				G.setLayer("K_Ballnose"..dB.."mm")
				-- distance = b + sunkenWidthTop + topGrooveWidth
				G.setThickness(-doTopShapeGrv)
				point3 = {0+distance, distance}
				point4 = {X-distance , bottomDist-distance}
				G.rectangle(point3,point4)
				-- distance2 = sunkenWidthTop + b + topGrooveWidth*2 + c + topGrooveWidth
				point5 = {0+distance2, distance2}
				point6 = {X-distance2 , bottomDist-distance2}
				if topDist>2*distance2  and b>0 then
					G.rectangle(point5,point6)
				end
			else
				if bottomDist >= (4*chamferWidth+2*a) then
					G.setLayer("K_AciliV"..aV)
					G.setThickness(0)
					point3 = {0+distance3, distance3}
					point4 = {X-distance3 , bottomDist-distance3}
					G.sunkenFrame(point3,point4, vd, aV, 60)
				end
			end
		end
	end
  
 
	
	if horLineExist>0 then					--yatay çizgiler var mı
		G.setLayer("K_Ballnose"..dB.."mm")
		G.setThickness(-ad)
		if topDist > 0 and doTopLine then					--ust yatay çizgi var mı
			G.line({0,Y-topDist}, {X,Y-topDist})	
		end
		if bottomPartExist > 0 and doBottomLine then			--alt yatay çizgi var mı
			G.line({0,bottomDist}, {X,bottomDist})
		end
	end
  
  
  
  
  --G.list()
  return true
end

require "ADekoDebugMode"
