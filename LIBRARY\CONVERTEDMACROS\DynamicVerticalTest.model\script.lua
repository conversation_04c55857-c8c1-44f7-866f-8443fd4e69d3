-- ADekoCAM, Model Script - Dynamic Vertical Dividers Test
-- Converted from C# azCAM macro: Dynamic Vertical.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Dynamic Vertical Dividers parameters (from original C# macro)
  A = 60     -- Left/right margin
  B = 60    -- Top/bottom margin  
  C = 20   -- Gap between dividers
  dividerX = 20  -- Width of each divider (~X parameter from C#)
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * A + dividerX + 20  -- minimum required width
  local minHeight = 2 * B + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Dynamic Vertical Dividers function (converted from C# macro)
  local function dynamic_vertical_dividers(A, B, C, dividerX, Z)
    -- Use default values if parameters not provided
    A = A or 0
    B = B or 50
    C = C or 40
    dividerX = dividerX or 100
    Z = Z or -5
    
    -- Calculate number of dividers that can fit
    -- M = Floor((width-2*A+C) / (dividerX+C))
    local M = math.floor((width - 2*A + C) / (dividerX + C))
    
    if M <= 0 then
      print("No dividers can fit with current parameters")
      return false
    end
    
    -- Calculate dimensions
    local mX = (width - 2*A - (M-1)*C) / M  -- Actual width of each divider
    local mY = height - 2*B  -- Height of each divider (full height minus margins)
    
    print(string.format("Creating %d vertical dividers", M))
    print(string.format("Each divider: %.1f x %.1f mm", mX, mY))
    
    -- Set layer and thickness for dividers
    G.setLayer("K_Freze10mm")
    G.setThickness(Z)
    
    -- Create each divider
    for j = 0, M-1 do
      -- Calculate position for this divider
      local rectPosX = A + (j * mX) + (j * C) + mX/2  -- Center X position
      local rectPosY = B + mY/2  -- Center Y position
      
      -- Create rectangle for this divider
      -- Rectangle corners relative to center
      local x1 = rectPosX - mX/2
      local y1 = rectPosY - mY/2
      local x2 = rectPosX + mX/2
      local y2 = rectPosY + mY/2
      
      -- Create the rectangle using polyline
      G.polyline(
        {x1, y1},  -- bottom-left
        {x2, y1},  -- bottom-right
        {x2, y2},  -- top-right
        {x1, y2},  -- top-left
        {x1, y1}   -- close the path
      )
      
      -- Start new shape for next divider
      if j < M-1 then
        G.nextShape()
      end
      
      print(string.format("Divider %d: center at (%.1f, %.1f)", j+1, rectPosX, rectPosY))
    end
    
    return true
  end
  
  -- Call the dynamic vertical dividers function
  local success = dynamic_vertical_dividers(A, B, C, dividerX, -Z)
  
  if success then
    print(string.format("Dynamic vertical dividers created with parameters:"))
    print(string.format("  A (left/right margins): %d mm", A))
    print(string.format("  B (top/bottom margins): %d mm", B))
    print(string.format("  C (gap between dividers): %d mm", C))
    print(string.format("  Divider width: %d mm", dividerX))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Door size: %dx%d mm", X, Y))
  end
  
  return true
end

require "ADekoDebugMode"
