{"modelNote": "Katmanlar:\tTakimlar: \n--------------------------------------------\nK_Ballnose\tTop burun kanal bicagi \nV_AciliV*aV*\t*aV* derece merkez acili V takim ", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 350, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "offset from the edges", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 120, "description": "Acili vbit acisi", "parameterName": "aV"}, {"defaultValue": 2, "description": "fine gap", "parameterName": "b"}, {"defaultValue": 2, "description": " K_Ballnose kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [{"tag": "door"}], "version": 1}