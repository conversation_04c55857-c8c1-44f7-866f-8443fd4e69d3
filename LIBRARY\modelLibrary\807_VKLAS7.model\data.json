{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Acili_aV \t\tAcili V bicagi \nCep_Acma\t\t Tarama duz Freze bicagi \nK_Freze_cT_mm\t\t cT Capli Freze bicagi \nK_Ballnose\t\t Kure uclu kanal bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar kapakda acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "<PERSON><PERSON> derin<PERSON>gi", "parameterName": "ad"}, {"defaultValue": 6, "description": "vbit ustu kanal ve ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 50, "description": "kose<PERSON><PERSON>i yay ya<PERSON>pi", "parameterName": "yaricap"}, {"defaultValue": 40, "description": "varsa gobekteki kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 2, "description": "vbit ustu kanal var mi? (var :derinlik/ yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 2, "description": "Ic kanal var mi? (var :derinlik/ yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}