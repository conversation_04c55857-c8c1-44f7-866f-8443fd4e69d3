-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  G = ADekoLib 
  minimum = 150
  limit = 350
  
  a = 100
  aa = 80
  ad = 8
  cW = 20                   -- Tarama Bicak capi
  cT = 10                   -- ince bicak capi (Ko<PERSON> Te<PERSON>zle<PERSON> ve pencere kesim)
  
  windowDepthFront = 14
  windowDepthBack = 6
  
  extEdgeVtoolExist       	= 0   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Ka<PERSON>k kose Radusu var mi? derinlik/0:yok
  
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
  local bulge = math.tan(math.pi/8)
 
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  G.setLayer("H_Desen_Dis")
  G.setThickness(-ad)
  G.rectangle({a, ust}, {X-a, Y-ust})
  
  G.setLayer("H_Freze"..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.rectangle({a,ust},{X-a,Y-ust})
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-cW/2, ust-cW/2}, {X-a+cW/2, Y-ust+cW/2})
  
  G.setLayer("K_Freze"..cW.."mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a, ust}, {X-a, Y-ust})
    
  return true
end

require "ADekoDebugMode"