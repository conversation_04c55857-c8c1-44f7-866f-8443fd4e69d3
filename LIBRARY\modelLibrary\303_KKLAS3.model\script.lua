 --ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 60          -- Kenardan Vbite
  aa = 30		  -- Dar kapak için Kenardan mesafe
  b = 15          -- Vbitten Dıs kanala
  c = 15          -- Kapak Kenarından Dıs kanala
  ad = 6          -- Vbit derinliği (sunkenDepth)
  cT = 6          -- İnce bıçak çapı (Köşe Temizleme)
      
  extGrooveExist   			   		= 3   -- Dış kanal var mı? derinlik/0:yok  
  edgeCornerRExist          		= 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
    
  local cW = 20          -- Tarama Bıçak Çapı
  local cD = 20				-- Radus Veren form bıcak düz geniç çapı
  local cR = 10		        -- Radus Veren form bıcak düz uç dar çapı veya parça dışına kaydırma mesafesi
  local aV = 90         -- Vbit Açısı
  --bd = 3          -- dış kanal derinliği
	
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end

  local extEdgeRtoolExist       	= 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
  local vBitOperation   			= false   -- vbit işlemi var mı true/false:yok  
  local rToolExist 					= true
  local D = edgeCornerRExist

  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  local vWideDiameter = 60
  local sunkenWidth1 = ad * math.tan((math.pi*aV/180)/2)
	if (cD-cR) > 0 and rToolExist then
		local sunkenWidth2 = cD-cR
	end
  local sunkenWidth = 0


  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end

  
  	local corner1, corner2 = {0,0,0}
  
	if rToolExist then
		sunkenwidth = sunkenWidth2
		G.setLayer("K_Raduslu_" .. cD .. "mm")  -- 
		G.setThickness(-ad)
		distance = a+cD/2
		distance2 = ust+cD/2
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
		corner1 = {a+sunkenWidth, ust+sunkenWidth,0}
		corner2	= {X-(a+sunkenWidth), Y-(ust+sunkenWidth),0}
		
	elseif vBitOperation then
		sunkenwidth = sunkenWidth1
		G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
		G.setThickness(0)
		corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	end
	
	  -------------- Göbek 0:yoksa tüm kapaklarda
    G.setLayer("Cep_Acma")  -- DEEP cleanup
    G.setThickness(-ad)
    distance = a+sunkenWidth
    distance2 = ust+sunkenWidth
    corner1 = {distance, distance2}
    corner2 = {X-distance, Y-distance2}
    G.rectangle(corner1,corner2)
  
	if extGrooveExist > 0 then
		G.setLayer("K_Freze"..cT.."mm") 
		G.setThickness(-extGrooveExist)
		distance = a-b
		distance2 = ust-b
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	
		distance1 = c
		point1 = {distance1, distance1}
		point2 = {X-distance1, Y-distance1}
		G.rectangle(point1,point2)
	end
  
  if cW <= 0 then
    cW = cT
  end
  
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
	
	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
	

  if cW ~= cT then
	if rToolExist and cR > 0 then
		G.setLayer("K_Raduslu_" .. cD .. "mm")
		G.setThickness(0)
		G.cleanCorners(corner1,corner2,ad,cR)
	else
		G.setLayer("K_TarKoseTemizl"..cT.."mm")
		G.setThickness(0)
		G.cleanCorners(corner1,corner2,ad,cT)
    end
  end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
