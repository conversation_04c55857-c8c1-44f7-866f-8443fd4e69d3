-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  xLimit = 300                                      -- Form değiştirme sınırı olan kapak en değeri
  yLimit = 300                                      -- Form değiştirme sınırı olan kapak boy değeri
  xMin = 140                                      -- Mümkün olan en küçük kapak en değeri
  yMin = 140                                      -- Mümkün olan en küçük kapak boy değeri
  
  a = 50                                                  -- kenardan offset
  
  print("Default model parameters are: ", a, "and", xLimit, yLimit, xMin, yMin)
  
  G = ADekoLib
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  print("Modified model parameters are: ", a, "and", xLimit, yLimit, xMin, yMin)
	
  local finalDepth = 8                                         -- <PERSON>lam derinlik
  local notchWidth = 2                                             -- çentik genişlikleri
  local notchDepth = 1                                             -- Çentik derinliği
  local sunkenDepth1 = 2                                            -- Açılı yüzey derinliği
  local sunkenDepth2 = 2*sunkenDepth1+notchDepth                                       -- Açılı yüzey derinliği
  local sunkenDepth3 = finalDepth-sunkenDepth2                                     -- Açılı yüzey derinliği
  local vNarrowAngle = 60
  local vMidAngle = 90
  local vWideAngle = 150
  local vNarrowDiameter = 30
  local vMidDiameter = 40
  local vWideDiameter = 75
  local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vNarrowAngle/180)/2.0)   -- Açılı yüzey genişliği
  local sunkenWidth2 = sunkenDepth1*math.tan((math.pi*vWideAngle/180)/2.0)   -- Açılı yüzey genişliği
  local sunkenWidth3 = sunkenDepth2*math.tan((math.pi*vMidAngle/180)/2.0)   -- Açılı yüzey genişliği
  local sunkenWidth4 = sunkenDepth3*math.tan((math.pi*vMidAngle/180)/2.0)   -- Açılı yüzey genişliği
  local overlap = 10
  local cThinDiameter = 5
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  if X>xLimit and Y>yLimit then
    
    G.setLayer("Cep_Acma")		--Outer pocketing
    G.setThickness(-sunkenDepth2)
    point1 = {-overlap/2,-overlap/2}
    point2 = {a,Y+overlap/2}
    G.rectangle(point1,point2)
    
    point1 = {a-overlap,Y-a}
    point2 = {X-a+overlap,Y+overlap/2}
    G.rectangle(point1,point2)
    
    point1 = {X-a,-overlap/2}
    point2 = {X+overlap/2,Y+overlap/2}
    G.rectangle(point1,point2)
    
    point1 = {a-overlap,-overlap/2}
    point2 = {X-a+overlap,a}
    G.rectangle(point1,point2)
    
    G.setThickness(-finalDepth)
    distance = a+notchWidth+sunkenWidth1+sunkenWidth2+sunkenWidth3+notchWidth+sunkenWidth4
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setLayer("H_Freze10mm_Dis")		--Notches
    G.setThickness(-2*sunkenDepth1)
    distance = a+notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setThickness(-sunkenDepth2)
    distance = a
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setLayer("H_Freze10mm_Ic")
    G.setThickness(-sunkenDepth2)
    distance = a+notchWidth+sunkenWidth1+sunkenWidth2+sunkenWidth3
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setLayer("K_AciliV60")		--First angled surface
    G.setThickness(-2*sunkenDepth1)
    distance = a+notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setLayer("K_AciliV15")		--Second angled surface
    G.setThickness(-sunkenDepth1)
    distance = a+notchWidth+sunkenWidth1
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    G.rectangle(point1,point2)
    
    G.setLayer("K_AciliV45")		--SunkenFrame
    G.setThickness(0)
    distance = a+notchWidth+sunkenWidth1+sunkenWidth2
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    local corner1,corner2 = G.sunkenFrame(point1,point2,sunkenDepth2,vMidAngle,vMidDiameter)
    corner1[3] = 0
    corner2[3] = 0
    
    G.setThickness(-sunkenDepth2)
    distance = a+notchWidth+sunkenWidth1+sunkenWidth2+sunkenWidth3+notchWidth
    point1 = {distance,distance}
    point2 = {X-distance,Y-distance}
    local corner3,corner4 = G.sunkenFrame(point1,point2,sunkenDepth3,vMidAngle,vMidDiameter)
    corner3[3] = 0
    corner4[3] = 0
    
    G.setLayer("K_TarKoseTemizl".."5mm")		--Corner cleaning
    G.setThickness(0)
    G.cleanCorners(corner1,corner2,sunkenDepth2,cThinDiameter)
    G.cleanCorners(corner3,corner4,finalDepth,cThinDiameter)
    
  else
    G.setLayer("Cep_Acma")		--Pocketing
    G.setThickness(0)
    point1 = {-overlap/2,-overlap/2}
    point2 = {X+overlap/2,Y+overlap/2}
    G.rectangle(point1,point2)
  end
  
  return true
end

require "ADekoDebugMode"