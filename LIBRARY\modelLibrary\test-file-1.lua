-- Test file 1 for comparison
function modelMain()
    -- Set up the drawing environment
    ADekoLib.setLayer("LMM0")
    ADekoLib.setThickness(18)
    ADekoLib.setFace(1)
    
    -- Local variables
    local x, y = 100, 100
    local radius = 25.5
    local message = "Hello, <PERSON>!"
    
    -- Basic drawing functions
    ADekoLib.point(x, y)
    ADekoLib.line(x, y, x + 50, y + 50)
    ADekoLib.circle(x, y, radius)
    
    -- Create a rectangle
    ADekoLib.rectangle(x - 10, y - 10, x + 10, y + 10)
    
    print("Drawing completed")
end
