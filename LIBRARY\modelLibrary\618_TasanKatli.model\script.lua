-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  local a = 60                                          -- mm cinsinden kenardan mesafe
  local t = 2                                           -- mm cinsinden çentik derinliği
  local c1 = 3                                          -- mm cinsinden çentik genişliği
  local c2 = 10                                         -- mm cinsinden çentik genişliği
  local d1 = 8                                          -- mm cinsinden derinlik
  local d2 = 3                                          -- mm cinsinden derinlik
  local d3 = 5                                          -- mm cinsinden derinlik
  local g = 30                                          -- mm cinsinden çentik derinliği
  local vToolAngle = 120                                -- derece cinsinden V çakısı açısı
  local vToolDiameter = 60                              -- mm cinsinden V çakısı çapı
  local m1 = d1*math.tan((math.pi*vToolAngle/180)/2.0)  -- mm cinsinden, V çakısı ile oluşturulan açılı yüzey genişliğinin yarısı
  local m2 = d2*math.tan((math.pi*vToolAngle/180)/2.0)  -- mm cinsinden, V çakısı ile oluşturulan açılı yüzey genişliğinin yarısı
  local m3 = d3*math.tan((math.pi*vToolAngle/180)/2.0)  -- mm cinsinden, V çakısı ile oluşturulan açılı yüzey genişliğinin yarısı
  local cToolDiameter1 = 5                              -- mm cinsinden 1. silindirik çakının çapı (1 numaralı yüzeyde ve 3 numaralı yüzey köşelerindeki fazlalıkların temizlenmesinde kullanılıyor)
  local cToolDiameter2 = 30                             -- mm cinsinden 2. silindirik çakının çapı (3 numaralı yüzeyde kullanılıyor)
  local cToolDiameter3 = 20                             -- mm cinsinden 3. silindirik çakının çapı (5 numaralı yüzeyde kullanılıyor)
  local cToolDiameter4 = 35                             -- mm cinsinden 4. silindirik çakının çapı (plakanın küçük boyutlu olması durumunda ortaya pocket yapmak için kullanılıyor)
  
  
  
  --- Profil ---
  --
  --                         1     2       3      4      5     6
  -- _________a_____________                                          ________________
  --                   t  ||__c1__                                 /|| 
  --                           | \                               / |
  --                           |  \                             /  |
  --                        d1 |   \                           /   | d3
  --                           |    \                 ___c2___/    |
  --                           |     \               /|         m3
  --                           |      \             / | d2
  --                           |       \___________/  |
  --                              m1         g      m2
  
  
  G.setLayer("5MM") -- katman adı, 1 numaralı çentiğin açılması
  G.setThickness(-t)
  
  local o = a+cToolDiameter1/2

  local p1 = {o,o}
  local p2 = {X-o,Y-o}
  G.rectangle(p1,p2)
  
  
  G.setLayer("30MM") -- katman adı, 3 numaralı tabanın açılması
  G.setThickness(-(d1+t))
  
  o = a+c1+m1+cToolDiameter2/2
  
  p1 = {o,o}
  p2 = {X-o,Y-o}
  G.rectangle(p1,p2)
  
  G.setLayer("V120") -- katman adı, 2 numaralı açılı yüzeyin kesilmesi
  G.setThickness(-t)
  
  o = a+c1
  
  p1 = {o,o}
  p2 = {X-o,Y-o}
  local corner1, corner2 = G.sunkenFrame(p1,p2,d1,vToolAngle,vToolDiameter)
  
  
  if X>300 and Y>300 then
    G.setLayer("20MM") -- katman adı, 5 numaralı çentiğin açılması
    G.setThickness(-d3)
  
    o = a+c1+m1+g+m2+c2-cToolDiameter3/2
  
    p1 = {o,o}
    p2 = {X-o,Y-o}
    G.rectangle(p1,p2)
  
    G.setLayer("V120") -- katman adı, 4 ve 6 numaralı açılı yüzeylerin kesilmesi
    G.setThickness(-(t+d1))
  
    o = a+c1+m1+g
  
    p1 = {o,o}
    p2 = {X-o,Y-o}
    G.rectangle(p1,p2)
  
    G.setThickness(-d3)
  
    o = a+c1+m1+g+m2+c2
  
    p1 = {o,o}
    p2 = {X-o,Y-o}
    G.rectangle(p1,p2)
  
  else
    G.setLayer("POCKET") -- katman adı, 5 numaralı çentiğin açılması
    G.setThickness(-(t+d1))
    
    o = a+c1+m1+g-cToolDiameter4/2
  
    p1 = {o,o}
    p2 = {X-o,Y-o}
    G.pocket(p1,p2,0,cToolDiameter2/2)

  end
  
  G.setLayer("CLEANCORNERS") -- katman adı, 3 numaralı bölgede köşelerde kalan fazlalıkların temizlenmesi
  
  G.cleanCorners(corner1, corner2, t, cToolDiameter1)
  
  
  
  
  return true
  
end

require "ADekoDebugMode"