-- CNC Door Surface Machining Example
-- This script demonstrates how to use different CNC tools for door surface operations

function modelMain()
  G = AdekoLib
  
  -- Door dimensions
  local doorWidth = 600
  local doorHeight = 800
  local doorThickness = 18
  
  -- Set material thickness
  G.setThickness(-doorThickness)
  
  -- Parse model parameters if available
  if modelParameters then
    if G<PERSON>parseM<PERSON>l<PERSON>arameters(modelParameters) == false then
      return false
    end
  end
  
  -- Create door outline
  G.makePartShape({0, 0}, {doorWidth, 0}, {doorWidth, doorHeight}, {0, doorHeight}, {0, 0})
  
  -- TOP SURFACE OPERATIONS
  G.setFace("top")
  
  -- 1. Roughing operation with 10mm cylindrical end mill
  G.setLayer("TOP_Roughing_10mm")
  G.setThickness(-3) -- 3mm deep roughing pass
  
  -- Create roughing pattern for top surface
  local roughingMargin = 50
  local roughingStepOver = 8
  
  for x = roughingMargin, doorWidth - roughingMargin, roughingStepOver do
    G.line({x, roughingMargin}, {x, doorHeight - roughingMargin})
  end
  
  -- 2. Finishing operation with 6mm cylindrical end mill
  G.setLayer("TOP_Finishing_6mm")
  G.setThickness(-5) -- Final depth 5mm
  
  -- Create decorative pattern on top surface
  local patternCenterX = doorWidth / 2
  local patternCenterY = doorHeight / 2
  local patternRadius = 150
  
  -- Central circular pattern
  G.circle({patternCenterX, patternCenterY}, patternRadius)
  
  -- Corner decorations with ballnose tool
  G.setLayer("TOP_Ballnose_6mm")
  G.setThickness(-2) -- Shallow decorative cuts
  
  local cornerRadius = 30
  local cornerMargin = 80
  
  -- Top-left corner decoration
  G.arc({cornerMargin, doorHeight - cornerMargin}, cornerRadius, 0, 90)
  
  -- Top-right corner decoration  
  G.arc({doorWidth - cornerMargin, doorHeight - cornerMargin}, cornerRadius, 90, 180)
  
  -- Bottom-right corner decoration
  G.arc({doorWidth - cornerMargin, cornerMargin}, cornerRadius, 180, 270)
  
  -- Bottom-left corner decoration
  G.arc({cornerMargin, cornerMargin}, cornerRadius, 270, 360)
  
  -- 3. V-groove operations with conical tool
  G.setLayer("TOP_VGroove_90deg")
  G.setThickness(-3) -- V-groove depth
  
  -- Create decorative V-grooves around the central pattern
  local vGrooveOffset = 200
  G.circle({patternCenterX, patternCenterY}, vGrooveOffset)
  
  -- BOTTOM SURFACE OPERATIONS
  G.setFace("bottom")
  
  -- 1. Roughing operation for bottom surface
  G.setLayer("BOTTOM_Roughing_10mm")
  G.setThickness(-4) -- Deeper roughing for bottom
  
  -- Create roughing pattern for bottom surface
  for y = roughingMargin, doorHeight - roughingMargin, roughingStepOver do
    G.line({roughingMargin, y}, {doorWidth - roughingMargin, y})
  end
  
  -- 2. Pocket operations for hardware mounting
  G.setLayer("BOTTOM_Pocket_Hardware")
  G.setThickness(-8) -- Deep pockets for hardware
  
  -- Hinge pockets (3 hinges)
  local hingeWidth = 80
  local hingeHeight = 100
  local hingeDepth = 3
  local hingeMarginFromEdge = 20
  
  -- Top hinge
  G.rectangle({hingeMarginFromEdge, doorHeight - 150}, 
              {hingeMarginFromEdge + hingeWidth, doorHeight - 150 + hingeHeight})
  
  -- Middle hinge
  G.rectangle({hingeMarginFromEdge, doorHeight/2 - hingeHeight/2}, 
              {hingeMarginFromEdge + hingeWidth, doorHeight/2 + hingeHeight/2})
  
  -- Bottom hinge
  G.rectangle({hingeMarginFromEdge, 50}, 
              {hingeMarginFromEdge + hingeWidth, 50 + hingeHeight})
  
  -- 3. Lock mechanism pocket
  G.setLayer("BOTTOM_Lock_Pocket")
  G.setThickness(-12) -- Deep pocket for lock mechanism
  
  local lockCenterX = doorWidth - 100
  local lockCenterY = doorHeight / 2
  local lockWidth = 60
  local lockHeight = 120
  
  G.rectangle({lockCenterX - lockWidth/2, lockCenterY - lockHeight/2},
              {lockCenterX + lockWidth/2, lockCenterY + lockHeight/2})
  
  -- 4. Finishing with ballnose tool for smooth surfaces
  G.setLayer("BOTTOM_Ballnose_Finish")
  G.setThickness(-1) -- Light finishing pass
  
  -- Create smooth finish pattern around hardware areas
  local finishMargin = 10
  
  -- Around hinge areas
  G.rectangle({hingeMarginFromEdge - finishMargin, doorHeight - 150 - finishMargin}, 
              {hingeMarginFromEdge + hingeWidth + finishMargin, doorHeight - 150 + hingeHeight + finishMargin})
  
  -- Around lock area
  G.rectangle({lockCenterX - lockWidth/2 - finishMargin, lockCenterY - lockHeight/2 - finishMargin},
              {lockCenterX + lockWidth/2 + finishMargin, lockCenterY + lockHeight/2 + finishMargin})
  
  -- 5. Special tool operations for custom features
  G.setLayer("BOTTOM_Special_Dovetail")
  G.setThickness(-6) -- Dovetail joint depth
  
  -- Create dovetail joints for assembly
  local dovetailWidth = 20
  local dovetailSpacing = 100
  
  for x = dovetailSpacing, doorWidth - dovetailSpacing, dovetailSpacing do
    -- Top edge dovetails
    G.rectangle({x - dovetailWidth/2, doorHeight - 20}, 
                {x + dovetailWidth/2, doorHeight})
    
    -- Bottom edge dovetails
    G.rectangle({x - dovetailWidth/2, 0}, 
                {x + dovetailWidth/2, 20})
  end
  
  -- EDGE OPERATIONS (if needed)
  G.setFace("front")
  
  -- Edge profiling with radial tool
  G.setLayer("EDGE_Profile_2mm_Radius")
  G.setThickness(-2) -- Edge rounding depth
  
  -- Round the front edges
  G.line({0, 0}, {doorWidth, 0}) -- Bottom edge
  G.line({doorWidth, 0}, {doorWidth, doorHeight}) -- Right edge
  G.line({doorWidth, doorHeight}, {0, doorHeight}) -- Top edge
  G.line({0, doorHeight}, {0, 0}) -- Left edge
  
  return true
end

-- Tool recommendations for this script:
--
-- TOP SURFACE:
-- - 10mm Cylindrical End Mill (HSS/Carbide) for roughing
-- - 6mm Cylindrical End Mill (Carbide) for finishing
-- - 6mm Ball End Mill (Carbide) for corner decorations
-- - 90° V-Bit (Carbide) for decorative grooves
--
-- BOTTOM SURFACE:
-- - 10mm Cylindrical End Mill for roughing
-- - 6mm Cylindrical End Mill for pocketing
-- - 6mm Ball End Mill for finishing
-- - Dovetail Cutter (14° angle) for joints
--
-- EDGES:
-- - 2mm Corner Radius Tool for edge profiling
--
-- Recommended cutting parameters:
-- - Feed Rate: 800-1200 mm/min for roughing, 600-800 mm/min for finishing
-- - Spindle Speed: 12000-18000 RPM depending on tool and material
-- - Step Down: 2-3mm for roughing, 0.5-1mm for finishing
-- - Step Over: 60-80% of tool diameter
