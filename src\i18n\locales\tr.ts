export default {
  // Uygulama başlığı ve marka
  app: {
    title: 'Adeko Lua Editörü',
    subtitle: 'Adeko CAM Kütüphaneleri için Lua Editörü',
    welcome: 'Adeko Lua Editörüne Hoş Geldiniz',
    welcomeMessage: 'Düzenlemeye başlamak için bir dosya açın veya AdekoCAM fonksiyonlarına göz atın',
    noFileOpen: 'Açık dosya yok'
  },

  // Menü çubuğu
  menu: {
    file: 'Dosya',
    edit: 'D<PERSON><PERSON><PERSON>',
    view: 'Görünüm',
    debug: 'Hata Ayıklama',
    tools: 'Araçlar',
    help: 'Yardım'
  },

  // Dosya menüsü
  fileMenu: {
    new: 'Yeni',
    open: 'Aç',
    save: 'Kaydet',
    saveAs: 'Farklı Kaydet',
    recentFiles: 'Son Dosyalar',
    clearRecentFiles: 'Son Do<PERSON>aları <PERSON>zle',
    exit: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
  },

  // <PERSON><PERSON><PERSON>le menüsü
  editMenu: {
    undo: '<PERSON><PERSON>',
    redo: '<PERSON><PERSON>',
    cut: 'Kes',
    copy: 'Kopyala',
    paste: 'Yapıştır',
    selectAll: 'Tümünü Seç',
    find: 'Bul',
    replace: 'Değiştir',
    findNext: 'Sonrakini Bul',
    findPrevious: 'Öncekini Bul'
  },

  // Görünüm menüsü
  viewMenu: {
    toggleSidebar: 'Kenar Çubuğunu Aç/Kapat',
    toggleFunctionBrowser: 'Fonksiyon Tarayıcısını Aç/Kapat',
    zoomIn: 'Yakınlaştır',
    zoomOut: 'Uzaklaştır',
    resetZoom: 'Yakınlaştırmayı Sıfırla',
    toggleTheme: 'Temayı Değiştir',
    increaseFontSize: 'Yazı Boyutunu Artır',
    decreaseFontSize: 'Yazı Boyutunu Azalt',
    fullscreen: 'Tam Ekranı Aç/Kapat'
  },

  // Hata ayıklama menüsü
  debugMenu: {
    runScript: 'Betiği Çalıştır',
    runWithDebug: 'Hata Ayıklama Modunda Çalıştır',
    stopExecution: 'Çalıştırmayı Durdur',
    clearConsole: 'Konsolu Temizle',
    toggleConsole: 'Hata Ayıklama Konsolunu Aç/Kapat',
    toggleBreakpoint: 'Kesme Noktası Aç/Kapat',
    removeAllBreakpoints: 'Tüm Kesme Noktalarını Kaldır',
    showBreakpoints: 'Kesme Noktalarını Göster'
  },

  // Araçlar menüsü
  toolsMenu: {
    settings: 'Ayarlar',
    functionBrowser: 'Fonksiyon Tarayıcısı',
    validateLua: 'Lua Sözdizimini Doğrula',
    formatCode: 'Kodu Biçimlendir',
    insertFunction: 'Fonksiyon Ekle'
  },

  // Yardım menüsü
  helpMenu: {
    documentation: 'Belgeler',
    keyboardShortcuts: 'Klavye Kısayolları',
    about: 'Hakkında',
    reportIssue: 'Sorun Bildir'
  },

  // Hakkında modal
  about: {
    title: 'Adeko Lua Editörü Hakkında',
    version: 'Sürüm',
    author: 'Yazar',
    authorName: 'Hakan Ak',
    description: 'Adeko CAM kütüphaneleri için özel olarak tasarlanmış gelişmiş IntelliSense, hata ayıklama yetenekleri ve turtle grafik görselleştirmesi ile güçlü bir Lua script editörü.',
    features: 'Temel Özellikler',
    featureList: [
      'Gelişmiş Lua sözdizimi vurgulama ve IntelliSense',
      '200+ fonksiyonlu entegre AdekoLib fonksiyon tarayıcısı',
      'Gerçek zamanlı Lua sözdizimi doğrulama ve hata kontrolü',
      'Turtle grafik görselleştirmesi ile hata ayıklama modu',
      'Çoklu dil desteği (İngilizce/Türkçe)',
      'Proje dosya yönetimi ve organizasyonu',
      'Yerleşik kod biçimlendirme ve doğrulama araçları'
    ],
    copyright: '© 2024 Adeko. Tüm hakları saklıdır.',
    builtWith: 'Geliştirildiği teknolojiler',
    technologies: 'Vue 3, TypeScript, Tauri, Rust, Monaco Editor',
    close: 'Kapat'
  },

  // Lua doğrulama
  luaValidation: {
    validating: 'Lua sözdizimi doğrulanıyor...',
    valid: 'Lua sözdizimi geçerli',
    invalid: 'Lua sözdizimi hataları bulundu',
    noFileOpen: 'Doğrulanacak açık dosya yok',
    errors: 'Sözdizimi Hataları',
    line: 'Satır',
    column: 'Sütun',
    message: 'Mesaj'
  },

  // Araç ipuçları ve IntelliSense
  tooltips: {
    adekoFunction: 'AdekoLib Fonksiyonu',
    luaFunction: 'Lua Standart Fonksiyonu',
    luaKeyword: 'Lua Anahtar Kelimesi',
    parameters: 'Parametreler',
    returns: 'Döndürür',
    example: 'Örnek',
    category: 'Kategori',
    complexity: 'Karmaşıklık',
    seeAlso: 'Ayrıca Bakınız',
    module: 'Modül',
    syntax: 'Sözdizimi',
    description: 'Açıklama'
  },



  // Navigasyon sekmeleri
  tabs: {
    files: 'Dosyalar',
    functions: 'Fonksiyonlar',
    visualization: 'Görselleştirme',
    closeTab: 'Sekmeyi Kapat',
    newTab: 'Yeni Sekme',
    splitEditor: 'Editörü Dikey Böl',
    tabMenu: 'Sekme Menüsü',
    close: 'Kapat',
    closeAll: 'Tüm Sekmeleri Kapat',
    closeOthers: 'Diğer Sekmeleri Kapat',
    closeModified: 'Değiştirilmiş Sekmeleri Kapat',
    copyPath: 'Yolu Kopyala',
    revealInExplorer: 'Dosya Gezgininde Göster'
  },

  // Ayarlar modalı
  settings: {
    title: 'Ayarlar',
    modelLibraryPath: 'Model Kütüphane Yolu',
    selectModelLibraryDirectory: 'Model kütüphane dizinini seçin...',
    currentPaths: 'Mevcut Yollar:',
    model: 'Model:',
    luaAutoDetected: 'Lua (otomatik algılandı):',
    notSet: 'Ayarlanmadı',
    notAvailable: 'Mevcut değil',
    autoDetectionNote: 'Lua kütüphanesi, model kütüphanesinin kardeş klasörü olarak otomatik olarak algılanacaktır.',
    language: 'Dil',
    editorTheme: 'Editör Teması',
    themeNote: 'Kod editörü için bir renk teması seçin',
    sidebarWidth: 'Kenar Çubuğu Genişliği',
    sidebarWidthNote: 'Kenar çubuğu panelinin genişliğini ayarlayın (200-600px). Ayrıca kenar çubuğu kenarını sürükleyerek yeniden boyutlandırabilirsiniz.',
    cancel: 'İptal',
    save: 'Kaydet',
    saving: 'Kaydediliyor...',
    saveError: 'Ayarlar kaydedilemedi. Lütfen tekrar deneyin.'
  },

  // Fonksiyon tarayıcısı
  functions: {
    searchPlaceholder: 'Dosyaları ara...',
    allCategories: 'Tüm Kategoriler',
    allLevels: 'Tüm Seviyeler',
    basic: 'Temel',
    intermediate: 'Orta',
    advanced: 'İleri',
    functionsCount: 'fonksiyon',
    insertFunction: 'Fonksiyon Ekle',
    parameters: 'Parametreler',
    returns: 'Döndürür',
    example: 'Örnek',
    usage: 'Kullanım',
    relatedFunctions: 'İlgili Fonksiyonlar',
    optional: 'isteğe bağlı',
    backToList: 'Fonksiyon Listesine Dön'
  },

  // Fonksiyon kategorileri
  functionCategories: {
    'Geometric Transformations': 'Geometrik Dönüşümler',
    'Point & Vector Operations': 'Nokta ve Vektör İşlemleri',
    'Shape Generation': 'Şekil Oluşturma',
    'Polyline Operations': 'Çok Çizgi İşlemleri',
    'Machining Operations': 'İşleme Operasyonları',
    'Analysis & Testing': 'Analiz ve Test',
    'Utilities': 'Yardımcı Araçlar'
  },

  // Fonksiyon kategori açıklamaları
  functionCategoryDescriptions: {
    'Geometric Transformations': 'Şekilleri döndürme, öteleme, aynalama ve ölçeklendirme fonksiyonları',
    'Point & Vector Operations': 'Nokta hesaplamaları, mesafeler, açılar ve vektör işlemleri fonksiyonları',
    'Shape Generation': 'Temel ve karmaşık geometrik şekiller oluşturma fonksiyonları',
    'Polyline Operations': 'Çok çizgi ve yolları oluşturma, değiştirme ve manipüle etme fonksiyonları',
    'Machining Operations': 'Delik, oluk ve cep gibi işleme operasyonları oluşturma fonksiyonları',
    'Analysis & Testing': 'Geometrik analiz, çarpışma tespiti ve doğrulama fonksiyonları',
    'Utilities': 'Sıralama, düzenleme ve veri manipülasyonu için yardımcı fonksiyonlar'
  },

  // Dosya işlemleri
  files: {
    luaFiles: 'Lua Dosyaları',
    newFileComment: '-- Yeni Lua dosyası\n-- Kodunuzu buraya ekleyin\n\n'
  },

  // Dosya Gezgini Bağlam Menüsü
  fileExplorer: {
    browseFolder: 'Klasör Gözat',
    navigateUp: 'Yukarı Git',
    newFile: 'Yeni Dosya',
    newFolder: 'Yeni Klasör',
    rename: 'Yeniden Adlandır',
    delete: 'Sil',
    copy: 'Kopyala',
    cut: 'Kes',
    paste: 'Yapıştır',
    refresh: 'Yenile',
    openInExplorer: 'Dosya Gezgininde Aç',
    navigateTo: 'Klasöre Git',
    properties: 'Özellikler',
    confirmDelete: '"{name}" dosyasını silmek istediğinizden emin misiniz?',
    confirmDeleteMultiple: '{count} öğeyi silmek istediğinizden emin misiniz?',
    enterFileName: 'Dosya adını girin:',
    enterFolderName: 'Klasör adını girin:',
    enterNewName: 'Yeni adı girin:',
    fileExists: 'Bu adda bir dosya zaten mevcut',
    folderExists: 'Bu adda bir klasör zaten mevcut',
    invalidName: 'Geçersiz dosya/klasör adı',
    createFileError: 'Dosya oluşturulamadı',
    createFolderError: 'Klasör oluşturulamadı',
    deleteError: 'Silinemedi',
    renameError: 'Yeniden adlandırılamadı',
    copyError: 'Kopyalanamadı',
    pasteError: 'Yapıştırılamadı',
    refreshError: 'Dizin yenilenemedi',
    noItemsSelected: 'Hiçbir öğe seçilmedi',
    cannotCopyToSelf: 'Öğe kendisine kopyalanamaz',
    cannotMoveToSelf: 'Öğe kendisine taşınamaz',
    expandFolder: 'Klasörü genişlet',
    collapseFolder: 'Klasörü daralt',
    loadingFolder: 'Klasör içeriği yükleniyor...'
  },

  // Hata mesajları
  errors: {
    openingFile: 'Dosya açılırken hata:',
    savingFile: 'Dosya kaydedilirken hata:',
    loadingFile: 'Dosya yüklenirken hata:',
    loadingSettings: 'Ayarlar yüklenirken hata:',
    calculatingLuaPath: 'Lua kütüphane yolu hesaplanırken hata:',
    selectingModelLibraryPath: 'Model kütüphane yolu seçilirken hata:'
  },

  // Durum mesajları
  status: {
    modified: '●',
    luaMacroEditor: 'Adeko Lua Editörü',
    initialized: 'Adeko Lua Editörü başlatıldı',
    directoryNotExist: 'Lua kütüphane dizini mevcut değil:',
    line: 'Satır',
    column: 'Sütun'
  },

  // Diller
  languages: {
    en: 'English',
    tr: 'Türkçe'
  },

  // Hata ayıklama konsolu
  debugConsole: {
    title: 'Hata Ayıklama Konsolu',
    running: 'Betik çalıştırılıyor...',
    completed: 'Betik çalıştırma tamamlandı',
    failed: 'Betik çalıştırma başarısız',
    stopped: 'Betik çalıştırma durduruldu',
    executionTime: 'Çalıştırma süresi: {time}ms',
    noOutput: 'Çıktı yok',
    clearConsole: 'Konsolu Temizle',
    copyOutput: 'Çıktıyı Kopyala',
    saveOutput: 'Çıktıyı Dosyaya Kaydet',
    toggleTurtleGraphics: 'Grafikleri Aç/Kapat',
    debugMode: 'Hata Ayıklama Modu',
    faceLayout: '6-Yüz Düzeni Aktif',
    drawCommands: 'çizim komutu yakalandı'
  },

  // Turtle Canvas
  turtleCanvas: {
    title: '2D Grafikler',
    commands: 'komut',
    resetView: 'Görünümü Sıfırla',
    zoomIn: 'Yakınlaştır',
    zoomOut: 'Uzaklaştır',
    minimize: 'Küçült'
  },

  // Görselleştirme Paneli
  visualization: {
    title: 'Görselleştirme',
    commands: 'komut',
    clear: 'Görselleştirmeyi Temizle',
    expand: 'Görselleştirmeyi Genişlet',
    collapse: 'Görselleştirmeyi Daralt',
    noData: 'Görselleştirme verisi yok',
    runScriptHint: 'Turtle grafiklerini görmek için hata ayıklama modunda bir betik çalıştırın',
    turtleGraphicsMinimized: 'Turtle Grafikleri (Küçültülmüş)',
    restore: 'Geri Yükle',
    exportSVG: 'SVG Dışa Aktar',
    exportDXF: 'DXF Dışa Aktar'
  },

  // Three.js Canvas
  threeCanvas: {
    csgOperations: 'CSG İşlemleri',
    union: 'Birleştir',
    subtract: 'Çıkar',
    intersect: 'Kesişim',
    resetView: 'Görünümü Sıfırla',
    fitToView: 'Görünüme Sığdır',
    wireframe: 'Tel Kafes',
    orthographic: 'Ortografik Görünüm',
    frontView: 'Ön Görünüm',
    backView: 'Arka Görünüm',
    topView: 'Üst Görünüm',
    rightView: 'Sağ Görünüm',
    isometricView: 'İzometrik Görünüm',
    rotate: 'Döndür',
    zoom: 'Yakınlaştır',
    pan: 'Kaydır',
    leftClick: 'Sol Tık + Sürükle',
    mouseWheel: 'Fare Tekerleği',
    rightClick: 'Sağ Tık + Sürükle',
    layers: 'Katmanlar',
    addLayer: 'Katman Ekle',
    removeLayer: 'Katmanı Kaldır'
  },

  // Araç çubuğu
  toolbar: {
    fileOpen: 'Dosya Açık',
    noFile: 'Dosya Yok',
    new: 'Yeni',
    open: 'Aç',
    save: 'Kaydet',
    saveAs: 'Farklı Kaydet',
    settings: 'Ayarlar',
    help: 'Yardım'
  },

  // Ortak UI öğeleri
  common: {
    loading: 'Yükleniyor...',
    error: 'Hata',
    directoryNotFound: 'Dizin bulunamadı:',
    noDirectorySpecified: 'Dizin belirtilmedi',
    close: 'Kapat',
    dismiss: 'Kapat'
  },

  // Katman yönetimi
  layers: {
    title: 'Katmanlar',
    togglePanel: 'Katman panelini aç/kapat',
    show: 'Katmanı göster',
    hide: 'Katmanı gizle',
    showAll: 'Tüm katmanları göster',
    hideAll: 'Tüm katmanları gizle',
    commands: 'komut',
    noLayers: 'Katman bulunamadı',
    doorPanel: 'Kapı Paneli',
    topFace: 'Üst',
    bottomFace: 'Alt',
    categories: {
      structural: 'Yapısal',
      cutting: 'Kesim',
      vbit: 'V-Bıçak',
      ballnose: 'Top Uçlu',
      radial: 'Radyal',
      special: 'Özel',
      drilling: 'Delme',
      tool: 'Takım',
      other: 'Diğer'
    }
  },

  // OpenCascade.js 3D Görselleştirme
  ocjs: {
    processing: 'OpenCascade.js ile işleniyor...',
    error: 'OC.js İşleme Hatası',
    retry: 'Tekrar Dene',
    download: 'GLB Model İndir',
    resetCamera: 'Kamerayı Sıfırla',
    toggleRotation: 'Otomatik Döndürmeyi Aç/Kapat',
    noModel: '3D Model Yok',
    noModelDescription: '3D model oluşturmak için kapı komutlarını OpenCascade.js ile işleyin',
    steps: {
      parsing: 'Katmanlar ayrıştırılıyor...',
      extracting: 'Kapı parametreleri çıkarılıyor...',
      processing: 'OpenCascade.js ile işleniyor...',
      creating: '3D model oluşturuluyor...'
    }
  },

  // Klavye kısayolları
  keyboardShortcuts: {
    title: 'Klavye Kısayolları',
    categories: {
      file: 'Dosya İşlemleri',
      edit: 'Düzenleme İşlemleri',
      view: 'Görünüm İşlemleri',
      tools: 'Araçlar',
      help: 'Yardım'
    },
    tips: {
      title: 'İpuçları',
      editorFocus: 'Çoğu kısayol editör odaklandığında çalışır',
      globalShortcuts: 'Fonksiyon tuşları (F1, F2, F7) global olarak çalışır',
      menuAccess: 'Bu komutlara menü çubuğundan da erişebilirsiniz'
    }
  },

  // Editör
  editor: {
    noFileOpen: 'Açık dosya yok',
    openFileHint: 'Gezginden bir dosya açın veya yeni bir tane oluşturun',
    newFile: 'Yeni Dosya'
  },

  // Takım Şekli Görselleştiricisi
  toolVisualizer: {
    title: 'Takım Şekilleri',
    description: 'CNC takım şekillerini görselleştirin ve süpürme işlemleri için BRep oluşturun',
    viewMode: 'Görünüm Modu',
    gallery: 'Galeri',
    comparison: 'Karşılaştırma',
    single: 'Tek Takım',
    toolHeight: 'Takım Yüksekliği',
    generateBReps: 'BRep Oluştur',
    generating: 'Oluşturuluyor...',
    exportGLBs: 'GLB Dışa Aktar',
    exporting: 'Dışa aktarılıyor...',
    error: 'Hata',
    brepReady: 'BRep Hazır',
    brepError: 'BRep Hatası',
    resetView: 'Görünümü Sıfırla',
    wireframe: 'Tel Kafes',
    autoRotate: 'Otomatik Döndür',
    specifications: 'Özellikler',
    shape: 'Şekil',
    diameter: 'Çap',
    length: 'Uzunluk',
    tipAngle: 'Uç Açısı',
    ballRadius: 'Top Yarıçapı',
    cornerRadius: 'Köşe Yarıçapı',
    brepInfo: 'BRep Bilgisi',
    brepGenerated: 'BRep başarıyla oluşturuldu',
    brepFailed: 'BRep oluşturma başarısız',
    downloadGLB: 'GLB İndir',
    operations: 'İşlemler',
    generateBRep: 'BRep Oluştur',
    exportGLB: 'GLB Dışa Aktar',
    tool1: 'Takım 1',
    tool2: 'Takım 2',
    selectTool: 'Bir takım seçin...',
    brepResults: 'BRep Oluşturma Sonuçları',
    successful: 'başarılı',
    toolCount: '{count} takım mevcut'
  },

  // CNC Takımları
  cncTools: {
    title: 'CNC Takımları',
    addTool: 'Takım Ekle',
    editTool: 'Takımı Düzenle',
    deleteTool: 'Takımı Sil',
    profiles: 'Profiller',
    machiningProfiles: 'İşleme Profilleri',
    filterByShape: 'Şekle Göre Filtrele',
    allShapes: 'Tüm Şekiller',
    cylindrical: 'Silindirik',
    conical: 'Konik',
    ballnose: 'Küre Uçlu',
    radial: 'Köşe Radyüslü',
    special: 'Özel',
    toolDetails: 'Takım Detayları',
    diameter: 'Çap',
    length: 'Uzunluk',
    material: 'Malzeme',
    coating: 'Kaplama',
    tipAngle: 'Uç Açısı',
    ballRadius: 'Küre Radyüsü',
    cornerRadius: 'Köşe Radyüsü',
    assignOperation: 'İşlem Ata',
    selectOperation: 'İşlem Seç',
    selectSurface: 'Yüzey Seç',
    roughing: 'Kaba İşleme',
    finishing: 'Son İşleme',
    profiling: 'Profil İşleme',
    drilling: 'Delme',
    pocketing: 'Cep Açma',
    topSurface: 'Üst Yüzey',
    bottomSurface: 'Alt Yüzey',
    bothSurfaces: 'Her İki Yüzey',
    assign: 'Ata',
    topOperations: 'Üst İşlemler',
    bottomOperations: 'Alt İşlemler',
    feedRate: 'İlerleme Hızı',
    spindleSpeed: 'Mil Hızı',
    stepDown: 'Adım Derinliği',
    stepOver: 'Adım Genişliği',
    depth: 'Derinlik',
    flutes: 'Ağız Sayısı',
    helixAngle: 'Helis Açısı',
    customParameters: 'Özel Parametreler',
    specialType: 'Özel Tip',
    manufacturer: 'Üretici',
    partNumber: 'Parça Numarası',
    units: 'Birimler',
    metric: 'Metrik',
    imperial: 'İnç',
    toolLibrary: 'Takım Kütüphanesi',
    newTool: 'Yeni Takım',
    saveTool: 'Takımı Kaydet',
    cancelTool: 'İptal',
    toolName: 'Takım Adı',
    toolDescription: 'Açıklama',
    requiredField: 'Bu alan zorunludur',
    invalidValue: 'Geçersiz değer',
    toolSaved: 'Takım başarıyla kaydedildi',
    toolDeleted: 'Takım başarıyla silindi',
    confirmDeleteTool: 'Bu takımı silmek istediğinizden emin misiniz?',
    operationAssigned: 'İşlem başarıyla atandı',
    noToolSelected: 'Takım seçilmedi',
    noOperationSelected: 'İşlem seçilmedi',
    noSurfaceSelected: 'Yüzey seçilmedi'
  },

  // Otomatik Takım Tespiti
  autoTool: {
    title: 'Otomatik Takım Tespiti',
    description: 'Katman adlandırma kurallarına göre uygun CNC takımlarını otomatik olarak tespit eder',
    testDetection: 'Katman Analizi Testi',
    enterLayerName: 'Katman adını girin (örn: K_Freze10mm)',
    analyze: 'Analiz Et',
    analysisResults: 'Analiz Sonuçları',
    detectedTool: 'Tespit Edilen Takım',
    noToolDetected: 'Uygun takım bulunamadı',
    nonMachinable: 'İşlenemeyen katman (LUA/LMM)',
    operation: 'İşlem',
    parameters: 'Parametreler',
    cuttingParameters: 'Önerilen Kesme Parametreleri',
    topSurface: 'Üst',
    bottomSurface: 'Alt',
    noneSurface: 'Yok',
    contourType: 'Kontur Tipi',
    inner: 'İç',
    outer: 'Dış',
    namingConventions: 'Katman Adlandırma Kuralları',
    kanal: 'Kanal',
    kanalDescription: 'Merkezden dışa doğru kanal işlemleri',
    contour: 'Kontur',
    contourDescription: 'Kontur işlemleri (DIS=dış, IC=iç)',
    vTool: 'V-Takım',
    vToolDescription: 'V-kanal için konik takım işlemleri',
    suffixes: 'Yüzey Ekleri',
    suffixDescription: 'Yüzey ve yön belirteçleri',
    nonMachinableLayers: 'İşlenemeyen Katmanlar',
    nonMachinableDescription: 'Bu katmanlar takım tespitinden hariç tutulur',
    commonExamples: 'Yaygın Katman Örnekleri (Test için tıklayın)',
    detectedTools: 'Tespit Edilen Takımlar',
    virtual: 'Sanal'
  }
}
