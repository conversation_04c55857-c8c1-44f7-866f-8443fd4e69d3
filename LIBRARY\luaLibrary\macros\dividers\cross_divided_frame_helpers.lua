-- Cross Divided Frame Helper Functions for Door Models
-- This file provides additional utilities and examples for using the cross_divided_frame macro

-- Core cross divided frame function
function cross_divided_frame(A, B, C, Z)
  -- Cross Divided Frame Macro for AdekoLib
  -- Creates a cross-divided frame with two separate polyline paths
  -- Parameters: A (margin), B (gap), C (width), Z (thickness)

  local G = AdekoLib

  -- Set thickness for machining operation
  if Z and Z ~= 0 then
    G.setThickness(Z)
  end

  -- Get current part dimensions (assuming X and Y are available globally)
  local frameWidth = X or 200   -- fallback to 200 if X not available
  local frameHeight = Y or 150  -- fallback to 150 if Y not available

  -- Use default values if parameters not provided
  A = A or 10
  B = B or 5
  C = C or 15
  Z = Z or -3

  -- Create first frame part (left/bottom section)
  local p1 = {A, A}
  local p2 = {frameWidth - A, A}
  local p3 = {frameWidth - A, A + C}
  local p4 = {A, frameHeight - A - C - B}
  local p5 = {A, A}  -- close the path

  -- Create the first polyline
  G.polyline(p1, p2, p3, p4, p5)

  -- Start a new shape for the second frame part
  G.nextShape()

  -- Create second frame part (right/top section)
  local q1 = {frameWidth - A, A + C + B}
  local q2 = {frameWidth - A, frameHeight - A}
  local q3 = {A, frameHeight - A}
  local q4 = {A, frameHeight - A - C}
  local q5 = {frameWidth - A, A + C + B}  -- close the path

  -- Create the second polyline
  G.polyline(q1, q2, q3, q4, q5)

  return true
end

-- Helper function to create cross divided frame with layer management
function createCrossDividedFrameWithLayer(A, B, C, Z, layerName, face)
  local G = AdekoLib
  
  -- Store current layer and face
  local currentLayer = G.getCurrentLayer and G.getCurrentLayer() or nil
  local currentFace = G.getCurrentFace and G.getCurrentFace() or nil
  
  -- Set the specified layer and face
  if layerName then
    G.setLayer(layerName)
  end
  
  if face then
    G.setFace(face)
  end
  
  -- Create the frame
  local result = cross_divided_frame(A, B, C, Z)
  
  -- Restore previous layer and face if they were captured
  if currentLayer then
    G.setLayer(currentLayer)
  end
  
  if currentFace then
    G.setFace(currentFace)
  end
  
  return result
end

-- Function to create multiple cross divided frames with different parameters
function createMultipleCrossDividedFrames(frameConfigs)
  local G = AdekoLib
  local results = {}
  
  for i, config in ipairs(frameConfigs) do
    -- Extract configuration
    local A = config.A or config.margin or 10
    local B = config.B or config.gap or 5
    local C = config.C or config.width or 15
    local Z = config.Z or config.thickness or -3
    local layerName = config.layer
    local face = config.face or "top"
    
    -- Create the frame
    local result = createCrossDividedFrameWithLayer(A, B, C, Z, layerName, face)
    table.insert(results, result)
  end
  
  return results
end

-- Function to create cross divided frame with automatic sizing based on door dimensions
function createDoorCrossDividedFrame(doorWidth, doorHeight, frameSpec)
  local G = AdekoLib
  
  -- Calculate frame parameters based on door size
  local A = frameSpec.marginRatio and (math.min(doorWidth, doorHeight) * frameSpec.marginRatio) or frameSpec.A or 10
  local B = frameSpec.gapRatio and (math.min(doorWidth, doorHeight) * frameSpec.gapRatio) or frameSpec.B or 5
  local C = frameSpec.widthRatio and (math.min(doorWidth, doorHeight) * frameSpec.widthRatio) or frameSpec.C or 15
  local Z = frameSpec.Z or frameSpec.thickness or -3
  
  -- Ensure minimum and maximum values
  A = math.max(frameSpec.minMargin or 5, math.min(frameSpec.maxMargin or 50, A))
  B = math.max(frameSpec.minGap or 2, math.min(frameSpec.maxGap or 20, B))
  C = math.max(frameSpec.minWidth or 5, math.min(frameSpec.maxWidth or 30, C))
  
  -- Set global dimensions for the macro
  X = doorWidth
  Y = doorHeight
  
  return cross_divided_frame(A, B, C, Z)
end

-- Example usage functions for different door types

-- Standard door with cross divided frame
function standardDoorCrossDividedFrame()
  local frameSpec = {
    A = 15,           -- 15mm margin
    B = 8,            -- 8mm gap
    C = 20,           -- 20mm frame width
    Z = -4            -- 4mm depth
  }
  
  return createDoorCrossDividedFrame(X or 600, Y or 800, frameSpec)
end

-- Small door with proportional frame
function smallDoorCrossDividedFrame()
  local frameSpec = {
    marginRatio = 0.05,   -- 5% of door size for margin
    gapRatio = 0.02,      -- 2% of door size for gap
    widthRatio = 0.04,    -- 4% of door size for frame width
    Z = -3,               -- 3mm depth
    minMargin = 8,        -- minimum 8mm margin
    maxMargin = 25,       -- maximum 25mm margin
    minGap = 3,           -- minimum 3mm gap
    maxGap = 12,          -- maximum 12mm gap
    minWidth = 8,         -- minimum 8mm width
    maxWidth = 20         -- maximum 20mm width
  }
  
  return createDoorCrossDividedFrame(X or 400, Y or 500, frameSpec)
end

-- Large door with fixed frame
function largeDoorCrossDividedFrame()
  local frameSpec = {
    A = 25,           -- 25mm margin
    B = 12,           -- 12mm gap
    C = 30,           -- 30mm frame width
    Z = -5            -- 5mm depth
  }
  
  return createDoorCrossDividedFrame(X or 800, Y or 1200, frameSpec)
end

-- Function to create cross divided frame with validation
function createValidatedCrossDividedFrame(A, B, C, Z)
  -- Validate parameters
  if not A or A < 0 then
    print("Warning: Invalid margin (A) parameter, using default value 10")
    A = 10
  end
  
  if not B or B < 0 then
    print("Warning: Invalid gap (B) parameter, using default value 5")
    B = 5
  end
  
  if not C or C <= 0 then
    print("Warning: Invalid width (C) parameter, using default value 15")
    C = 15
  end
  
  if not Z or Z == 0 then
    print("Warning: Invalid thickness (Z) parameter, using default value -3")
    Z = -3
  end
  
  -- Check if frame fits within door dimensions
  local doorWidth = X or 200
  local doorHeight = Y or 150
  
  local minDoorWidth = 2 * A + C + B
  local minDoorHeight = 2 * A + 2 * C + B
  
  if doorWidth < minDoorWidth or doorHeight < minDoorHeight then
    print(string.format("Warning: Door dimensions (%dx%d) may be too small for frame parameters", doorWidth, doorHeight))
    print(string.format("Minimum required dimensions: %dx%d", minDoorWidth, minDoorHeight))
  end
  
  return cross_divided_frame(A, B, C, Z)
end

-- Utility function to preview frame dimensions
function previewCrossDividedFrameDimensions(A, B, C, doorWidth, doorHeight)
  doorWidth = doorWidth or X or 200
  doorHeight = doorHeight or Y or 150

  print("Cross Divided Frame Preview:")
  print(string.format("Door dimensions: %dx%d", doorWidth, doorHeight))
  print(string.format("Parameters: A=%d, B=%d, C=%d", A, B, C))
  print("")
  print("Frame 1 (left/bottom section):")
  print(string.format("  Point 1: (%d, %d)", A, A))
  print(string.format("  Point 2: (%d, %d)", doorWidth - A, A))
  print(string.format("  Point 3: (%d, %d)", doorWidth - A, A + C))
  print(string.format("  Point 4: (%d, %d)", A, doorHeight - A - C - B))
  print("")
  print("Frame 2 (right/top section):")
  print(string.format("  Point 1: (%d, %d)", doorWidth - A, A + C + B))
  print(string.format("  Point 2: (%d, %d)", doorWidth - A, doorHeight - A))
  print(string.format("  Point 3: (%d, %d)", A, doorHeight - A))
  print(string.format("  Point 4: (%d, %d)", A, doorHeight - A - C))
end

-- Note: This is a helper library file, not a standalone model script.
-- For a working test model, use: LIBRARY/modelLibrary/CrossDividedFrameTest.model/script.lua

-- Simple preview function that doesn't require AdekoLib
function previewCrossDividedFrameTest()
  local X, Y = 300, 200
  local A, B, C = 15, 8, 20

  print("=== Cross Divided Frame Preview ===")
  print(string.format("Door dimensions: %dx%d mm", X, Y))
  print(string.format("Parameters: A=%d, B=%d, C=%d", A, B, C))
  print("")
  print("Frame 1 (left/bottom section) points:")
  print(string.format("  P1: (%d, %d)", A, A))
  print(string.format("  P2: (%d, %d)", X - A, A))
  print(string.format("  P3: (%d, %d)", X - A, A + C))
  print(string.format("  P4: (%d, %d)", A, Y - A - C - B))
  print(string.format("  P5: (%d, %d) [close]", A, A))
  print("")
  print("Frame 2 (right/top section) points:")
  print(string.format("  Q1: (%d, %d)", X - A, A + C + B))
  print(string.format("  Q2: (%d, %d)", X - A, Y - A))
  print(string.format("  Q3: (%d, %d)", A, Y - A))
  print(string.format("  Q4: (%d, %d)", A, Y - A - C))
  print(string.format("  Q5: (%d, %d) [close]", X - A, A + C + B))
  print("")
  print("To see the actual 2D model, run:")
  print("LIBRARY/modelLibrary/CrossDividedFrameTest.model/script.lua")
end

-- Call the preview function when this file is loaded
previewCrossDividedFrameTest()

return {
  cross_divided_frame = cross_divided_frame,
  createCrossDividedFrameWithLayer = createCrossDividedFrameWithLayer,
  createMultipleCrossDividedFrames = createMultipleCrossDividedFrames,
  createDoorCrossDividedFrame = createDoorCrossDividedFrame,
  standardDoorCrossDividedFrame = standardDoorCrossDividedFrame,
  smallDoorCrossDividedFrame = smallDoorCrossDividedFrame,
  largeDoorCrossDividedFrame = largeDoorCrossDividedFrame,
  createValidatedCrossDividedFrame = createValidatedCrossDividedFrame,
  previewCrossDividedFrameDimensions = previewCrossDividedFrameDimensions,
  previewCrossDividedFrameTest = previewCrossDividedFrameTest
}
