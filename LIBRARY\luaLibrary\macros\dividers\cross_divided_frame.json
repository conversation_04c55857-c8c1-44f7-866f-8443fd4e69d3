{"parameters": [{"defaultValue": 10, "description": "margin from the edges (A parameter)", "parameterName": "A"}, {"defaultValue": 5, "description": "gap between frame parts (B parameter)", "parameterName": "B"}, {"defaultValue": 15, "description": "width of the frame (C parameter)", "parameterName": "C"}, {"defaultValue": -3, "description": "thickness/depth for machining (Z parameter)", "parameterName": "Z"}], "functionDef:": "cross_divided_frame(A, B, C, Z)", "functionCode": "function cross_divided_frame(A, B, C, Z)\n  -- Cross Divided <PERSON><PERSON> for AdekoLib\n  -- Creates a cross-divided frame with two separate polyline paths\n  -- Parameters: A (margin), B (gap), C (width), Z (thickness)\n  \n  local G = AdekoLib\n  \n  -- Set thickness for machining operation\n  if Z and Z ~= 0 then\n    G.setThickness(Z)\n  end\n  \n  -- Get current part dimensions (assuming X and Y are available globally)\n  local frameWidth = X or 200   -- fallback to 200 if X not available\n  local frameHeight = Y or 150  -- fallback to 150 if Y not available\n  \n  -- Use default values if parameters not provided\n  A = A or 10\n  B = B or 5\n  C = C or 15\n  Z = Z or -3\n  \n  -- Create first frame part (left/bottom section)\n  local p1 = {A, A}\n  local p2 = {frameWidth - A, A}\n  local p3 = {frameWidth - A, A + C}\n  local p4 = {A, frameHeight - A - C - B}\n  local p5 = {A, A}  -- close the path\n  \n  -- Create the first polyline\n  G.polyline(p1, p2, p3, p4, p5)\n  \n  -- Start a new shape for the second frame part\n  G.nextShape()\n  \n  -- Create second frame part (right/top section)\n  local q1 = {frameWidth - A, A + C + B}\n  local q2 = {frameWidth - A, frameHeight - A}\n  local q3 = {A, frameHeight - A}\n  local q4 = {A, frameHeight - A - C}\n  local q5 = {frameWidth - A, A + C + B}  -- close the path\n  \n  -- Create the second polyline\n  G.polyline(q1, q2, q3, q4, q5)\n  \n  return true\nend", "description": "Creates a cross divided frame with two separate polyline paths, equivalent to the azCAM C# macro", "category": "<PERSON>ame", "subcategory": "Dividers", "complexity": "intermediate", "usage": "Use to create a cross divided frame for door panels with customizable margins, gaps, and frame width", "tags": ["frame", "divider", "cross", "polyline", "door"], "version": 2}