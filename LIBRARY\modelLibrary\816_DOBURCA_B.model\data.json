{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Acili_aV \t\tAcili V bicagi \nK_Desen\t\tDesen-motif bicagi \nH_Freze_cT_mm_Ic\t\tcT Capli Freze bicagi \nK_Freze_cT_mm\t\tcT Capli Freze bicagi \nCep_Acma\t\tCam yeri tarama bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar kapakda acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "<PERSON><PERSON><PERSON> k<PERSON>inin y<PERSON>", "parameterName": "yy"}, {"defaultValue": 50, "description": "Ust duz kenar uzunlugu", "parameterName": "m"}, {"defaultValue": 8, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 10, "description": "Cam yeri fatura genisligi", "parameterName": "fatura"}, {"defaultValue": 14, "description": "mm, window depth from top", "parameterName": "windowDepthFront"}, {"defaultValue": 4, "description": "mm, window depth from bottom", "parameterName": "windowDepthBack"}, {"defaultValue": 2, "description": "Vbit Ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 1, "description": "Ic kenar ustu kanal kenar uzatma islemi var mi?(Var :1/ Yok:0)", "parameterName": "topGrvExtExist"}, {"defaultValue": 5, "description": "Gobek desen bicak islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "shapeToolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}