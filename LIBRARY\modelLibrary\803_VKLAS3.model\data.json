{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nH_Freze10mm_Dis\t10mm Freze bicagi \nK_Freze10mm\t\t10mm Freze bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar kapakda acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 25, "description": "Acili V kenardan gobege mesafe", "parameterName": "h"}, {"defaultValue": 20, "description": "<PERSON><PERSON> kisminin yuk<PERSON>", "parameterName": "yy"}, {"defaultValue": 50, "description": "<PERSON><PERSON><PERSON> a<PERSON>i me<PERSON>", "parameterName": "gaps"}, {"defaultValue": 20, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 6, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 6, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeRtoolExist"}, {"defaultValue": 2, "description": "Ic Dik kanallar var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intVertGrvExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}