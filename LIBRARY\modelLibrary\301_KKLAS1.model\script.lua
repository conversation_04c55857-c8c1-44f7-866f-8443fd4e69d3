-- ADekoCAM, Model Script
--emrah_hata
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
 	minimum = 150
	limit = 350

  a = 50                    -- Kenardan Vbite
  aa = 30					-- Dar kapak için Kenardan mesafe
  b = 15                    -- Vbitten Dıs kanala
  h = 25                    -- Vbitten göbeğe
  ad = 4                    -- Vbit derinliği (sunkenDepth)
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 6                   -- <PERSON>nce bıçak çapı (Köşe Temizleme)
    
  extGrooveExist          = 1.5   -- Dış kanal var mı? derinlik/0:yok
  edgeCornerRExist          	= 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
    
  --bd = 3                    -- dış kanal derinliği
  --local hd = 5              -- <PERSON><PERSON><PERSON> Desen Bıcak derinliği
  --local dd = 2              -- iç kanal derinliği
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
	local xMin = minimum
	local yMin = minimum
	--local xLimit = limit
	--local yLimit = limit
  
  
  local d = 35              -- Göbekten iç kanala
  local aV = 120                  -- Vbit Açısı
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
  local extEdgeVtoolExist             = 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
  local cabCoreExist            = true   -- Göbek var mı?
  local intGrooveExist        	= 0   -- iç kanal var mı? derinlik/ 0:yok
  -- local shapeToolExist        	= 0   -- Göbek Desen bıçağı var mı? derinlik/0:yok
  local D = edgeCornerRExist
    
  local sunkenDepth = ad
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)     --indiği derinlikte yarısı oluyor
      
  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end

  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  minXCalculated = 2*a + 2*sunkenWidth + 2*h
  minYCalculated = 2*ust + 2*sunkenWidth + 2*h

  if cW <= 0 then
    cW = cT
  end
  
  if X<minXCalculated or Y<minYCalculated then
    print("Part dimension too small")
    return true
  end
  
  if extEdgeVtoolExist > 0 then
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if extGrooveExist > 0 then
    G.setLayer("K_Freze"..cT.."mm")  -- DEEP cleanup
    G.setThickness(-extGrooveExist)
    distance = a-b
    distance2 = ust-b
    point1 = {distance, distance2}
    point2 = {X-distance, Y-distance2}
    G.rectangle(point1,point2)
  end
  
	if cabCoreExist then
  	  
		G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
		G.setThickness(-ad)
		distance = a + h
		distance2 = ust + h
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	
		G.setLayer("K_Freze"..cW.."mm")  -- DEEP cleanup
		G.setThickness(-ad)
		distance1 = a+cW/2
		distance2 = ust+cW/2
		point1 = {distance1, distance2}
		point2 = {X-distance1, Y-distance2}
		G.rectangle(point1,point2)
	
	
		if h<cT or h<cW then
			print("Tool too large")
			return true
		end
	
		if h>cW then
			distance = a+h-cW/2
			distance2 = ust+h-cW/2
			point3 = {distance, distance2}
			point4 = {X-distance, Y-distance2}
			--G.rectangle(point3,point4)
			k = (h-cW)/(cW/2)
			for i=1, k, 1 do
				point1 = G.ptAdd(point1,{cW/2,cW/2})
				point2 = G.ptSubtract(point2, {cW/2,cW/2})
				if point1[1]>point3[1]-cW/2 then
					break
				end
			G.rectangle(point1,point2)
			end
		end
	
	
		if h ~= cW then
			distance = a+h-cW/2
			distance2 = ust+h-cW/2
			point10 = {distance, distance2}
			point11 = {X-distance, Y-distance2}
			G.rectangle(point10,point11)
		end
	
		if intGrooveExist > 0 then      ------İç kanal varsa
			local checkX = 2*a + 2*h + 2*d
			local checkY = 2*ust + 2*h + 2*d
			if X < checkX or Y < checkY then
				print("Part dimension too small, check a + h + d value")
				return true
			end
			G.setLayer("K_Freze"..cT.."mm")  -- DEEP cleanup
			G.setThickness(-intGrooveExist)
			distance = a + h + d
			distance2 = ust + h + d
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)
		end
	
		-- else ------------- bu araya 250 den küçükler için tarama gelebilir
		-- G.setLayer("Cep_Acma".. cW .. "mm")  -- DEEP cleanup
		-- G.setThickness(-ad)
		-- distance = a
		-- distance2 = ust
		-- point6 = {distance, distance2}
		-- point7 = {X-distance, Y-distance2}
		-- G.rectangle(point6,point7)
		-- end
		
		G.setLayer("K_TarKoseTemizl"..cT.."mm")
	
		if cW ~= cT then
			if h >= 3*cT then
				G.setThickness(0)
				distance = a
				distance2 = ust
				G.cleanCorners({distance, distance2,0},{X-distance, Y-distance2,0},ad,cT)
			else
				G.setThickness(-ad)
				distance = a + cT/2
				distance = ust + cT/2
				point1 = {distance, distance2}
				point2 = {X-distance, Y-distance2}
				G.rectangle(point1,point2)
			end
	
		end
	end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"

