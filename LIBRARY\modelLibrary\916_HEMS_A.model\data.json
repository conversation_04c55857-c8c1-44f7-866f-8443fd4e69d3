{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV* \t\t*aV* Acili V bicagi \nK_Freze*cW*mm\t\t*cW*mm Freze Bicagi \nK_Freze*cT*mm\t\t*cT*mm Freze Bicagi  \nCep_Acma\t\tTarama Freze Bicagi \nK_Desen\t\tGobek formlu desen bicagi \nK_D_Kanal \t\tDis kanal bicagi  \nK_I_Kanal \t\tIc kanal bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 350, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 0, "description": "X-Y icin kucuk kapak tarama limit degeri", "parameterName": "limit2"}, {"defaultValue": 60, "description": "Kenardan Acili V Kenara Mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar kapakda acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 25, "description": "Acili V Kenardan DIS Kanala Mesafe", "parameterName": "b"}, {"defaultValue": 0, "description": "Acili V Kenardan Gobege Mesafe", "parameterName": "h"}, {"defaultValue": 35, "description": "Gobekten IC Kanala Mesafe", "parameterName": "d"}, {"defaultValue": 5, "description": "Acili V Derinligi", "parameterName": "ad"}, {"defaultValue": 90, "description": "Acili V Uc Acisi", "parameterName": "aV"}, {"defaultValue": 18, "description": "Tarama <PERSON>", "parameterName": "cW"}, {"defaultValue": 6, "description": "ic Kose Finis Bicak Capi", "parameterName": "cT"}, {"defaultValue": 60, "description": "Ic kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 20, "description": "<PERSON><PERSON><PERSON> ile ic kenar arasi kaydirma miktari", "parameterName": "sbt"}, {"defaultValue": 0, "description": "----------------------DIS KANAL ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 3, "description": "DIS kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 1, "description": "DIS kanal islemi :Ballnose mu? Flat mi? V kanal mi?(B:0/F:1/V:2)", "parameterName": "extGrvBorForV"}, {"defaultValue": 0, "description": "DIS kanal kenar uzatma islemi var mi?(Var :1/ Yok:0)", "parameterName": "extGrvExtExist"}, {"defaultValue": 0, "description": "----------------------GOBEK KANAL ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 1, "description": "Gobek var mi?(Var:1/ Yok:0)", "parameterName": "cabCoreExist"}, {"defaultValue": 3, "description": "Ic dikdortgen kanal islemi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "intRectGrvExist"}, {"defaultValue": 3, "description": "Ic yatay-dikey kanal islemi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "intGrvExist"}, {"defaultValue": 0, "description": "Ic kanallar yatay mi?-dikey mi? (Dikey-Vert:1/Yatay-<PERSON>r:0) ", "parameterName": "<PERSON><PERSON><PERSON>_<PERSON>"}, {"defaultValue": 0, "description": "Gobek desen bicagi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "shapeToolExist"}, {"defaultValue": 0, "description": "----------------------DIS KENAR ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeToolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi V mi? Raduslu mu? (V :0/ R:1)", "parameterName": "extEdgeVorR"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}