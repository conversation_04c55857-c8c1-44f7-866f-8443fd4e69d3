{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\tK_AciliV*aV-K_Ballnose*dB-K_Freze*dF* \tPah bicagi \nK_Ballnose*dB*mm\tOrta bolum kanal bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 12, "description": "DIS vbitten kanala mesafe", "parameterName": "a"}, {"defaultValue": 12, "description": "<PERSON><PERSON>dan ic kanala mesafe", "parameterName": "b"}, {"defaultValue": 4, "description": "Ortadaki kanal adeti", "parameterName": "how<PERSON><PERSON>"}, {"defaultValue": 15, "description": "Orta kanal ilave bosluk", "parameterName": "gStartGap"}, {"defaultValue": 2, "description": "Orta kanal cizgileri derinligi", "parameterName": "ad"}, {"defaultValue": 10, "description": "Top burun orta kanal bicak capi", "parameterName": "dB"}, {"defaultValue": 120, "description": "Kenar pah V bicak acisi (*varsa*)", "parameterName": "aV"}, {"defaultValue": 2.5, "description": "V desen derinligi (Pah ve sunken *varsa*)", "parameterName": "vd"}, {"defaultValue": 100, "description": "Ust desen mesafesi", "parameterName": "topDist"}, {"defaultValue": 2.5, "description": "Ust desen ballnose kanal mi?(Var :Derinlik/ Yok:0)", "parameterName": "doTopShapeGrv"}, {"defaultValue": 1, "description": "DIS kenarlarda pah islemi var mi?(Var :1/ Yok:0)", "parameterName": "extEdgeToolExist"}, {"defaultValue": 1, "description": "Ust desen etrafinda pah islemi var mi?(Var :1/ Yok:0)", "parameterName": "topChamferExist"}, {"defaultValue": 1, "description": "Alt desen var mi? *Ust varsa* (Var :1/ Yok:0)", "parameterName": "bottomPartExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": ["All", "Door"], "version": 1}