-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
   G = ADekoLib
  minimum = 150
  limit = 550
  
  a = 70
  aa = 30
  ad = 8
  spacing = 70
  
  cW = 10                   -- kenar <PERSON>
  cT = 5					-- d<PERSON><PERSON>
  
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köş<PERSON> Radü<PERSON>ü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> rad<PERSON>ü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
      
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
	
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  return Rustic_Regular(X, Y)
end

------------------------------------------------
function Rustic_Regular(X, Y)
	local point1 = {X-a, Y-2.5*ust, 0, 0}
	local bulge = G.bulge(point1, {X/2, Y-ust}, {a, Y-2.5*ust})
  if (bulge>1) then
    print("Bulge too large")
    return true
  end
  point1[4] = bulge
  local point2 = {a, Y-2.5*ust, 0, 0}
  G.setLayer("K_Freze"..cW.."mm")
  G.setThickness(-ad)
  G.line(point1, point2, bulge)		-- make the arc
  local radius = G.radius(point1, point2, bulge)
  local sunkenDepth = G.distance(point1, point2)
  local nLines = math.floor((X-2*a)/spacing)
  local modifiedSpacing = (X-2*a)/nLines
  for i=0, nLines, 1		-- loop for the vertical lines
  do
  	local Lp1 = {a+i*modifiedSpacing, ust}		-- imaginary lines to intersect the above arc
  	local Lp2 = {a+i*modifiedSpacing, Y}
  	comment, pc1, pc2 = G.circleCircleIntersection(point1, radius, point2, radius)		--find center of the arc
  	if (comment=='tangent' or comment=='intersection') then
  		comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		--find line-arc intersection point
  		if (comment2=='tangent' or comment2=='secant') then
         if (i==0 or i==nLines) then 
           G.setLayer("K_Freze"..cW.."mm")
         else
           G.setLayer("K_Freze"..cT.."mm")
         end
         G.setThickness(-ad)
  			G.line(intersection1, Lp1)
  		end
  	else
  		G.error()
       return false
  	end
  end
  G.setLayer("K_Freze"..cW.."mm")
  G.setThickness(-ad)
  G.line({a, ust}, {X-a, ust})		-- close the botom
  return true
end

require "ADekoDebugMode"