-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script - Triangular Divider Frame Test
-- Converted from C# azCAM macro: Triangular Divider Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 400
  Y = Y or 300
  materialThickness = materialThickness or 18
  
  -- Triangular Divider Frame parameters (from original C# macro)
  A = 50    -- Left/right margin
  B = 50    -- Top/bottom margin  
  C = 50    -- Offset/gap parameter
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * A + C + 20  -- minimum required width
  local minHeight = 2 * B + 20     -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Triangular Divider Frame function (converted from C# macro)
  local function triangular_divider_frame(A, B, C, Z)
    -- Use default values if parameters not provided
    A = A or 50
    B = B or 50
    C = C or 50
    Z = Z or -5
    
    print(string.format("Creating triangular divider frame"))
    print(string.format("Parameters: A=%d, B=%d, C=%d, Z=%d", A, B, C, -Z))
    
    -- Set layer and thickness for triangular shapes
    G.setLayer("G_V15")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create first triangular shape (from C# points p1, p2, p3)
    -- p1 = (A, height-B) - top-left area
    -- p2 = (width-A-C, height-B) - top-right area  
    -- p3 = (A, B) - bottom-left area
    local p1 = {A, height - B}
    local p2 = {width - A - C, height - B}
    local p3 = {A, B}
    
    print(string.format("First triangle: (%.1f,%.1f) -> (%.1f,%.1f) -> (%.1f,%.1f)", 
          p1[1], p1[2], p2[1], p2[2], p3[1], p3[2]))
    
    -- Create the first triangular path
    G.polyline(p1, p2, p3, p1)  -- Close the triangle
    
    -- Start new shape for second triangle
    G.nextShape()
    
    -- Create second triangular shape (from C# points q1, q2, q3)
    -- q1 = (A+C, B) - bottom-left area (offset by C)
    -- q2 = (width-A, B) - bottom-right area
    -- q3 = (width-A, height-B) - top-right area
    local q1 = {A + C, B}
    local q2 = {width - A, B}
    local q3 = {width - A, height - B}
    
    print(string.format("Second triangle: (%.1f,%.1f) -> (%.1f,%.1f) -> (%.1f,%.1f)", 
          q1[1], q1[2], q2[1], q2[2], q3[1], q3[2]))
    
    -- Create the second triangular path
    G.polyline(q1, q2, q3, q1)  -- Close the triangle
    
    return true
  end
  
  -- Call the triangular divider frame function
  local success = triangular_divider_frame(A, B, C, -Z)
  
  if success then
    print(string.format("Triangular divider frame created with parameters:"))
    print(string.format("  A (left/right margin): %d mm", A))
    print(string.format("  B (top/bottom margin): %d mm", B))
    print(string.format("  C (offset parameter): %d mm", C))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Door size: %dx%d mm", X, Y))
    print("")
    print("Pattern description:")
    print("  - Creates two complementary triangular shapes")
    print("  - First triangle covers upper-left to lower-left area")
    print("  - Second triangle covers lower-right to upper-right area")
    print("  - Together they form a diagonal divider frame pattern")
  end
  
  return true
end

require "ADekoDebugMode"
