-- Test file 2 for comparison (modified version)
function modelMain()
    -- Set up the drawing environment
    ADekoLib.setLayer("LMM1")  -- Changed layer
    ADekoLib.setThickness(20)  -- Changed thickness
    ADekoLib.setFace(2)        -- Changed face
    
    -- Local variables with changes
    local x, y = 150, 150      -- Changed coordinates
    local radius = 30.0        -- Changed radius
    local message = "Hello, Lua World!"  -- Changed message
    local newVar = true        -- Added new variable
    
    -- Basic drawing functions
    ADekoLib.point(x, y)
    ADekoLib.line(x, y, x + 75, y + 75)  -- Changed line length
    ADekoLib.circle(x, y, radius)
    
    -- Create a rectangle with different size
    ADekoLib.rectangle(x - 15, y - 15, x + 15, y + 15)
    
    -- Added new functionality
    ADekoLib.arc(x, y, radius, 0, 90)
    
    print("Enhanced drawing completed")
end
