-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 240
  
  a = 50
  aa = 30
  ad = 6
  aV = 120
  b = 0 -- kenardan kanal baslangıcına mesafe
  c = 20 --kanallar arası mesafe
  cT = 5 -- takım capı
  surface  = 0		
  
  intGrooveExist            = 1  --Ic kanal var mı (Var: 1 / Yok: 0)
  grooveBorForV	            = 2		--kanallar Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)
  extEdgeVtoolExist       	= 6  -- D<PERSON><PERSON> kenar <PERSON>h i<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end

  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
  local ust = a
  local ustKanal = 0
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
	
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
		if grooveBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			G.setLayer("K_Ballnose"..cT.."mm")  --yarıcaptan fazla dalıyorsa cT/2 olsun
      if ad > cT/2 then
        ad = cT/2
      end
      ustKanal = ((cT/2)^2 - ((cT/2)-ad)^2)^0.5
		elseif grooveBorForV == 1 then
			G.setLayer("K_Freze"..cT.."mm")  
      ustKanal = cT/2
		elseif grooveBorForV == 2 then
			G.setLayer("K_AciliV"..aV)  
      ustKanal = sunkenWidth
		end	
    
  G.setThickness(0)
  local points1 = {{a+ustKanal,b,-surface},{a+ustKanal,ust+ustKanal,-ad},{a+ustKanal,Y-(ust+ustKanal),-ad},{a+ustKanal,Y-b,-surface}} --sol
  local points2 = {{X-(a+ustKanal),b,-surface},{X-(a+ustKanal),ust+ustKanal,-ad},{X-(a+ustKanal),Y-(ust+ustKanal),-ad},{X-(a+ustKanal),Y-b,-surface}} --sag
  local points3 = {{b,ust+ustKanal,-surface},{a+ustKanal,ust+ustKanal,-ad},{X-(a+ustKanal),ust+ustKanal,-ad},{X-b,ust+ustKanal,-surface}} --ust
  local points4 = {{b,Y-(ust+ustKanal),-surface},{a+ustKanal,Y-(ust+ustKanal),-ad},{X-(a+ustKanal),Y-(ust+ustKanal),-ad},{X-b,Y-(ust+ustKanal),-surface}} --alt
 
  if intGrooveExist  > 0 then
    local points11 = {{a+ustKanal+c,b,-surface},{a+ustKanal+c,ust+ustKanal,-ad},{a+ustKanal+c,Y-(ust+ustKanal),-ad},{a+ustKanal+c,Y-b,-surface}}--sol2
    local points21 = {{X-(a+ustKanal+c),b,-surface},{X-(a+ustKanal+c),ust+ustKanal,-ad},{X-(a+ustKanal+c),Y-(ust+ustKanal),-ad},{X-(a+ustKanal+c),Y-b,-surface}}--sag2
    local points31 = {{b,ust+ustKanal+c,-surface},{a+ustKanal,ust+ustKanal+c,-ad},{X-(a+ustKanal),ust+ustKanal+c,-ad},{X-b,ust+ustKanal+c,-surface}} --ust
    local points41 = {{b,Y-(ust+ustKanal+c),-surface},{a+ustKanal,Y-(ust+ustKanal+c),-ad},{X-(a+ustKanal),Y-(ust+ustKanal+c),-ad},{X-b,Y-(ust+ustKanal+c),-surface}} --alt
      G.polylineimp (points11)
      G.polylineimp (points21)
      G.polylineimp (points31)
      G.polylineimp (points41)
  end
  
  G.polylineimp (points1)
  G.polylineimp (points2)
  G.polylineimp (points3)
  G.polylineimp (points4)
  
  return true
end

require "ADekoDebugMode"
