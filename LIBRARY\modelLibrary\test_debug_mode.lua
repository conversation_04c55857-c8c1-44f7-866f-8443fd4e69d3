-- Test script for debug mode
-- This should work like ZeroBrain when run in debug mode

function modelMain()
    print("modelMain() called successfully!")
    
    -- Draw a simple rectangle using turtle graphics
    zero(100, 100)
    move(200)
    turn(90)
    move(150)
    turn(90)
    move(200)
    turn(90)
    move(150)
    turn(90)
    
    text("Test Rectangle", 0, 10, 100)
    
    print("modelMain() completed")
end

print("Test script loaded - modelMain function defined")
