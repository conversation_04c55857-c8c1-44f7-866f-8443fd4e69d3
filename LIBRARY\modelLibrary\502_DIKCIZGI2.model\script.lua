-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
   
  xMin = 140
  yMin = 140 
  a 	= -5
  xMin = 140
  yMin = 140
  toolTipDiameter = 20
  toolNumber = 50
  gaps = 50
  ad = 3		--toolDepth--kanal derinliği
  
  edgeCornerRExist        = 0   -- Ka<PERSON><PERSON> köşe Radüsü var mı?
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  bulge = math.tan(math.pi/8)
  
  local D = edgeCornerRExist		-- köşe radüsü-yarıçapı
  local B = math.tan(math.pi/8)
  if edgeCornerRExist > 0 then
    G<PERSON>makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
   
  local edgeChmfExist        = 1   --Kenarlarda aynı takımla işleme var: 1 yok: 0
  local toolType = "Form" -- "Freze" , "Ballnose" or "Acili_V"
  --local toolDepth = 8
  --local toolDiameter = 16       --Ballnose or conical
  local toolAngle = 170
  local grooveThickness =0
  local toolRadius = toolTipDiameter/2
  --local gaps = 12.5
  local ara = 0
  local layerNameEd ="K_" .. toolType .."_TN_".. tostring(toolNumber)
  
  local gorunenHal = false
  

  
  grooveThickness = toolTipDiameter 
  
  --Aralık hesaplama
  
    local aprxGaps = gaps
    local e = gaps
    G.setLayer(layerNameEd)
    G.setThickness(-ad)
	
	if edgeChmfExist == 1 then
	  point1 = {-toolTipDiameter/2, 0+a}
      point2 = {-toolTipDiameter/2, Y-a}
      point5 = {X+toolTipDiameter/2, 0+a}
      point6 = {X+toolTipDiameter/2, Y-a}
      G.line(point1,point2,0)
      G.line(point5,point6,0)
	elseif edgeChmfExist == 0 then
		gorunenHal = true
	end
    
    local stepX = 0

    local fPcX = math.floor(X/gaps)

    if ((X/gaps)-fPcX)  >= 0.5 then
      stepX = math.ceil((X/gaps)-1)
    else
      stepX = math.floor((X/gaps)-1)
    end
      
   
    if gorunenHal == false then
        e = ((X-(stepX)*grooveThickness)/(stepX+1))+grooveThickness/2
        aprxGaps = e+grooveThickness/2
    
      for i=0,stepX-1 do
        local x = e+i*aprxGaps
        point3 = {x,a}
        --point3 = {x,0}
        point4 = {x,Y-a}  
        --point4 = {x,Y}  
        G.line(point3,point4,0)
        i = i+1
      end
      --print("bu kenarlar farklı ortalar eşit aralık 'FALSE_'"..aprxGaps .. "_mm")
    else
      aprxGaps = (X/(stepX+1))
      for i=0,stepX-1 do
        local x = aprxGaps+i*aprxGaps
        point3 = {x,a}
        --point3 = {x,0}
        point4 = {x,Y-a}  
        --point4 = {x,Y}  
        G.line(point3,point4,0)
        i = i+1
      end
      --print("bu esit aralık TRUE_"..aprxGaps .. "_mm")
    end  
	
	
  
  
  return true
end

require "ADekoDebugMode"