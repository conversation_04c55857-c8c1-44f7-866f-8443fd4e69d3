-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
   
  a = 50
  c = 20
  aV = 120					-- Vbit Acisi
  blockNoX = 0
  blockNoY = 0
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 5                   -- <PERSON><PERSON> bıçak çapı (Köşe Temizleme ve pencere kesim)
    
  windowDepthFront = 14
  windowDepthBack = 6
  
  intEdgeVtoolExist         = 5
  extEdgeVtoolExist       	= 0   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak kose Radusu var mi? derinlik/0:yok
    
   
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  --blockNoX ve blockNoY 0 ise alttaki limitlere göre pencere koyar
  
  local xLimit1 = 400 -- yana 2. pencere koymak icin alt limit "Dahil"
  local xLimit2 = 800 -- yana 3. pencere koymak icin alt limit "Dahil"
  local xLimit3 = 1200 -- yana 4. pencere koymak icin alt limit "Dahil"
  
  local yLimit1 = 400 -- uste 2. pencere koymak icin alt limit "Dahil"
  local yLimit2 = 800 -- uste 3. pencere koymak icin alt limit "Dahil"
  local yLimit3 = 1200 -- uste 4. pencere koymak icin alt limit "Dahil"
  
  if blockNoX == 0 then
    blockNoX = 1
    if X >= xLimit1 and X < xLimit2 then
      blockNoX = 2
    elseif X >= xLimit2 and X < xLimit3 then
      blockNoX = 3
    elseif X >= xLimit3 then
      blockNoX = 4
    end
  end
  
  if blockNoY == 0 then
     blockNoY = 1
    if Y >= yLimit1 and Y < yLimit2 then
      blockNoY = 2
    elseif Y >= yLimit2 and X < yLimit3 then
      blockNoY = 3
    elseif Y >= yLimit3 then
      blockNoY = 4
    end
  end
  
  local bulge = math.tan(math.pi/8)
 
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  local vWideDiameter = 60
  local sunkenWidth = intEdgeVtoolExist*math.tan((math.pi*aV/180)/2.0)
  local stepX = (blockNoX-1)		--Number of laths on X axis
  local stepY = (blockNoY-1)		--Number of laths on Y axis
  local innerX = X-2*a-2*sunkenWidth-stepX*c-stepX*2*sunkenWidth		--Width of the inner rectangle on the x axis
  local innerY = Y-2*a-2*sunkenWidth-stepY*c-stepY*2*sunkenWidth		--Length of the inner rectangle on the y axis
  local correctedblockX = innerX / blockNoX
  local correctedblockY = innerY / blockNoY
  
  
  if extEdgeVtoolExist > 0 then
    G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
    sunkenWidth3 = extEdgeVtoolExist*math.tan((math.pi*aV/180)/2)
  end
  
  for i=0, stepX, 1
  do
    G.setLayer("K_AciliV"..aV)  -- windows
    G.setThickness(0)
    x1 = a+i*(2*sunkenWidth+correctedblockX+c)
    x2 = a+(i+1)*(2*sunkenWidth+correctedblockX)+i*c
    for j=0, stepY, 1
    do
		G.setLayer("K_AciliV"..aV)  -- windows
		G.setThickness(0)
		y1 = a+j*(2*sunkenWidth+correctedblockY+c)
		y2 = a+(j+1)*(2*sunkenWidth+correctedblockY)+j*c
		point1 = {x1,y1}
		point2 = {x2,y2}
		if intEdgeVtoolExist >0 then
			local corner1,corner2 = G.sunkenFrame(point1, point2, intEdgeVtoolExist, aV, vWideDiameter)
			corner1[3], corner2[3] = 0, 0
		end
		G.setLayer("H_Freze"..cT.."mm_Ic")
		G.setThickness(-windowDepthFront)		--windows
		if intEdgeVtoolExist >0 then
			point1 = G.ptAdd(point1,{sunkenWidth,sunkenWidth})
			point2 = G.ptSubtract(point2, {sunkenWidth,sunkenWidth})
			G.rectangle(point1, point2)
		else
			G.rectangle(point1, point2)
		end
    end
  end
  
  G.setFace("bottom")
  
  G.setLayer("K_Freze"..cW.."mm_SF")		--Clearing procedures of the backside
  for i = 1, stepX do
    G.setThickness(-windowDepthBack)
    local y = a+sunkenWidth
    local x = a+i*(2*sunkenWidth+correctedblockX)+((i*2)-1)*(c/2)
    point1 = {x,y}
    point2 = {x,Y-y}
    G.line(point1,point2,0)
  end
  
  for j = 1, stepY do
    G.setThickness(-windowDepthBack)
    local x = a+sunkenWidth
    local y = a+j*(2*sunkenWidth+correctedblockY)+((j*2)-1)*(c/2)
    point1 = {x,y}
    point2 = {X-x,y}
    G.line(point1,point2,0)
  end
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth-cW/2, a+sunkenWidth-cW/2}, {X-a-sunkenWidth+cW/2, Y-a-sunkenWidth+cW/2})
  
  G.setLayer("K_Freze"..cW.."mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth, a+sunkenWidth}, {X-a-sunkenWidth, Y-a-sunkenWidth})
  return true
end

require "ADekoDebugMode"
