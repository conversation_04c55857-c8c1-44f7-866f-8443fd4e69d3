-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  minimum = 250
  limit = 350
    
  a = 60
  aa = 30
  ad = 8
  aV = 90	
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 10                   -- <PERSON><PERSON> bıçak çapı (Köşe Temizleme ve pencere kesim)
  
  extEdgeVtoolExist       	= 0   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak kose Radusu var mi? derinlik/0:yok
    
  windowDepthFront = 14
  windowDepthBack = 6
  	  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local ust = a

	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
      
  if extEdgeVtoolExist > 0 then
    G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  -- local finalDepth = 8
  local vMidDiameter = 40
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2.0)

  if X<xMin or Y<yMin then
    G.error("Plate too small")
    return false
  end
  
  G.setLayer("K_AciliV"..aV)		--Angled surface
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setThickness(-ad)
  G.rectangle({0,0},{X,Y})
  
  G.setLayer("H_Freze"..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  distance = a+sunkenWidth
  distance1 = ust+sunkenWidth
  point1 = {distance, distance1}
  point2 = {X-distance, Y-distance1}
  G.rectangle(point1,point2)
  
  G.setFace("bottom")
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth-cW/2, ust+sunkenWidth-cW/2}, {X-a-sunkenWidth+cW/2, Y-ust-sunkenWidth+cW/2})
  
  G.setLayer("K_Freze"..cW.."mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a+sunkenWidth, ust+sunkenWidth}, {X-a-sunkenWidth, Y-ust-sunkenWidth})

  return true
end
require "ADekoDebugMode"
