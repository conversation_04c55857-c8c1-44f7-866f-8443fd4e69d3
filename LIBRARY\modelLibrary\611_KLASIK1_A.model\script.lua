-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  minimum = 250
  limit = 350
  
  a = 60
  aa = 30
  ad = 8
  aV = 90					-- Vbit Acisi
  h = 30
  cW = 10                   -- Tarama Bıçak Çapı
  cT = 5                   -- <PERSON><PERSON> bıçak çapı (Köşe Temizleme ve pencere kesim)
     
  extEdgeVtoolExist       	= 5   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak kose Radusu var mi? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local vWideAngle = 120
  local vMidDiameter = 40
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2.0)
  
  local ust = a

	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cW or h<cT then
    print("Tool too large")
    return true
  end
  
  G.setLayer("K_AciliV"..aV)		--Angled surface
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  G.setLayer("K_AciliV30")		--Angled surface
  G.setThickness(-ad)
  G.rectangle({a+sunkenWidth+h, ust+sunkenWidth+h}, {X-(a+sunkenWidth+h), Y-(ust+sunkenWidth+h)})
  
  G.setLayer("K_Freze"..cW.."mm")		--Channel cleaning
  G.setThickness(-ad)
  distance = a+sunkenWidth+cW/2
  distance1 = ust+sunkenWidth+cW/2
  point1 = {distance, distance1}
  point2 = {X-distance, Y-distance1}
  G.rectangle(point1,point2)
  
  if h > cW then
    distance = a+sunkenWidth+h-cW/2
    distance1 = ust+sunkenWidth+h-cW/2
    point3 = {distance, distance1}
    point4 = {X-distance, Y-distance1}
    G.rectangle(point3,point4) 
  
    k = (h-cW)/(cW/2)
    
    for i=1, k, 1 do
      point1 = G.ptAdd(point1,{cW/2,cW/2})
      point2 = G.ptSubtract(point2, {cW/2,cW/2})
      if point1[1]>point3[1]-cW/2 then
        break
      end
      G.rectangle(point1,point2)
    end
  end
  
  G.setLayer("K_TarKoseTemizl"..cT.."mm")		--Corner cleaning
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,ad,cT)
    
  return true
end


require "ADekoDebugMode"
