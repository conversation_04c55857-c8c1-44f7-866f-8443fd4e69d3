-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  --limit = 350
  
  a = 40
  ad = 5
  aV = 120
  d = 20
  m = 75
  yy = 100
  cT = 6
  
  topGrooveExist   				= 1.5   -- Vbit Ustu kanal var mı? derinlik/0:yok
  topGrvExtExist				= 1 --Ust kanal uzatma var mı? (Var:1/Yok:0)
  extEdgeVtoolExist       	= 0  -- D<PERSON>ş kenar Pah işlemi var mı? derinlik/0:yok
  edgeCornerRExist          = 0  -- Kapak köşe Radüsü var mı? derinlik/0:yok
  local h = 25
  local cabCoreExist            = 0 -- Go<PERSON> var mı Var:1 Yok:0 --gobek yoksa ortayı tarama yap
  local shapeToolExist         = 4 --Gobekte Desen bıcak var mı derinlik/0:yok
  local intGrvExist          = 0 --ic kanal islemi var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
  
  local bulge1 = G.bulge({m,0,0,0}, {X/2, yy,0,0}, {X-m, 0, 0, 0})
  if (bulge1>1) then
    print("Bulge too large")
    return true
  end
  
  local bulge =  math.tan(math.pi/8)
  G.setFace("top")
  G.setThickness(-materialThickness)
  
  
  local pointExt ={}
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
      pointExt = {{edgeCornerRExist,0},		--chamfered part shape
      {edgeCornerRExist+m,0,0,bulge1},		--chamfered part shape
      {X-m,0,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0}}
      G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {edgeCornerRExist+m,0,0,bulge1},		--chamfered part shape
      {X-m,0,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
      pointExt = {{0,0},		--chamfered part shape
      {m,0,0,bulge1},		--chamfered part shape
      {X-m,0},		--chamfered part shape
      {X,0},
      {X,Y},
      {0,Y},
      {0,0}}
    G.makePartShape({0,0},		--chamfered part shape
      {m,0,0,bulge1},		--chamfered part shape
      {X-m,0},		--chamfered part shape
      {X,0},
      {X,Y},
      {0,Y},
      {0,0})
  end
  
  --poins = 
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
    G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.polylineimp(pointExt)
  end

  local c = 0
  local pointb2 = a
  local firstPoint = {pointb2,a,0,0}
  local lastPoint = {X-pointb2, a, 0, 0}
  local bulge2 = G.bulge(firstPoint, {X/2, yy+a,0,0},lastPoint )
  local radius = G.radius(firstPoint, lastPoint, bulge2)
  
    if (bulge2>1) then
      print("Bulge too large")
      return true
    end
	
	local Lp1 = {(X-a)/2, 0}		-- imaginary lines to intersect the above arc
	local Lpm1 = {(X-1.5*a)/2, 0}		-- imaginary lines to intersect the above arc
  	local Lp2 = {(X-a)/2, Y}
  	local Lpm2 = {(X-1.5*a)/2, Y}
	local nokta1 ={}
	local nokta1m ={}
	comment, pc1, pc2 = G.circleCircleIntersection(firstPoint, radius, lastPoint, radius)		--find center of the arc
	if (comment=='tangent' or comment=='intersection') then
		comment2, intersection1, intersection2 = G.circleLineIntersection(pc2, radius, Lp1, Lp2)		--find line-arc intersection point
		comment3, intersection1m, intersection2m = G.circleLineIntersection(pc2, radius, Lpm1, Lpm2)		--find line-arc intersection point
		if (comment2=='tangent' or comment2=='secant') then
			nokta1 = intersection1
		end
		if (comment3=='tangent' or comment3=='secant') then
			nokta1m = intersection1m
		end
	end
	
	nokta1m[3] = 0
	nokta1m[4] = 0
	nokta1 [3] = 0
	nokta1 [4] = 0
	local firstLeftPoint = {pointb2,a,0,0}
	local bulge2m = G.bulge(firstLeftPoint, nokta1m, nokta1 )
	
	points3 = {{(X-a)/2,a+yy},		--sol taraf
      {(X-a)/2,Y-a},
      {a,Y-a},
      {a,a,0,bulge2m/3},
      {(X-a)/2,a+yy}}
	
	
	local Lp11 = {(X+a)/2, 0}		-- imaginary lines to intersect the above arc
	local Lpm11 = {(X+1.5*a)/2, 0}		-- imaginary lines to intersect the above arc
  	local Lp21 = {(X+a)/2, Y}
  	local Lpm21 = {(X+1.5*a)/2, Y}
	local nokta2 ={}
	local nokta2m ={}
	
	if (comment=='tangent' or comment=='intersection') then
		comment2, intersection11, intersection21 = G.circleLineIntersection(pc2, radius, Lp11, Lp21)		--find line-arc intersection point
		comment3, intersection11m, intersection21m = G.circleLineIntersection(pc2, radius, Lpm11, Lpm21)		--find line-arc intersection point
		if (comment2=='tangent' or comment2=='secant') then
			nokta2 = intersection11
		end
		if (comment3=='tangent' or comment3=='secant') then
			nokta2m = intersection11m
		end
	end
	
	nokta2m[3] = 0
	nokta2m[4] = 0
	local firstRightPoint = {X-a,a,0,0}
	local bulge3m = G.bulge(firstRightPoint, nokta2m, nokta2 )

		
  --iki parça olarak

	points33 = {					--sag taraf
      {(X+a)/2,Y-a},
      {(X+a)/2,a+yy,0,-bulge3m/3},
      {X-a,a},
      {X-a,Y-a},
	  {(X+a)/2,Y-a},		--chamfered part shape
	  }
	--iki parca olarak 
	
	local gobekListe = {points3,points33}		--sag ve sol tarafı da kullansın
	--print("test"..gobekListe[1])  
  
   if (yy+2*h+2*a > Y) then 
		print("yukseklikleri kontrol edin yy,h,a toplamı Y den buyuk olamaz!!")
     return true
   end

    
	for i=1, 2		-- loop for the vertical lines
	do
		local sekil2 = true
		if m> 2*a+2*sunkenWidth and h == 0 then       --h ofseti doğru olmadığı için kapatıldı
			c= 1*(m-2*a)
			pointb2 = a+c
			points2 = {{a,a},		--chamfered part shape
			{a+c,a,0,bulge2},		--chamfered part shape
			{X-a-c,a},		--chamfered part shape
			{X-a,a},		--chamfered part shape
			{X-a,Y-a},
			{a,Y-a},
			{a,a}}
			G.setLayer("V_AciliV"..aV)
			G.setThickness(-ad)
			G.polylineimp(points2)
			sekil2 = false
		else
			-- pointsToCut = G.sunkenFrameAny(points33, 2, ad, aV, vWideDiameter)
			G.setLayer("K_AciliV"..aV)
			G.setThickness(0)
			pointsToCut = G.sunkenFrameAny(gobekListe[i], 2, ad, aV, vWideDiameter)
			--G.polylineimp(pointsToCut)
		end
    
	-- for i=0, gobekListe.lenght(), 1		-- loop for the vertical lines
		-- do

		
		if cabCoreExist >0 and  h>0 then
			if shapeToolExist > 0 then  				          ----Göbekte desen bıçağı varsa
				G.setLayer("K_Desen")
				G.setThickness(-shapeToolExist)
			else
				G.setLayer("K_AciliV"..aV)
				G.setThickness(-ad)
			end
			G.polylineimp (G.offSet(pointsToCut, -h))
			
			G.setLayer("Cep_Acma")                  --Tarama islemi
			G.setThickness(-ad)
			G.polylineimp (pointsToCut)
			G.polylineimp (G.offSet(pointsToCut, -h))
			G.setLayer("H_Freze"..cT.."mm_Ic")
			G.setThickness(-ad)
			G.polylineimp(pointsToCut)
		else        --gobek yoksa gobekte tarama yap
			if sekil2 then
				G.setLayer("Cep_Acma")                  --Tarama islemi
				G.setThickness(-ad)
				G.polylineimp (pointsToCut)
				G.setLayer("H_Freze"..cT.."mm_Ic")
				G.setThickness(-ad)
				G.polylineimp(pointsToCut)
			end
		end 
		
		local offset = h+d
		if intGrvExist > 0 and cabCoreExist > 0 then              --ic kanal var mı
			G.setLayer("H_Freze"..cT.."mm_Ic")
			G.setThickness(-intGrvExist)
			G.polylineimp(G.offSet(pointsToCut, -offset))
		end
		
		if topGrooveExist > 0 then              --Dıs v kanal üstü kanal islemi var mı
			G.setLayer("H_Freze"..cT.."mm_Ic")
			G.setThickness(-topGrooveExist)
			G.polylineimp(gobekListe[i])
		-- G.polylineimp(points3)
		
		end
		i=i+1
	end
	local ust = a
	if topGrvExtExist == 1 and topGrooveExist > 0 then		--sag ve sol tarafta fazladan islem cıkmasın diye buraya alındı
		G.setLayer("K_Freze"..cT.."mm")
		G.setThickness(-topGrooveExist)
		G.line({X-a-cT/2, Y-ust-cT},{X-a-cT/2, Y})
		G.line({a+cT/2, Y-ust-cT},{a+cT/2, Y})
		G.line({a+cT/2, ust+cT},{a+cT/2, 0})
		G.line({X-a-cT/2, ust+cT},{X-a-cT/2, 0})
	end
			
  G.list()
  return true
end

require "ADekoDebugMode"
