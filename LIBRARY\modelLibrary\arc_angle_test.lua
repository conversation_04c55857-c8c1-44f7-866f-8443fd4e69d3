-- Arc Angle Test Script
-- This script tests various arc angles to identify the issue

print("=== Arc Angle Test ===")

-- Load the new engine
local makerjs_engine = require("LIBRARY.luaLibrary.new_engine.makerjs_engine")
local ADekoLib = require("LIBRARY.luaLibrary.new_engine.ADekoLib")

-- Set up the engine
ADekoLib.engine = makerjs_engine

-- Create a test model
makerjs_engine.model_def("arc_angle_test", function()
    
    -- Test 1: Basic arcs with different angles
    print("Test 1: Basic arcs with different angles")
    makerjs_engine.layer("basic_arcs")
    
    -- Quarter arc (0° to 90°) - should be a quarter circle in first quadrant
    makerjs_engine.arc("quarter_arc", 100, 100, 50, 0, 90, false)
    
    -- Half arc (0° to 180°) - should be a semicircle
    makerjs_engine.arc("half_arc", 250, 100, 50, 0, 180, false)
    
    -- Three-quarter arc (0° to 270°) - should be three quarters of a circle
    makerjs_engine.arc("three_quarter_arc", 400, 100, 50, 0, 270, false)
    
    -- Test 2: Arcs with different start angles
    print("Test 2: Arcs with different start angles")
    makerjs_engine.layer("start_angle_arcs")
    
    -- Arc from 45° to 135° (90° span)
    makerjs_engine.arc("arc_45_135", 100, 250, 50, 45, 135, false)
    
    -- Arc from 90° to 180° (90° span)
    makerjs_engine.arc("arc_90_180", 250, 250, 50, 90, 180, false)
    
    -- Arc from 180° to 270° (90° span)
    makerjs_engine.arc("arc_180_270", 400, 250, 50, 180, 270, false)
    
    -- Test 3: Clockwise vs Counter-clockwise
    print("Test 3: Clockwise vs Counter-clockwise")
    makerjs_engine.layer("clockwise_test")
    
    -- Counter-clockwise arc (0° to 90°)
    makerjs_engine.arc("ccw_arc", 100, 400, 50, 0, 90, false)
    
    -- Clockwise arc (0° to 90°)
    makerjs_engine.arc("cw_arc", 250, 400, 50, 0, 90, true)
    
    -- Test 4: Using ADekoLib arc function
    print("Test 4: ADekoLib arc function")
    ADekoLib.setFace("top")
    ADekoLib.setLayer("adeko_arcs")
    
    -- Create arcs using ADekoLib
    ADekoLib.arc({100, 550}, 50, 0, 90, false, "adeko_quarter")
    ADekoLib.arc({250, 550}, 50, 0, 180, false, "adeko_half")
    ADekoLib.arc({400, 550}, 50, 45, 135, false, "adeko_45_135")
    
    -- Test 5: Arcs with bulge (polyline arcs)
    print("Test 5: Polyline arcs with bulge")
    ADekoLib.setLayer("polyline_arcs")
    
    -- Create a polyline with arc segments
    local arc_polyline = {
        {50, 700},           -- start point
        {150, 700, 0, 0.5},  -- point with positive bulge (counter-clockwise arc)
        {250, 700},          -- end point
    }
    ADekoLib.polylineimp(arc_polyline)
    
    -- Another polyline with negative bulge
    local arc_polyline2 = {
        {300, 700},           -- start point
        {400, 700, 0, -0.5},  -- point with negative bulge (clockwise arc)
        {500, 700},           -- end point
    }
    ADekoLib.polylineimp(arc_polyline2)
    
    -- Test 6: Reference circles for comparison
    print("Test 6: Reference circles for comparison")
    makerjs_engine.layer("reference_circles")
    
    -- Draw full circles at the same centers as our arcs for reference
    makerjs_engine.circle("ref_circle_1", 100, 100, 50)
    makerjs_engine.circle("ref_circle_2", 250, 100, 50)
    makerjs_engine.circle("ref_circle_3", 400, 100, 50)
    
    -- Test 7: Debug output for arc parameters
    print("Test 7: Debug output")
    print("Expected: Quarter arc from 0° to 90° should go from (150,100) to (100,150)")
    print("Expected: Half arc from 0° to 180° should go from (300,100) to (200,100)")
    print("Expected: Three-quarter arc from 0° to 270° should go from (450,100) to (400,50)")
    
end)

-- Export the model
local json_output = makerjs_engine.export_model("arc_angle_test")

-- Save to file
local file = io.open("arc_angle_test_output.json", "w")
if file then
    file:write(json_output)
    file:close()
    print("✓ Arc angle test model exported to arc_angle_test_output.json")
else
    print("✗ Failed to save output file")
end

print("=== Arc Angle Test Complete ===")
