-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script - Rectangle Chamfer Frame Test
-- Converted from C# azCAM macro: Rectangle Chamfer Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Rectangle Chamfer Frame parameters (from original C# macro)
  A = 60     -- Left margin
  B = 60    -- Top margin
  C = 60    -- Right margin
  D = 60     -- Bottom margin
  DA = 10    -- Chamfer size at corner A (top-left)
  DB = 10    -- Chamfer size at corner B (top-right)
  DC = 10   -- Chamfer size at corner C (bottom-right)
  DD = 10    -- Chamfer size at corner D (bottom-left)
  Z = 5     -- Thickness/depth

  -- Reference corner (from original parameters.json: reference = 6)
  -- 1=top-left, 2=top-center, 3=top-right, 4=middle-left, 5=center, 6=middle-right, 7=bottom-left, 8=bottom-center, 9=bottom-right
  referenceCorner = 3  -- middle-right (original default)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + C + math.max(DA, DB, DC, DD) * 2 + 20
  local minHeight = B + D + math.max(DA, DB, DC, DD) * 2 + 20
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Rectangle Chamfer Frame function (converted from C# macro)
  local function rectangle_chamfer_frame(A, B, C, D, DA, DB, DC, DD, Z, refCorner)
    -- Use default values if parameters not provided
    A = A or 5
    B = B or 80
    C = C or 80
    D = D or 5
    DA = DA or 5
    DB = DB or 5
    DC = DC or 5
    DD = DD or 5
    Z = Z or -5
    refCorner = refCorner or 6

    print(string.format("Creating rectangle chamfer frame"))
    print(string.format("Margins: A=%d, B=%d, C=%d, D=%d", A, B, C, D))
    print(string.format("Chamfers: DA=%d, DB=%d, DC=%d, DD=%d", DA, DB, DC, DD))
    print(string.format("Reference corner: %d", refCorner))

    -- Calculate frame dimensions
    local frameWidth = A + C  -- Total horizontal margins
    local frameHeight = B + D -- Total vertical margins

    -- Calculate frame position based on reference corner
    local frameLeft, frameBottom

    if refCorner == 1 then      -- top-left
      frameLeft = 0
      frameBottom = height - frameHeight
    elseif refCorner == 2 then  -- top-center
      frameLeft = (width - frameWidth) / 2
      frameBottom = height - frameHeight
    elseif refCorner == 3 then  -- top-right
      frameLeft = width - frameWidth
      frameBottom = height - frameHeight
    elseif refCorner == 4 then  -- middle-left
      frameLeft = 0
      frameBottom = (height - frameHeight) / 2
    elseif refCorner == 5 then  -- center
      frameLeft = (width - frameWidth) / 2
      frameBottom = (height - frameHeight) / 2
    elseif refCorner == 6 then  -- middle-right
      frameLeft = width - frameWidth
      frameBottom = (height - frameHeight) / 2
    elseif refCorner == 7 then  -- bottom-left
      frameLeft = 0
      frameBottom = 0
    elseif refCorner == 8 then  -- bottom-center
      frameLeft = (width - frameWidth) / 2
      frameBottom = 0
    elseif refCorner == 9 then  -- bottom-right
      frameLeft = width - frameWidth
      frameBottom = 0
    else
      -- Default to middle-right if invalid reference
      frameLeft = width - frameWidth
      frameBottom = (height - frameHeight) / 2
    end

    -- Calculate frame corner points with reference positioning
    local leftX = frameLeft + A
    local rightX = frameLeft + frameWidth - C
    local bottomY = frameBottom + D
    local topY = frameBottom + frameHeight - B
    
    -- Calculate chamfered corner points
    -- Each corner gets cut at 45-degree angle by the chamfer distance
    
    -- Bottom-left corner (line4 to line1 transition)
    local p1 = {leftX + DD, bottomY}        -- Bottom line end (chamfered)
    local p2 = {leftX, bottomY + DD}        -- Left line start (chamfered)
    
    -- Top-left corner (line1 to line2 transition)  
    local p3 = {leftX, topY - DA}           -- Left line end (chamfered)
    local p4 = {leftX + DA, topY}           -- Top line start (chamfered)
    
    -- Top-right corner (line2 to line3 transition)
    local p5 = {rightX - DB, topY}          -- Top line end (chamfered)
    local p6 = {rightX, topY - DB}          -- Right line start (chamfered)
    
    -- Bottom-right corner (line3 to line4 transition)
    local p7 = {rightX, bottomY + DC}       -- Right line end (chamfered)
    local p8 = {rightX - DC, bottomY}       -- Bottom line start (chamfered)
    
    -- Validate chamfer sizes don't exceed frame dimensions
    local frameWidth = rightX - leftX
    local frameHeight = topY - bottomY
    
    if DA > frameWidth/2 or DA > frameHeight/2 then
      print(string.format("Warning: Chamfer DA (%.1f) may be too large for frame", DA))
    end
    
    -- Set layer and thickness for frame
    G.setLayer("C_10MM_In")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the chamfered frame using polyline
    -- Start from p1 and go clockwise around the frame
    local framePoints = {
      p1,  -- Bottom line start (after bottom-left chamfer)
      p8,  -- Bottom line end (before bottom-right chamfer)
      p7,  -- Bottom-right chamfer start
      p6,  -- Right line start (after bottom-right chamfer)
      p5,  -- Right line end (before top-right chamfer)
      p4,  -- Top-right chamfer start  
      p3,  -- Top line start (after top-right chamfer)
      p2,  -- Top line end (before top-left chamfer)
      p1   -- Close the frame (back to start)
    }
    
    -- Create the chamfered frame
    G.polyline(table.unpack(framePoints))
    
    print(string.format("Chamfered frame created:"))
    print(string.format("  Frame bounds: (%.1f,%.1f) to (%.1f,%.1f)", leftX, bottomY, rightX, topY))
    print(string.format("  Frame size: %.1f x %.1f mm", frameWidth, frameHeight))
    print(string.format("  Corner chamfers: DA=%.1f, DB=%.1f, DC=%.1f, DD=%.1f", DA, DB, DC, DD))
    
    return true
  end
  
  -- Call the rectangle chamfer frame function
  local success = rectangle_chamfer_frame(A, B, C, D, DA, DB, DC, DD, -Z, referenceCorner)
  
  if success then
    local refNames = {
      [1] = "top-left", [2] = "top-center", [3] = "top-right",
      [4] = "middle-left", [5] = "center", [6] = "middle-right",
      [7] = "bottom-left", [8] = "bottom-center", [9] = "bottom-right"
    }

    print(string.format("Rectangle chamfer frame created with parameters:"))
    print(string.format("  Margins: A=%d, B=%d, C=%d, D=%d mm", A, B, C, D))
    print(string.format("  Chamfers: DA=%d, DB=%d, DC=%d, DD=%d mm", DA, DB, DC, DD))
    print(string.format("  Reference corner: %d (%s)", referenceCorner, refNames[referenceCorner] or "unknown"))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a rectangular frame with chamfered corners")
    print("  - Each corner is cut at 45-degree angle")
    print("  - Independent chamfer size for each corner")
    print("  - Frame margins can be different on each side")
    print("  - Frame positioned relative to specified reference corner")
    print("")
    print("Reference corner options:")
    print("  1=top-left, 2=top-center, 3=top-right")
    print("  4=middle-left, 5=center, 6=middle-right")
    print("  7=bottom-left, 8=bottom-center, 9=bottom-right")
    print("")
    print("Applications:")
    print("  - Industrial design with beveled edges")
    print("  - Modern furniture with angular aesthetics")
    print("  - Technical/mechanical appearance")
    print("  - Frames requiring precise corner cuts")
  end
  
  return true
end

require "ADekoDebugMode"
