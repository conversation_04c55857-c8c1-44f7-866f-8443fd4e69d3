<template>
  <div class="layer-panel">
    <div class="panel-header">
      <span class="text-sm font-medium text-gray-700">{{ $t('layers.title') }}</span>
      <div class="header-controls">
        <button
          @click="toggleAllLayers"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="allLayersVisible ? $t('layers.hideAll') : $t('layers.showAll')"
        >
          <Eye v-if="allLayersVisible" :size="14" />
          <EyeOff v-else :size="14" />
        </button>
        <button
          @click="$emit('close')"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="$t('common.close')"
        >
          <X :size="14" />
        </button>
      </div>
    </div>
    
    <div class="layer-list">
      <div
        v-for="layer in layers"
        :key="layer.name"
        class="layer-item"
        :class="{ 'layer-hidden': !layer.visible }"
      >
        <div class="layer-toggle">
          <button
            @click="toggleLayer(layer.name)"
            class="toggle-btn"
            :class="{ 'visible': layer.visible, 'hidden': !layer.visible }"
            :title="layer.visible ? $t('layers.hide') : $t('layers.show')"
          >
            <Eye v-if="layer.visible" :size="16" />
            <EyeOff v-else :size="16" />
          </button>
        </div>
        
        <div class="layer-info">
          <div class="layer-name">
            {{ layer.name }}
            <span v-if="layer.isDoorPanel" class="door-panel-badge">{{ $t('layers.doorPanel') }}</span>
            <span v-if="layer.face" class="face-badge" :class="`face-${layer.face}`">
              {{ layer.face === 'top' ? $t('layers.topFace') : $t('layers.bottomFace') }}
            </span>
          </div>
          <div class="layer-details">
            <span class="command-count">{{ layer.commandCount }} {{ $t('layers.commands') }}</span>
            <span v-if="layer.category" class="category-info" :class="`category-${layer.category}`">
              {{ getCategoryDisplayName(layer.category) }}
            </span>
            <span v-if="layer.toolInfo" class="tool-info">
              <component :is="getToolIcon(layer.toolInfo.shape)" :size="12" />
              {{ layer.toolInfo.name }}
            </span>
          </div>
        </div>
        
        <div class="layer-color" :style="{ backgroundColor: layer.color }"></div>
      </div>
      
      <div v-if="layers.length === 0" class="no-layers">
        <span class="text-sm text-gray-500">{{ $t('layers.noLayers') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Eye, EyeOff, X, Circle, Square, Triangle } from 'lucide-vue-next'

export interface LayerInfo {
  name: string
  visible: boolean
  commandCount: number
  color: string
  category?: string
  face?: string
  isDoorPanel?: boolean
  toolInfo?: {
    name: string
    shape: string
    diameter: number
  }
}

interface Props {
  layers: LayerInfo[]
}

interface Emits {
  (e: 'toggle-layer', layerName: string): void
  (e: 'toggle-all-layers', visible: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const allLayersVisible = computed(() => {
  return props.layers.length > 0 && props.layers.every(layer => layer.visible)
})

const toggleLayer = (layerName: string) => {
  emit('toggle-layer', layerName)
}

const toggleAllLayers = () => {
  const newVisibility = !allLayersVisible.value
  emit('toggle-all-layers', newVisibility)
}

const getToolIcon = (shape: string) => {
  switch (shape) {
    case 'ball':
    case 'spherical':
      return Circle
    case 'square':
    case 'rectangular':
      return Square
    case 'v-bit':
    case 'chamfer':
      return Triangle
    default:
      return Circle
  }
}

const getCategoryDisplayName = (category: string) => {
  const categoryNames: Record<string, string> = {
    structural: 'Structural',
    cutting: 'Cutting',
    vbit: 'V-Bit',
    ballnose: 'Ball Nose',
    radial: 'Radial',
    special: 'Special',
    drilling: 'Drilling',
    tool: 'Tool',
    other: 'Other'
  }
  return categoryNames[category] || category
}
</script>

<style scoped>
.layer-panel {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg;
  width: 280px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  @apply flex items-center justify-between px-3 py-2 bg-gray-50 border-b border-gray-200 rounded-t-lg;
}

.header-controls {
  @apply flex items-center space-x-1;
}

.layer-list {
  @apply flex-1 overflow-y-auto;
  max-height: 320px;
}

.layer-item {
  @apply flex items-center px-3 py-2 border-b border-gray-100 hover:bg-gray-50 transition-colors;
}

.layer-item.layer-hidden {
  @apply opacity-60;
}

.layer-toggle {
  @apply flex-shrink-0 mr-3;
}

.toggle-btn {
  @apply p-1 rounded transition-colors;
}

.toggle-btn.visible {
  @apply text-blue-600 hover:bg-blue-100;
}

.toggle-btn.hidden {
  @apply text-gray-400 hover:bg-gray-100;
}

.layer-info {
  @apply flex-1 min-w-0;
}

.layer-name {
  @apply text-sm font-medium text-gray-700 font-mono flex items-center gap-2 flex-wrap;
}

.door-panel-badge {
  @apply bg-amber-100 text-amber-800 text-xs px-2 py-0.5 rounded-full font-medium;
}

.face-badge {
  @apply text-xs px-2 py-0.5 rounded-full font-medium;
}

.face-top {
  @apply bg-blue-100 text-blue-800;
}

.face-bottom {
  @apply bg-green-100 text-green-800;
}

.layer-details {
  @apply flex items-center space-x-2 text-xs text-gray-500 mt-1 flex-wrap gap-1;
}

.command-count {
  @apply font-mono;
}

.category-info {
  @apply text-xs px-2 py-0.5 rounded font-medium;
}

.category-structural {
  @apply bg-amber-100 text-amber-700;
}

.category-cutting {
  @apply bg-red-100 text-red-700;
}

.category-vbit {
  @apply bg-purple-100 text-purple-700;
}

.category-ballnose {
  @apply bg-blue-100 text-blue-700;
}

.category-radial {
  @apply bg-green-100 text-green-700;
}

.category-special {
  @apply bg-orange-100 text-orange-700;
}

.category-drilling {
  @apply bg-cyan-100 text-cyan-700;
}

.category-tool {
  @apply bg-indigo-100 text-indigo-700;
}

.category-other {
  @apply bg-gray-100 text-gray-700;
}

.tool-info {
  @apply flex items-center space-x-1;
}

.layer-color {
  @apply w-3 h-3 rounded border border-gray-300 flex-shrink-0 ml-2;
}

.no-layers {
  @apply flex items-center justify-center py-8;
}
</style>
