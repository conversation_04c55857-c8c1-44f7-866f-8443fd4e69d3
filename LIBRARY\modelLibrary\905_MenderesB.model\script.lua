-- ADekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  marginX = 50
  marginY = 50
  structureSizeX = 100
  structureSizeY = 100
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local finalDepth = 8
  
  G.setLayer("K_AciliV60")
  G.setThickness(-finalDepth)
  G.menderesB(marginX, marginY, structureSizeX, structureSizeY)
  
  return true
end

require "ADekoDebugMode"