-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
   
  ust = 75
  sol = 75
  sag = 75
  ara = 150
  a = 0 --<PERSON><PERSON><PERSON> Vbite
  alt = 100
  KD = 450
  altH = 100 
  
  b = 0 --Vbitten Dıs kanala
  h = 35 -- Vbitten göbeğe
  d = 20 --Göbekten iç kanala
  ad = 6 -- Vbit derinliği (sunkenDepth)
  aV = 120    --Vbit Açısı
  cW = 20     -- Tarama Bıçak Çapı
  cT = 8      -- İnce bıçak çapı (Köşe Temizleme)
  gaps = 30
  sbt = 15		
  
  extGrooveExist    = 2  --<PERSON><PERSON><PERSON> kanal var mı (Var: Derinlik / Yok: 0)
  extGrvExtExist	= 0 --dış kanal var mı
  extGrvBorForV	= 1		--Dış kanal1 Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)  
  bottomGrvExist = 0 --alt kısımda kanal 1 var 0 yok
  
  cabCoreExist     = 1   --Göbek var mı
  intRectGrvExist = 2  --iç dikdörtgen kanal var mı (Var: Derinlik / Yok: 0)
  intGrvExist  = 1	 --iç kanal var mı (Var: Derinlik / Yok: 0)
  grooveVert_Hor = 1 	--intGrvExist varsa çalışır--1 vertical 0 horizontal
  shapeToolExist = 4   -- Göbek Desen bıçağı var mı?(Var: Derinlik / Yok: 0)
  
  extEdgeToolExist = 0  --Dış kenar Pah işlemi var mı
  extEdgeVorR 		= 0  --Dış kenar Pah işlemi V mi? Raduslu mu? (V:0/R:1)  
  edgeCornerRExist = 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
   
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  
  local xMin = minimum
  local yMin = minimum

    
  local D = edgeCornerRExist
  local B = math.tan(math.pi/8)
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist > 0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
  
  	if a>0 then
		ust = a
		sol = a
		sag = a
		ara = a
	end
	 
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
  
	if KD >0 then
		altH = KD-ara/2-alt
	end
  
  
	local limitX = sol+sag
	local limitY = alt+altH+ara+ust
	local ustH = Y-(alt+altH+ara+ust)
	local sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
	local gKanalMesafesi = 0
	  
	if X<limitX or Y<limitY then
		print("Part dimension too small")
		return true
	end
	
	if altH < (2*sunkenWidth+2*h+2*d) or ustH < (2*sunkenWidth+2*h+2*d) then
		print("Part dimension too small")
		return true
	end
	
  
	if intRectGrvExist > 0 and intGrvExist > 0 then
		gKanalMesafesi = d
	end
	
	local gobekGenislik = X-sol-2*sunkenWidth-2*h-2*sbt-2*gKanalMesafesi-sag		--ustX
	local gobekGenislik2 = ustH-2*sunkenWidth-2*h-2*sbt-2*gKanalMesafesi			--ustY
	local gobekGenislik3 = altH-2*sunkenWidth-2*h-2*sbt-2*gKanalMesafesi			--altY 
	
	--dikey çizgiler UST
	local stepX = math.floor(gobekGenislik/gaps)		
	
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local aprxGaps = gobekGenislik/(stepX+1)
	local e = sol+sunkenWidth+h+sbt+aprxGaps/2+gKanalMesafesi
	
	--dikey çizgiler ALT
	
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local e1 = sol+sunkenWidth+h+sbt+aprxGaps/2+gKanalMesafesi
	
	--yatay çizgiler UST
	local stepY = math.floor(gobekGenislik2/gaps)
	
	if ((gobekGenislik2/gaps)-stepY)  >= 0.5 then
		stepY = stepY + 1
	end
	
	local aprxGaps2 = gobekGenislik2/(stepY+1)
	local e2 = alt+altH+ara+sunkenWidth+h+sbt+aprxGaps2/2+gKanalMesafesi
	
	--yatay çizgiler ALT
	
	local stepYalt = math.floor(gobekGenislik3/gaps)
	
	if ((gobekGenislik3/gaps)-stepYalt)  >= 0.5 then		
		stepYalt = stepYalt + 1
	end
	
	local aprxGaps3 = gobekGenislik3/(stepYalt+1)
	local e3 = alt+sunkenWidth+h+sbt+aprxGaps3/2+gKanalMesafesi
	
	
	    
	G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	G.setThickness(0)
	local corner1, corner2 = G.sunkenFrame({sol, alt+altH+ara}, {X-sag, Y-ust}, ad, aV, 60)
	corner1[3] = 0
	corner2[3] = 0
	local corner3, corner4 = G.sunkenFrame({sol, alt}, {X-sag, alt+altH}, ad, aV, 60)
	corner3[3] = 0
	corner4[3] = 0
	
	if extEdgeToolExist  > 0 then
		if extEdgeVorR == 0 then --  (V:0/R:1)
			G.setLayer("K_AciliV_Pah")	
		elseif extEdgeVorR ==1 then
			G.setLayer("H_Raduslu_Pah_" .. "DIS")	
		end	
		G.setThickness(-extEdgeToolExist)
		G.rectangle({0,0},{X,Y})
	end
	
	if extGrooveExist  > 0 then
		if extGrvBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			G.setLayer("K_Ballnose")
		elseif extGrvBorForV == 1 then
			G.setLayer("K_Freze")  
		elseif extGrvBorForV == 2 then
			G.setLayer("K_AciliV")  
		end	
		G.setThickness(-extGrooveExist)
		distanceSol = sol-b
		distanceSag = sag-b
		distance1Ust = ust-b			--ust bolum
		distance1Alt = alt+altH+ara-b
		distance2Ust = ust+ustH+ara-b			--alt bolum
		distance2Alt = alt-b
	
		if extGrvExtExist == 1 then
			G.line({distanceSol, distance1Alt}, {distanceSol, Y})	--ust
			G.line({X-distanceSag, distance1Alt}, {X-distanceSag, Y})
			
			G.line({distanceSol, 0}, {distanceSol, Y-distance2Ust})	--alt
			G.line({X-distanceSag, 0}, {X-distanceSag, Y-distance2Ust})
			
			G.line({distanceSol, distance1Alt}, {X-distanceSag, distance1Alt})			--ust bolum alt cızgı
			G.line({distanceSol, Y-distance1Ust}, {X-distanceSag, Y-distance1Ust})		--ust bolum ust cızgı
			
			G.line({distanceSol, distance2Alt}, {X-distanceSag, distance2Alt})			--alt bolum alt cızgı
			G.line({distanceSol, Y-distance2Ust}, {X-distanceSag, Y-distance2Ust})		--alt bolum ust cızgı
		else
			point1 = {distanceSol, distance1Alt}
			point2 = {X-distanceSag, Y-distance1Ust}
			G.rectangle(point1,point2)
			point3 = {distanceSol, distance2Alt}
			point4 = {X-distanceSag, Y-distance2Ust}
			G.rectangle(point3,point4)
		end
		
	end
	
	if cabCoreExist == 1 then
	
		if h<cW and h>cT then
			cW = cT
		end
		
		if h > cT then
			-- if X>xLimit and Y>yLimit then
			G.setLayer("K_Freze" .. cW .. "mm")  -- DEEP cleanup
			G.setThickness(-ad)
			distanceSol = sol+sunkenWidth+cW/2		
			distanceSag = X-(sag+sunkenWidth+cW/2)		
			distance1Ust = Y-(ust+sunkenWidth+cW/2)			--ust bolum
			distance1Alt = Y-(ust+ustH-sunkenWidth-cW/2)
			distance2Ust = alt+altH-sunkenWidth-cW/2			--alt bolum
			distance2Alt = alt+sunkenWidth+cW/2
			
			point1 = {distanceSol, distance1Ust}
			point2 = {distanceSag, distance1Alt}
			G.rectangle(point1,point2)
			point3 = {distanceSol, distance2Ust}
			point4 = {distanceSag, distance2Alt}
			G.rectangle(point3,point4)
			
			if h>cW then
				distanceSol = sol+sunkenWidth+h-cW/2		
				distanceSag = X-(sag+sunkenWidth+h-cW/2)		
				distance1Ust = Y-(ust+sunkenWidth+h-cW/2)		--ust bolum
				distance1Alt = Y-(ust+ustH-sunkenWidth-h+cW/2)
				distance2Ust = alt+altH-sunkenWidth-h+cW/2			--alt bolum
				distance2Alt = alt+sunkenWidth+h-cW/2
				
				point5 = {distanceSag, distance1Alt}
				point6 = {distanceSol, distance1Ust}
				--G.rectangle(point5,point6)
				k = (h-cW)/(cW/2)
				for i=1, k, 1 do
					point1 = G.ptAdd(point1,{cW/2,-cW/2})
					point2 = G.ptSubtract(point2, {cW/2,-cW/2})
					point3 = G.ptAdd(point3,{cW/2,-cW/2})
					point4 = G.ptSubtract(point4, {cW/2,-cW/2})
					if point1[1]>point5[1]-cW/2 then
						break
					end
					G.rectangle(point1,point2)
					G.rectangle(point3,point4)
				end
			end
			
			G.setThickness(-(ad))
			distanceSol = sol+sunkenWidth+h-cW/2		
			distanceSag = X-(sag+sunkenWidth+h-cW/2)		
			distance1Ust = Y-(ust+sunkenWidth+h-cW/2)		--ust bolum
			distance1Alt = Y-(ust+ustH-sunkenWidth-h+cW/2)
			distance2Ust = alt+altH-sunkenWidth-h+cW/2			--alt bolum
			distance2Alt = alt+sunkenWidth+h-cW/2
			
			point1 = {distanceSol, distance1Ust}
			point2 = {distanceSag, distance1Alt}
			G.rectangle(point1,point2)
			point3 = {distanceSol, distance2Ust}
			point4 = {distanceSag, distance2Alt}
			G.rectangle(point3,point4)
			
			if shapeToolExist  > 0 then  ----Göbekte desen bıçağı varsa
				G.setLayer("K_Desen")  -- DEEP cleanup
				G.setThickness(-shapeToolExist)
				
				distanceSol = sol+sunkenWidth+h	
				distanceSag = X-(sag+sunkenWidth+h)	
				distance1Ust = Y-(ust+sunkenWidth+h)		--ust bolum
				distance1Alt = Y-(ust+ustH-sunkenWidth-h)
				distance2Ust = alt+altH-sunkenWidth-h		--alt bolum
				distance2Alt = alt+sunkenWidth+h
				
				point1 = {distanceSol, distance1Ust}
				point2 = {distanceSag, distance1Alt}
				G.rectangle(point1,point2)
				point3 = {distanceSol, distance2Ust}
				point4 = {distanceSag, distance2Alt}
				G.rectangle(point3,point4)
			else 
				G.setLayer("K_AciliV" .. aV)  --- Gobek
				G.setThickness(-ad)
				
				distanceSol = sol+sunkenWidth+h	
				distanceSag = X-(sag+sunkenWidth+h)	
				distance1Ust = Y-(ust+sunkenWidth+h)		--ust bolum
				distance1Alt = Y-(ust+ustH-sunkenWidth-h)
				distance2Ust = alt+altH-sunkenWidth-h		--alt bolum
				distance2Alt = alt+sunkenWidth+h
				
				point1 = {distanceSol, distance1Ust}
				point2 = {distanceSag, distance1Alt}
				G.rectangle(point1,point2)
				point3 = {distanceSol, distance2Ust}
				point4 = {distanceSag, distance2Alt}
				G.rectangle(point3,point4)
			end
		end
		if h >= 0 then
	
			if intRectGrvExist  > 0 then      ------İç kanal varsa
				local check = sol + sag + 2*h + 2*d
				if X < check or Y < check then
					print("Part dimension too small, check a + h + d value")
					return true
				end
				G.setLayer("K_I_Kanal")  -- DEEP cleanup
				G.setThickness(-intRectGrvExist)
				
				distanceSol = sol+sunkenWidth+h	+ d
				distanceSag = X-(sag+sunkenWidth+h+d)	
				distance1Ust = Y-(ust+sunkenWidth+h+d)		--ust bolum
				distance1Alt = Y-(ust+ustH-sunkenWidth-h-d)
				distance2Ust = alt+altH-sunkenWidth-h-d		--alt bolum
				distance2Alt = alt+sunkenWidth+h+d
				
				point1 = {distanceSol, distance1Ust}
				point2 = {distanceSag, distance1Alt}
				G.rectangle(point1,point2)
				point3 = {distanceSol, distance2Ust}
				point4 = {distanceSag, distance2Alt}
				G.rectangle(point3,point4)
				
			end
			
			if intGrvExist > 0 then      ------İç kanal varsa
				if grooveVert_Hor == 1 then			--1 dikey
					G.setLayer("K_I_Kanal")  
					G.setThickness(-intGrvExist)
	
					for i=0,stepX do
						local x = e+i*aprxGaps
						local offsetUst = ust + sunkenWidth + h + gKanalMesafesi
						local offsetAlt = alt + altH + ara + sunkenWidth + h + gKanalMesafesi
						point3 = {x,offsetAlt}
						point4 = {x,Y-offsetUst}  
						G.line(point3,point4,0)
						
						if bottomGrvExist > 0 then
							local x1 = e1+i*aprxGaps
							local offsetUst1 = ust + ustH + ara + sunkenWidth + h + gKanalMesafesi
							local offsetAlt1 = alt + sunkenWidth + h + gKanalMesafesi
							point5 = {x1,offsetAlt1}
							point6 = {x1,Y-offsetUst1}  
							G.line(point5,point6,0)
						end
						
						i = i+1
					end 
					
				elseif grooveVert_Hor == 0 then		--0 yatay
					G.setLayer("K_I_Kanal")  
					G.setThickness(-intGrvExist)
					
					for i=0,stepY do							--ust yatay
						local y = e2+i*aprxGaps2
						local offsetSol = sol + sunkenWidth + h + gKanalMesafesi
						local offsetSag = sag + sunkenWidth + h + gKanalMesafesi
						point3 = {offsetSol,y}
						point4 = {X-offsetSag,y}  
						G.line(point3,point4,0)
						i = i+1
					end 
				
					if bottomGrvExist > 0 then					--alt yatay
						for i=0,stepYalt do
							local y3 = e3+i*aprxGaps3
							local offsetSol3 = sol + sunkenWidth + h + gKanalMesafesi
							local offsetSag3 = sag + sunkenWidth + h + gKanalMesafesi
							point5 = {offsetSol3,y3}
							point6 = {X-offsetSag3,y3}  
							G.line(point5,point6,0)
							i = i+1
						end 
					end
					
				end
			end
		end
	else    -------------- Göbek yoksa tüm kapaklarda
		G.setLayer("Cep_Acma".. cW .. "mm")  -- DEEP cleanup
		G.setThickness(-ad)
		distanceSol = sol+sunkenWidth
		distanceSag = sag+sunkenWidth
		distance1ust = ust+sunkenWidth
		distance1alt = alt + altH + ara + sunkenWidth
		point1 = {distanceSol, distance1alt}
		point2 = {X-distanceSag, Y-distance1ust}
		G.rectangle(point1,point2)					--ust desen alan tarama
		distance2ust = ust+ustH+ara+sunkenWidth
		distance2alt = alt + sunkenWidth
		point3 = {distanceSol, distance2alt}
		point4 = {X-distanceSag, Y-distance2ust}
		G.rectangle(point3,point4)					--alt desen alan tarama
	end
	
	G.setLayer("K_TarKoseTemizl" .. cT .. "mm")
	if h >= cT and h < 3*cT and cabCoreExist == 1 then
		G.setThickness(-ad)
		distanceSol = sol+sunkenWidth+cT/2
		distanceSag = sag+sunkenWidth+cT/2
		distance1ust = ust+sunkenWidth+cT/2
		distance1alt = alt + altH + ara + sunkenWidth + cT/2
		point1 = {distanceSol, distance1alt}
		point2 = {X-distanceSag, Y-distance1ust}
		G.rectangle(point1,point2)
		distance2ust = ust+ustH+ara+sunkenWidth+cT/2
		distance2alt = alt + sunkenWidth+cT/2
		point3 = {distanceSol, distance2alt}
		point4 = {X-distanceSag, Y-distance2ust}
		G.rectangle(point3,point4)
	elseif h >= 3*cT or cabCoreExist == 0 then
		G.setThickness(0)
		G.cleanCorners(corner1,corner2,ad,cT)
		G.cleanCorners(corner3,corner4,ad,cT)
	end
  
  G.list()
  return true
end

require "ADekoDebugMode"
