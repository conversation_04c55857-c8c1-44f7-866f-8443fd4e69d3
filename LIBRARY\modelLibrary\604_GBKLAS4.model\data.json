{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV_aV\t\taV degiskeni ile acisi verilen V bicak \nK_Freze_cW_mm\t\tcW Capli Freze bicagi \nK_Freze_cT_mm\t\tcT Capli Freze bicagi \nK_Desen\t\tVarsa desen-motif bicagi \nK_Kanal\t\t DIS kanal islemi icin kullanilacak bir bicak \nPANEL\t\tEbatlama bicagi\n-----------------------------------------------------------------------------------------", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 10, "description": "Acili V kenardan DIS kanala mesafe", "parameterName": "b"}, {"defaultValue": 30, "description": "Acili V kenardan gobege mesafe", "parameterName": "h"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 90, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 6, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 0, "description": "DIS kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 5, "description": "Gobek desen bicak islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "shapeToolExist"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}