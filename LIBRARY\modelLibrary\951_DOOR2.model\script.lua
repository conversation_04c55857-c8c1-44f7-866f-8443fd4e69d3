-- ADekoCA<PERSON>, Model Script Yatay cızgılı
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  
	G = ADekoLib
	
		
	a = 100
	ust   	= 0
	alt	= 0
	sag = 100
	sol = 50
	ad = 2 -- toolDepth kanal derinliği
	howMany = 0
	gaps = 50
	cT = 8
	aV = 120

  sideGrooveExist = 2    --Dik kanallar var mı? (Var: Derinlik / Yok: 0)
  GrvBorForV	= 0		--Dik kanallar Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)
  
  extEdgeToolExist 	= 0  --Dış kenar Pah işlemi var mı? (Var: Derinlik / Yok: 0)
  extEdgeVorR 		= 0  --<PERSON><PERSON><PERSON> kenar Pah işlemi V mi? Raduslu mu? (V:0/R:1)
  edgeCornerRExist 	= 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
   
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end

	local xMin 	= 140
	local yMin 	= 140
	
	if a > 0 or a < 0 then
		sag = a
		sol = a
	end
  local calcHeigth = 0
  local calcNum = 0
  
  function HowMany(calcNum,calcNumC)
      if (calcNum-calcNumC) >= 0.5 then
        calcNum = calcNumC+1
      else
        calcNum = calcNumC
      end
    return calcNum
  end
  if howMany == 0 and gaps == 0 then 
    return false
  end
  
  if howMany >0 then
    if ust == 0 and alt ==0 then
      calcHeigth = (Y)/(howMany+1)
      alt = calcHeigth
    else
      calcHeigth = (Y-ust-alt)/(howMany-1)
    end
  else
    calcNum = (((Y-ust-alt)/gaps)+1)
    calcNumC = math.floor(calcNum)
    if ust == 0 and alt ==0 then
      --buradayım
      howMany = HowMany(calcNum,calcNumC)-2
      calcHeigth = (Y)/(howMany+1)
      alt = calcHeigth
    else
      howMany = HowMany(calcNum,calcNumC)
      calcHeigth = (Y-ust-alt)/(howMany-1)
    end
  end
	
	bulge = math.tan(math.pi/8)
	local D = edgeCornerRExist
	local B = math.tan(math.pi/8)
	
	G.setThickness(-materialThickness)
	G.setFace("ust")
	if edgeCornerRExist > 0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	
	if extEdgeToolExist  > 0 then
		if extEdgeVorR == 0 then --  (V:0/R:1)
			G.setLayer("K_AciliV_Pah")	
		elseif extEdgeVorR ==1 then
			G.setLayer("H_Raduslu_Pah_" .. "DIS")	
		end
		G.setThickness(-extEdgeToolExist)
		G.rectangle({0,0},{X,Y})
	end

  if GrvBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
		G.setLayer("K_Ballnose"..cT.."mm")
	elseif GrvBorForV == 1 then
		G.setLayer("K_Freze"..cT.."mm")  
	elseif GrvBorForV == 2 then
		G.setLayer("K_AciliV"..aV)  
	end	
    
  if sideGrooveExist>0 then 
    G.setThickness(-sideGrooveExist)
    point1 = {sol,0}
    point12 = {sol,Y}
    point2 = {X-sag,0}
    point22 = {X-sag,Y}
    
    if sol == 0 and sag >0 then
      G.line(point2,point22,0)
    elseif sag == 0 and sol >0 then	
      G.line(point1,point12,0)
    elseif sol == 0 and sag == 0 then
      -- yan kanal yok
    elseif sol >0 and sag >0 then
      G.line(point1,point12,0)
      G.line(point2,point22,0)
    end
  end

  G.setThickness(-ad)
  for i=0,howMany-1 do
  local y = alt+i*calcHeigth
    --point3 = {x,a}
    --point4 = {x,Y-a} 
  point3 = {sol,y}
  point4 = {X-sag,y}  
  G.line(point3,point4,0)
  i = i+1
  end
	
	
  
  return true
end  
  
require "ADekoDebugMode"