{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV-K_Ballnose-K_Freze \t\t*cT* capli Ballnose, freze veya *aV* derece V bicak\nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 247, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "offset from the edges", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 3, "description": "<PERSON><PERSON> derin<PERSON>gi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili vbit acisi", "parameterName": "aV"}, {"defaultValue": 5, "description": "<PERSON><PERSON> baslangic-kenar arasi mesafe", "parameterName": "b"}, {"defaultValue": 30, "description": "<PERSON><PERSON>dan ic kanala mesafe", "parameterName": "c"}, {"defaultValue": 8, "description": "Kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 0, "description": "Kanala ilk dalis derinligi", "parameterName": "surface"}, {"defaultValue": 1, "description": "Ic kanal islemi var mi?(Var :1/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 2, "description": "Kanal :Ballnose mu? Flat mi? V kanal mi?(B:0/F:1/V:2)", "parameterName": "grooveBorForV"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}