{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV*\t\t*aV* Derece V Bicak \nH_Freze*cW*mm_Ic\t*cW*mm Freze Bicagi \nK_Freze*cT*mm\t\t*cT*mm Freze Bicagi \nCep_Acma\t\tTarama Freze Bicagi \nK_Freze*cW*mm\t\t*cW*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 350, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 40, "description": "width of the blocks on each side", "parameterName": "g"}, {"defaultValue": 40, "description": "space between the inner frame and inner shape", "parameterName": "h"}, {"defaultValue": 90, "description": "Acili vbit acisi", "parameterName": "aV"}, {"defaultValue": 10, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 20, "description": "Cam yeri tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}