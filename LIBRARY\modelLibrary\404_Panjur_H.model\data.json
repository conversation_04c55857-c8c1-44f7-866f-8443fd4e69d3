{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nH_Freze..cT..mm_Ic\t*cT*mm Freze Bicagi\nK_Freze*cW*mm\t\t*cW*mm Freze Bicagi \nK_Freze*cT*mm\t\t*cT*mm Freze Bicagi \nK_Panjur*cW*mm\t\t*cW*mm Panjur Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum door dimensions", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y limit value for narror doors", "parameterName": "limit"}, {"defaultValue": 60, "description": "offset from the edges", "parameterName": "a"}, {"defaultValue": 40, "description": "Width of each block for narrow(below the limit) doors", "parameterName": "aa"}, {"defaultValue": 50, "description": "Inclined pocket height", "parameterName": "b"}, {"defaultValue": 1, "description": "Panjur tool exist?(Yes=1 / No=0)", "parameterName": "panjurVToolExist"}, {"defaultValue": 10, "description": "Rough tool diameter or Panjur Dia, mm", "parameterName": "cW"}, {"defaultValue": 6, "description": "Fine tool diameter, mm. Enter 0 for no fine-tuning", "parameterName": "cT"}, {"defaultValue": 1, "description": "cT panjur side grooves?(Yes=1 / No=0)", "parameterName": "sideGroove"}, {"defaultValue": 3, "description": "Surface machining side step", "parameterName": "step"}, {"defaultValue": 2, "description": "Starting depth, mm", "parameterName": "<PERSON><PERSON><PERSON><PERSON>"}, {"defaultValue": 10, "description": "<PERSON><PERSON><PERSON>, mm", "parameterName": "final<PERSON><PERSON><PERSON>"}, {"defaultValue": 0, "description": "Door edge V chamfer operation exist?(Yes :Depth/ No:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "Corner radius exist?(Yes :Radius/ No:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}