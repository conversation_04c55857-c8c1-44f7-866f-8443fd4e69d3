-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script - Sunken Frame Test
-- Converted from C# azCAM macro: Sunken Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Sunken Frame parameters (from original C# macro)
  A = 9     -- Left margin
  B = 9     -- Top margin
  C = 9     -- Right margin
  D = 9     -- Bottom margin
  DA = 90   -- Angle in degrees (90° = vertical walls)
  DT = 200  -- Depth tolerance (maximum allowed depth)
  Z = 10    -- Depth of sunken area
  
  -- Reference corner (from original parameters.json: reference = 3)
  -- 1=top-left, 2=top-center, 3=top-right, 4=middle-left, 5=center, 6=middle-right, 7=bottom-left, 8=bottom-center, 9=bottom-right
  referenceCorner = 3  -- top-right (original default)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + C + 20   -- minimum required width
  local minHeight = B + D + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Sunken Frame function (converted from C# macro using ADekoLib.sunkenFrame)
  local function sunken_frame(A, B, C, D, DA, DT, Z, refCorner)
    -- Use default values if parameters not provided
    A = A or 9
    B = B or 9
    C = C or 9
    D = D or 9
    DA = DA or 90
    DT = DT or 200
    Z = Z or 10
    refCorner = refCorner or 3

    print(string.format("Creating sunken frame using ADekoLib.sunkenFrame"))
    print(string.format("Margins: A=%d, B=%d, C=%d, D=%d", A, B, C, D))
    print(string.format("Angle: DA=%.1f°, Depth tolerance: DT=%.1f, Depth: Z=%.1f", DA, DT, Z))
    print(string.format("Reference corner: %d", refCorner))

    -- Calculate frame boundaries (following C# logic exactly)
    local firstPoint = {A, D}                    -- Bottom-left corner
    local secondPoint = {width - C, height - B}  -- Top-right corner

    -- Validate frame dimensions
    local frameWidth = secondPoint[1] - firstPoint[1]
    local frameHeight = secondPoint[2] - firstPoint[2]

    if frameWidth <= 0 or frameHeight <= 0 then
      print(string.format("Error: Invalid frame dimensions: %.1f x %.1f", frameWidth, frameHeight))
      return false
    end

    print(string.format("Frame area: (%.1f,%.1f) to (%.1f,%.1f)",
          firstPoint[1], firstPoint[2], secondPoint[1], secondPoint[2]))
    print(string.format("Frame size: %.1f x %.1f mm", frameWidth, frameHeight))

    -- Set layer and thickness for frame
    G.setLayer("sunken")  -- Using original layer name from parameters.json

    -- Use ADekoLib.sunkenFrame function
    -- ADekoLib.sunkenFrame(fp, sp, depth, vToolAngle, vToolDiameter)
    -- We need to provide a reasonable tool diameter for the validation
    local toolDiameter = DT  -- Use depth tolerance as tool diameter

    local innerBottomLeft, innerTopRight = G.sunkenFrame(firstPoint, secondPoint, Z, DA, toolDiameter)

    if innerBottomLeft and innerTopRight then
      print(string.format("Sunken frame created successfully:"))
      print(string.format("  Outer frame: (%.1f,%.1f) to (%.1f,%.1f)",
            firstPoint[1], firstPoint[2], secondPoint[1], secondPoint[2]))
      print(string.format("  Inner area: (%.1f,%.1f) to (%.1f,%.1f)",
            innerBottomLeft[1], innerBottomLeft[2], innerTopRight[1], innerTopRight[2]))
      print(string.format("  Sunken depth: %.1f mm", Z))
      print(string.format("  Tool angle: %.1f degrees", DA))
      print("")
      print("3D sunken frame with beveled edges created using ADekoLib.sunkenFrame")
      return true
    else
      print("Error: ADekoLib.sunkenFrame failed - possibly tool diameter too small")
      return false
    end
  end
  
  -- Call the sunken frame function
  local success = sunken_frame(A, B, C, D, DA, DT, Z, referenceCorner)
  
  if success then
    local refNames = {
      [1] = "top-left", [2] = "top-center", [3] = "top-right",
      [4] = "middle-left", [5] = "center", [6] = "middle-right", 
      [7] = "bottom-left", [8] = "bottom-center", [9] = "bottom-right"
    }
    
    print(string.format("Sunken frame created with parameters:"))
    print(string.format("  Margins: A=%d, B=%d, C=%d, D=%d mm", A, B, C, D))
    print(string.format("  Angle: DA=%.1f degrees", DA))
    print(string.format("  Depth tolerance: DT=%.1f mm", DT))
    print(string.format("  Sunken depth: Z=%.1f mm", Z))
    print(string.format("  Reference corner: %d (%s)", referenceCorner, refNames[referenceCorner] or "unknown"))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a true 3D sunken frame with angled/beveled sides")
    print("  - Uses ADekoLib.sunkenFrame for proper 3D geometry")
    print("  - Frame depth is controlled by Z parameter")
    print("  - Angle DA controls the bevel steepness")
    print("  - DT provides tool diameter for machining validation")
    print("  - Creates complex 3D beveled transition from surface to sunken area")
    print("")
    print("Technical details:")
    print("  - DA=90° creates vertical walls (no bevel)")
    print("  - DA<90° creates inward-sloping bevels")
    print("  - DA>90° creates outward-sloping bevels")
    print("  - Tool validation: offset = Z * tan(DA/2) must be ≤ toolDiameter/2")
    print("  - Returns inner rectangle coordinates for further operations")
    print("")
    print("Applications:")
    print("  - Recessed panels and frames")
    print("  - 3D decorative elements")
    print("  - Architectural details")
    print("  - Furniture with depth variations")
    print("  - CNC machining with proper tool validation")
  end
  
  return true
end

require "ADekoDebugMode"
