{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV*\t\t*aV* Derece V Bicak \nH_Freze*cT*mm_Ic\t*cT*mm Freze Bicagi \nH_Freze*CT*mm_Dis\t*cT*mm Freze Bicagi \nH_Freze*cW*mm_Ic_SF\t*cW*mm Freze Bicagi \nK_Freze*cW*mm_SF\t*cW*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi ", "modelParameters": [{"defaultValue": 140, "description": "minimum dimension limit on the x axis", "parameterName": "xMin"}, {"defaultValue": 140, "description": "minimum dimension limit on the y axis", "parameterName": "yMin"}, {"defaultValue": 50, "description": "mm, distance from the edge on x axis", "parameterName": "a"}, {"defaultValue": 50, "description": "mm, distance from the edge on x axis", "parameterName": "b"}, {"defaultValue": 100, "description": "mm, diameter of the center circle", "parameterName": "d"}, {"defaultValue": 90, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 20, "description": "Cam yeri tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 5, "description": "Cam yeri kesim bicak capi", "parameterName": "cT"}, {"defaultValue": 14, "description": "mm, window depth from top", "parameterName": "windowDepthFront"}, {"defaultValue": 6, "description": "mm, window depth from bottom", "parameterName": "windowDepthBack"}, {"defaultValue": 5, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}