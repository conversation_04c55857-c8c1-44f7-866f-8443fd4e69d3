{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV_Pah \t\t acili V pah bicagi \nK_Ballnose\t\t Kure uclu kanal bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 2, "description": "<PERSON><PERSON> derin<PERSON>gi", "parameterName": "ad"}, {"defaultValue": 60, "description": "<PERSON><PERSON><PERSON> arasi yakla<PERSON> mesafe", "parameterName": "gaps"}, {"defaultValue": 2, "description": "Ic kanal var mi? (Var :1/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}