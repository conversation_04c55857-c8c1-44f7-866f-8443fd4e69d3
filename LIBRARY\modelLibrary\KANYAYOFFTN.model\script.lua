-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib	
	minimum = 150
  
  yy = 20				-- yay kısmının yüksekliği
  m = 100
  
  K1Off = 40              -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  K1Opr = 1              --Ya<PERSON>lacak islem 0-Bos,1-K_<PERSON><PERSON>, 2-Cep-A<PERSON><PERSON>, 3-K-<PERSON>ken
  K1Depth = 2           -- Kanal Derinligi
  K1ToolNumber = 11 				-- Cap veya Vbit Acisi
  
  K2Off = 5             -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  K2Opr = 3             --Ya<PERSON><PERSON><PERSON>k islem 0-Bo<PERSON>,1-K_<PERSON><PERSON>, 2-Cep-A<PERSON><PERSON>, 3-K-<PERSON><PERSON>
  K2Depth = 5            -- Ka<PERSON> Derinligi
  K2ToolNumber = 90 				-- Cap veya Vbit Acisi
  
  K3Off = 0              -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  K3Opr = 2              --Yapilacak islem 0-<PERSON><PERSON>,1-K_Kanal, 2-Cep-Acma, 3-K-Sunken
  K3Depth = 5            -- Kanal Derinligi
  K3ToolNumber = 12 				-- Cap veya Vbit Acisi
  
  K4Off = 25              -- Kenardan Offset- icteki kanallarda kanaldan offset
  K4Opr = 2              --Yapilacak islem 0-Bos,1-K_Kanal, 2-Cep-Acma, 3-K-Sunken
  K4Depth = 5            -- Kanal Derinligi
  K4ToolNumber = 12				-- Cap veya Vbit Acisi

  K5Off = 0              -- Kenardan Offset- icteki kanallarda kanaldan offset
  K5Opr = 1              --Yapilacak islem 0-Bos,1-K_Kanal, 2-Cep-Acma, 3-K-Sunken
  K5Depth = 3            -- Kanal Derinligi
  K5ToolNumber = 13 				-- Cap veya Vbit Acisi

  K6Off = 5              -- Kenardan Offset- icteki kanallarda kanaldan offset
  K6Opr = 1              --Yapilacak islem 0-Bos,1-K_Kanal, 2-Cep-Acma, 3-K-Sunken
  K6Depth = 6            -- Kanal Derinligi
  K6ToolNumber = 90 				-- Cap veya Vbit Acisi
  
  K7Off = 10              -- Kenardan Offset- icteki kanallarda kanaldan offset
  K7Opr = 1              --Yapilacak islem 0-Bos,1-K_Kanal, 2-Cep-Acma, 3-K-Sunken
  K7Depth = 7            -- Kanal Derinligi
  K7ToolNumber = 120 				-- Cap veya Vbit Acisi

  K8Off = 5              -- Kenardan Offset- icteki kanallarda kanaldan offset
  K8Opr = 1            --Yapilacak islem 0-Bos,1-K_Kanal, 2-Cep-Acma, 3-K-Sunken
  K8Depth = 7            -- Kanal Derinligi
  K8ToolNumber = 150 				-- Cap veya Vbit Acisi
      
  edgeCornerRExist        = 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  extEdgeVtoolExist      	= 0   -- Dış kenar Pah işlemi var mı? derinlik/0:yok
      
  KLineAvailable = 0
  KLineRotation = 1   --yatay:0, dikey:1
  KLineOffset = 60
  KLineDepth = 1.4
  KToolNumber = 55

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  local vWideDiameter = 60
  --local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
  --local sunkenWidth2 = sunkenWidth              --göbekte desen bıçağı yoksa eşittir. Varsa shapetoolexist den bak

  local Dd = edgeCornerRExist
  local Bb = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape(        ----chamfered part shape
      {Dd,0},		
      {X-Dd,0,0,Bb},
      {X,Dd},
      {X,Y-Dd,0,Bb},
      {X-Dd,Y},
      {Dd,Y,0,Bb},
      {0,Y-Dd},
      {0,Dd,0,Bb},
      {Dd,0}
      )
  else
    G.makePartShape()
  end

	if X < xMin or Y < yMin then
	print("Part dimension too small")
	return true
end

  ym = 0
  
	function OffsetHesap(a1,h1,m)
		if m == nil then
		print("---------Offset fonk değişken 'm' nil. Lütfen kontrol edin--------")
		end
		local kiris = (X-2*a1+h1-2*m)/2
		local orta = a1 + m + kiris
		local R = ((kiris*kiris)+(yy*yy))/(2*yy); --yarıcap
		local radian1 = math.asin((R-yy)/R);
		local angle1 = radian1 * (180/math.pi);
		local angle2 = (90-angle1)/2;
		local radian2 = angle2 * (math.pi/180);
		local bb = (math.tan(radian2)) * h1;
		--// var ofkiris = (yaricap/(yaricap+H))*kiris; //ofsetlenen kiris boyu yarisi
		local m2 = m-h1+bb;
		
		if m2 > m then
			local fark = m2-m
			m2 = m-fark
		end
		
		local kiris2 = X-2*(h1+a1)-2*m
		
		if kiris2 < 2*yy then
			print("Orta kiris cok dar")
			return true
		end	
			
		if m2<0 then
			return true
		end
		return a1+h1+m2,m2
	end
	
	
	
	local bulge = G.bulge(
		{X-K1Off-m, Y-K1Off-yy, 0, 0},
		{X/2, Y-K1Off}, {m+K1Off, Y-K1Off-yy}
		)
	
	function BulgePoly(distance1, m, bulge)        --- distance1 soldan mesafe, distance2 6numaranın yukarıdan mesafesi, ust-ust den yaya mesafe, m- 2 ile 3 numara arası mesafe
		local distance2 = distance1+yy
		local points = {
			{X-distance1-m, Y-distance2, 0, bulge},  --1                  2---1-7  
			{distance1+m, Y-distance2, 0, 0},       --2               	3-------6
			{distance1, Y-distance2, 0, 0},       --3                  	---------  
			{distance1, distance1, 0, 0},         --4                 	---------
			{X-distance1, distance1, 0},             --5              	---------  
			{X-distance1, Y-distance2, 0, 0},      --6                	4-------5
			{X-distance1-m, Y-distance2, 0, bulge} --7
			}  
			return points
	end
  
	function Decimals(W)
    W = string.gsub(W,"[.]",",")
    return W
  end

  function Kanallar(offset,operation,ad,W,ym)	-- KENARDAN MESAFE, İSLEM, DERİNLİK, CAP VEYA ACI
    -- ym = ym or 0
    if ym == nil then
		ym = -1
	end
	W=Decimals(W)
	print("kanal fonk ym : ", ym)
	
    G.setThickness(-ad)
    
    if operation == 1 then
      G.setLayer("K_Kanal_TN_"..W)		
      -- G.polylineimp(points)
	  -- G.polylineimp(BulgePoly(offset, m, bulge))
    end
    
    if operation == 2 then
      G.setLayer("Cep_Acma_TN_"..W)		
      -- G.polylineimp(points)
	  
    end
      
    if operation == 3 then
      G.setLayer("V_Oyma_TN"..W)	
      -- G.polylineimp(points)
	  -- G.polylineimp(BulgePoly(offset, m, bulge))
      -- G.sunkenFrameAny(points, 10, -ad, W, 100, true)
    end
	
	if ym == 0 then
		G.polylineimp(BulgePoly(offset, m, bulge))
	elseif ym > 0 then
		G.polylineimp(BulgePoly(offset, ym, bulge))
	else
		print("Ym sıfırın altında")
	end
	--return
  end
    
  local kenar1 = K1Off
  local kenar2 = K1Off+K2Off
  local kenar3 = K1Off+K2Off+K3Off
  local kenar4 = K1Off+K2Off+K3Off+K4Off
  local kenar5 = K1Off+K2Off+K3Off+K4Off+K5Off
  local kenar6 = K1Off+K2Off+K3Off+K4Off+K5Off+K6Off
  local kenar7 = K1Off+K2Off+K3Off+K4Off+K5Off+K6Off+K7Off
  local kenar8 = K1Off+K2Off+K3Off+K4Off+K5Off+K6Off+K7Off+K8Off
  ya= 0
  
  Kanallar(kenar1,K1Opr,K1Depth,K1ToolNumber,0)

	if K2Opr>0 then
		if (X-2*(kenar2+m))> 0 and (Y-2*(kenar2))> 0 then
		ya,ym = OffsetHesap(kenar1,kenar2-kenar1,m)
			Kanallar(kenar2,K2Opr,K2Depth,K2ToolNumber,ym)
		end
		
		if K3Opr>0 then
			if (X-2*(kenar3))> 0 and (Y-2*(kenar3))> 0 then
			ya,ym = OffsetHesap(kenar1,kenar3-kenar1,m)
			Kanallar(kenar3,K3Opr,K3Depth,K3ToolNumber,ym)
			end
			if K4Opr>0 then
				if (X-2*(kenar4))> 0 and (Y-2*(kenar4))> 0 then
				ya,ym = OffsetHesap(kenar1,kenar4-kenar1,m)
				Kanallar(kenar4,K4Opr,K4Depth,K4ToolNumber,ym)
				end
				if K5Opr>0 then
					if (X-2*(kenar5))> 0 and (Y-2*(kenar5))> 0 then
					ya,ym = OffsetHesap(kenar1,kenar5-kenar1,m)
					Kanallar(kenar5,K5Opr,K5Depth,K5ToolNumber,ym)
					end
					if K6Opr>0 then
						if (X-2*(kenar6))> 0 and (Y-2*(kenar6))> 0 then
						ya,ym = OffsetHesap(kenar1,kenar6-kenar1,m)
						Kanallar(kenar6,K6Opr,K6Depth,K6ToolNumber,ym)
						end
						if K7Opr>0 then
							if (X-2*(kenar7))> 0 and (Y-2*(kenar7))> 0 then
							ya,ym = OffsetHesap(kenar1,kenar7-kenar1,m)
							Kanallar(kenar7,K7Opr,K7Depth,K7ToolNumber,ym)
							end
							if K8Opr>0 then
								if (X-2*(kenar8))> 0 and (Y-2*(kenar8))> 0 then
								ya,ym = OffsetHesap(kenar1,kenar8-kenar1,m)
								Kanallar(kenar8,K8Opr,K8Depth,K8ToolNumber,ym)
								end  
							end
						end
					end
				end
			end
		end
	end
  
  if KLineAvailable == 1 then
    G.setLayer("K_Cizgi_TN"..KToolNumber)	
    G.setThickness(-KLineDepth)
    if KLineRotation == 0 then
      G.line({0, KLineOffset}, {X, KLineOffset})
      G.line({0, Y - KLineOffset}, {X, Y-KLineOffset})
    end
    if KLineRotation == 1 then
      G.line({KLineOffset, 0},{KLineOffset, Y})
      G.line({X-KLineOffset, 0}, {X-KLineOffset, Y})
    end
  end
  
  return true
end


----------------------------------------------
require "ADekoDebugMode"