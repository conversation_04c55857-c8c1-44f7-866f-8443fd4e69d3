@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
}

/* Monaco Editor Styles */
.monaco-editor {
  font-family: '<PERSON>sol<PERSON>', 'Courier New', 'Monaco', monospace !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
  font-weight: 400 !important;
  letter-spacing: 0.5px !important;
}

.monaco-editor .view-lines {
  line-height: 1.6 !important;
}

.monaco-editor .margin {
  background-color: #1e1e1e !important;
}

.monaco-editor .margin-view-overlays .line-numbers {
  color: #858585 !important;
  font-size: 14px !important;
}

/* Ensure Monaco Editor container has proper dimensions */
.monaco-editor-container {
  width: 100% !important;
  height: 100% !important;
  min-height: 0 !important;
  flex: 1 1 auto !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

/* Ensure Monaco Editor itself fills the container */
.monaco-editor-container .monaco-editor {
  width: 100% !important;
  height: 100% !important;
}

/* Enhanced text rendering for better readability - removed color overrides */
.monaco-editor .view-line {
  font-size: 16px !important;
  line-height: 1.6 !important;
}

/* Dark theme enhancements - removed color overrides */
.monaco-editor.vs-dark {
  background-color: #1e1e1e !important;
}

/* Enhanced syntax highlighting - let Monaco handle colors, we just add styling */
.monaco-editor .mtk5 { /* Keywords */
  font-weight: bold !important;
}

.monaco-editor .mtk6 { /* Strings */
  font-style: normal !important;
}

.monaco-editor .mtk7 { /* Numbers */
  font-weight: 500 !important;
}

.monaco-editor .mtk8 { /* Comments */
  font-style: italic !important;
}

.monaco-editor .mtk9 { /* Functions */
  font-weight: bold !important;
}

/* Enhanced bracket matching */
.monaco-editor .bracket-match {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border: 2px solid !important;
  border-radius: 2px !important;
}

/* Enhanced selection highlighting */
.monaco-editor .selected-text {
  border-radius: 2px !important;
}

/* Enhanced find/replace highlighting */
.monaco-editor .findMatch {
  border-radius: 2px !important;
  animation: pulse 1s ease-in-out !important;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* Current line highlighting */
.monaco-editor .current-line {
  background-color: #2a2d2e !important;
}

/* Selection highlighting */
.monaco-editor .selected-text {
  background-color: #264f78 !important;
}

/* Cursor styling */
.monaco-editor .cursor {
  background-color: #aeafad !important;
  width: 2px !important;
}

/* Minimap styling */
.monaco-editor .minimap {
  background-color: #1e1e1e !important;
}

/* Scrollbar styling */
.monaco-editor .scrollbar .slider {
  background-color: #424242 !important;
}

/* Breakpoint styling */
.monaco-editor .breakpoint-enabled {
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIGZpbGw9IiNEQzE0M0MiLz4KPC9zdmc+') no-repeat center center !important;
  background-size: 12px 12px !important;
}

.monaco-editor .breakpoint-disabled {
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIGZpbGw9IiM4ODg4ODgiLz4KPC9zdmc+') no-repeat center center !important;
  background-size: 12px 12px !important;
}

/* Breakpoint hover effects */
.monaco-editor .breakpoint-enabled:hover {
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIGZpbGw9IiNGRjE2NDQiLz4KPC9zdmc+') no-repeat center center !important;
  background-size: 14px 14px !important;
}

.monaco-editor .breakpoint-disabled:hover {
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIGZpbGw9IiNBQUFBQUEiLz4KPC9zdmc+') no-repeat center center !important;
  background-size: 14px 14px !important;
}

.monaco-editor .scrollbar .slider:hover {
  background-color: #4f4f4f !important;
}

/* Better text anti-aliasing - removed color override */
.monaco-editor .view-lines .view-line span {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* Sidebar resize handle styles */
.sidebar-resize-handle {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  transition: background-color 0.2s ease;
  z-index: 10;
}

.sidebar-resize-handle:hover {
  background-color: #3b82f6;
  opacity: 0.7;
}

.sidebar-resize-handle:active {
  background-color: #2563eb;
  opacity: 0.9;
}

/* Add a subtle visual indicator */
.sidebar-resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 20px;
  background: #d1d5db;
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.sidebar-resize-handle:hover::after {
  opacity: 1;
}
