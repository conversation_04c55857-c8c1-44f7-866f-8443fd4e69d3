-- ADekoCAM, Model Script - Center Rectangular Frame Test
-- Converted from C# azCAM macro: Center Rectangular Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 400
  Y = Y or 300
  materialThickness = materialThickness or 18
  
  -- Center Rectangular Frame parameters (from original C# macro)
  -- Note: Using different variable names to avoid conflict with global X, Y
  centerX = 0    -- Center X coordinate (0 = center of panel)
  centerY = 0    -- Center Y coordinate (0 = center of panel)
  frameW = 50    -- Width of the frame
  frameH = 50    -- Height of the frame
  Z = 5          -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = frameW + 20   -- minimum required width
  local minHeight = frameH + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Center Rectangular Frame function (converted from C# macro)
  local function center_rectangular_frame(centerX, centerY, frameW, frameH, Z)
    -- Use default values if parameters not provided
    centerX = centerX or 0
    centerY = centerY or 0
    frameW = frameW or 50
    frameH = frameH or 50
    Z = Z or -5
    
    -- Calculate actual center position
    -- If centerX/centerY are 0, use panel center
    local actualCenterX = centerX
    local actualCenterY = centerY
    
    if centerX == 0 then
      actualCenterX = width / 2
    end
    
    if centerY == 0 then
      actualCenterY = height / 2
    end
    
    print(string.format("Creating centered rectangular frame"))
    print(string.format("Frame center: (%.1f, %.1f)", actualCenterX, actualCenterY))
    print(string.format("Frame size: %.1f x %.1f mm", frameW, frameH))
    
    -- Calculate corner positions (following C# logic exactly)
    -- p1 = (X-W/2, Y-H/2) - Bottom-left corner
    -- p2 = (X+W/2, Y-H/2) - Bottom-right corner  
    -- p3 = (X+W/2, Y+H/2) - Top-right corner
    -- p4 = (X-W/2, Y+H/2) - Top-left corner
    local p1 = {actualCenterX - frameW/2, actualCenterY - frameH/2}
    local p2 = {actualCenterX + frameW/2, actualCenterY - frameH/2}
    local p3 = {actualCenterX + frameW/2, actualCenterY + frameH/2}
    local p4 = {actualCenterX - frameW/2, actualCenterY + frameH/2}
    
    -- Validate that frame fits within panel bounds
    if p1[1] < 0 or p1[2] < 0 or p3[1] > width or p3[2] > height then
      print("Warning: Frame extends beyond panel boundaries")
      print(string.format("Frame bounds: (%.1f,%.1f) to (%.1f,%.1f)", 
            p1[1], p1[2], p3[1], p3[2]))
      print(string.format("Panel bounds: (0,0) to (%.1f,%.1f)", width, height))
    end
    
    -- Set layer and thickness for frame
    G.setLayer("G_V15")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the rectangular frame
    G.polyline(p1, p2, p3, p4, p1)  -- Close the rectangle
    
    print(string.format("Frame corners:"))
    print(string.format("  Bottom-left: (%.1f, %.1f)", p1[1], p1[2]))
    print(string.format("  Bottom-right: (%.1f, %.1f)", p2[1], p2[2]))
    print(string.format("  Top-right: (%.1f, %.1f)", p3[1], p3[2]))
    print(string.format("  Top-left: (%.1f, %.1f)", p4[1], p4[2]))
    
    return true
  end
  
  -- Call the center rectangular frame function
  local success = center_rectangular_frame(centerX, centerY, frameW, frameH, -Z)
  
  if success then
    print(string.format("Center rectangular frame created with parameters:"))
    print(string.format("  Center X: %s", centerX == 0 and "panel center" or centerX .. "mm"))
    print(string.format("  Center Y: %s", centerY == 0 and "panel center" or centerY .. "mm"))
    print(string.format("  Frame width: %d mm", frameW))
    print(string.format("  Frame height: %d mm", frameH))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Usage notes:")
    print("  - X=0, Y=0 centers the frame in the panel")
    print("  - Positive X/Y values offset from panel center")
    print("  - Frame is always centered at the specified coordinates")
  end
  
  return true
end

require "ADekoDebugMode"
