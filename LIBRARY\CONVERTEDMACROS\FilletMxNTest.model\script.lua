-- ADekoCAM, Model Script - Fillet MxN Grid Test
-- Converted from C# azCAM macro: Fillet MxN.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Fillet MxN Grid parameters (from original C# macro)
  A = 50    -- Outer margin
  B = 10    -- Gap between rectangles  
  R = 10    -- Fillet radius for rounded corners
  M = 3     -- Number of columns (fixed)
  N = 5     -- Number of rows (fixed)
  Z = 0     -- Thickness/depth (0 = use material thickness)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions and limits
  if M > 30 or N > 30 or M < 1 or N < 1 then
    print("Grid size must be between 1x1 and 30x30")
    return true
  end
  
  local minWidth = 2 * A + M * 20 + (M-1) * B  -- minimum required width
  local minHeight = 2 * A + N * 20 + (N-1) * B  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Fillet MxN Grid function (converted from C# macro)
  local function fillet_mxn_grid(A, B, R, M, N, Z)
    -- Use default values if parameters not provided
    A = A or 50
    B = B or 10
    R = R or 10
    M = M or 3
    N = N or 5
    Z = Z or 0
    
    -- Calculate rectangle dimensions (fixed grid, not dynamic)
    local x = (width - 2*A - (M-1)*B) / M   -- Width of each rectangle
    local y = (height - 2*A - (N-1)*B) / N  -- Height of each rectangle
    
    print(string.format("Creating %dx%d grid with filleted rectangles", M, N))
    print(string.format("Each rectangle: %.1f x %.1f mm with R=%.1f fillet", x, y, R))
    
    -- Set layer and thickness for rectangles
    G.setLayer("K_Freze10mm")
    if Z ~= 0 then
      G.setThickness(-Z)
    end
    
    local rectangleCount = 0
    
    -- Create filleted rectangle template function
    local function create_filleted_rectangle(centerX, centerY, rectWidth, rectHeight, radius)
      -- Calculate corner positions
      local halfW = rectWidth / 2
      local halfH = rectHeight / 2
      local x1 = centerX - halfW
      local y1 = centerY - halfH
      local x2 = centerX + halfW
      local y2 = centerY + halfH
      
      -- Ensure fillet radius doesn't exceed rectangle dimensions
      local maxRadius = math.min(rectWidth, rectHeight) / 2
      local actualRadius = math.min(radius, maxRadius)
      
      if actualRadius <= 0 then
        -- No fillet, create regular rectangle
        G.polyline(
          {x1, y1},  -- bottom-left
          {x2, y1},  -- bottom-right
          {x2, y2},  -- top-right
          {x1, y2},  -- top-left
          {x1, y1}   -- close the path
        )
      else
        -- Create filleted rectangle using arcs at corners
        -- Calculate bulge for 90-degree arc: bulge = tan(angle/4) = tan(90°/4) = tan(22.5°) ≈ 0.414
        local bulge = math.tan(math.pi / 8)  -- ≈ 0.414
        
        -- Create points with fillets at corners
        -- Start from bottom-left, going clockwise
        local p1 = {x1 + actualRadius, y1, 0, 0}        -- bottom edge start
        local p2 = {x2 - actualRadius, y1, 0, bulge}    -- bottom edge end, fillet to right edge
        local p3 = {x2, y1 + actualRadius, 0, 0}        -- right edge start
        local p4 = {x2, y2 - actualRadius, 0, bulge}    -- right edge end, fillet to top edge
        local p5 = {x2 - actualRadius, y2, 0, 0}        -- top edge start
        local p6 = {x1 + actualRadius, y2, 0, bulge}    -- top edge end, fillet to left edge
        local p7 = {x1, y2 - actualRadius, 0, 0}        -- left edge start
        local p8 = {x1, y1 + actualRadius, 0, bulge}    -- left edge end, fillet to bottom edge
        
        -- Create the filleted rectangle
        G.polyline(p1, p2, p3, p4, p5, p6, p7, p8, p1)
      end
    end
    
    -- Create grid using nested loops (rows and columns)
    for i = 0, N-1 do  -- rows (vertical position)
      for j = 0, M-1 do  -- columns (horizontal position)
        rectangleCount = rectangleCount + 1
        
        -- Calculate center position for this rectangle
        local rectCenterX = A + (j * x) + (j * B) + x/2
        local rectCenterY = A + (i * y) + (i * B) + y/2
        
        -- Create filleted rectangle at this position
        create_filleted_rectangle(rectCenterX, rectCenterY, x, y, R)
        
        -- Start new shape for next rectangle (except for the last one)
        if rectangleCount < M * N then
          G.nextShape()
        end
        
        print(string.format("Filleted rectangle [%d,%d]: center at (%.1f, %.1f)", 
              i+1, j+1, rectCenterX, rectCenterY))
      end
    end
    
    return true
  end
  
  -- Call the fillet MxN grid function
  local success = fillet_mxn_grid(A, B, R, M, N, Z)
  
  if success then
    print(string.format("Fillet MxN grid created with parameters:"))
    print(string.format("  A (outer margin): %d mm", A))
    print(string.format("  B (gap between rectangles): %d mm", B))
    print(string.format("  R (fillet radius): %d mm", R))
    print(string.format("  Grid size: %dx%d", M, N))
    print(string.format("  Thickness: %s", Z == 0 and "material thickness" or Z .. "mm"))
    print(string.format("Door size: %dx%d mm", X, Y))
  end
  
  return true
end

require "ADekoDebugMode"
