{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV_aV \t\tAcili V bicagi \nK_Ballnose\t\t Kure uclu kanal bicagi \nK_Freze_cT_mm\t\t cT Capli Freze bicagi \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 20, "description": "Gobek ic kanal arasi mesafe", "parameterName": "d"}, {"defaultValue": 135, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 60, "description": "Ic kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 0, "description": "<PERSON><PERSON><PERSON> ile kenar arasi kaydirma miktari", "parameterName": "sbt"}, {"defaultValue": 6, "description": "Acili V ustu kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 2, "description": "Gobekte kanal cizgileri var mi? (Var :Derinlik/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 3, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}