-- Test script that should work with ADekoDebugMode.lua
-- This mimics how ZeroBrain handles scripts

function modelMain()
    print("=== modelMain() started ===")
    
    -- Use some of the debug variables that ADekoDebugMode.lua sets up
    print("Cabinet dimensions: X=" .. X .. ", Y=" .. Y)
    print("Material thickness: " .. materialThickness)
    print("Offset: " .. offset)
    
    -- Draw a simple cabinet door using turtle graphics
    zero(50, 50)
    pnsz(2)
    pncl(colr(255, 0, 0))  -- Red color
    
    -- Draw door outline
    move(X - 100)  -- Use the X variable from ADekoDebugMode
    turn(90)
    move(Y - 100)  -- Use the Y variable from ADekoDebugMode
    turn(90)
    move(X - 100)
    turn(90)
    move(Y - 100)
    turn(90)
    
    -- Add a handle
    zero(X - 150, Y/2 - 25)
    pncl(colr(0, 0, 255))  -- Blue handle
    move(50)
    turn(90)
    move(10)
    turn(90)
    move(50)
    turn(90)
    move(10)
    turn(90)
    
    text("Cabinet Door", 0, 100, 200)
    
    print("=== modelMain() completed ===")
end

print("Script loaded - ready for ADekoDebugMode.lua to call modelMain()")
