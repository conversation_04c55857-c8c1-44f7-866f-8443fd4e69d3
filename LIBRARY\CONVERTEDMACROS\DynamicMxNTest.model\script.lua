-- ADekoCAM, Model Script - Dynamic MxN Grid Test
-- Converted from C# azCAM macro: Dynamic MxN.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Dynamic MxN Grid parameters (from original C# macro)
  A = 50     -- Left/right margin
  B = 50    -- Top/bottom margin  
  C = 20    -- Gap between rectangles
  rectX = 100  -- Width of each rectangle (~X parameter from C#)
  rectY = 100  -- Height of each rectangle (~Y parameter from C#)
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * A + rectX + 20  -- minimum required width
  local minHeight = 2 * B + rectY + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Dynamic MxN Grid function (converted from C# macro)
  local function dynamic_mxn_grid(A, B, C, rectX, rectY, Z)
    -- Use default values if parameters not provided
    A = A or 0
    B = B or 50
    C = C or 40
    rectX = rectX or 23
    rectY = rectY or 40
    Z = Z or -5
    
    -- Calculate number of columns and rows that can fit
    -- M = Floor((width-2*A+C) / (rectX+C))  -- columns
    -- N = Floor((height-2*B+C) / (rectY+C)) -- rows
    local M = math.floor((width - 2*A + C) / (rectX + C))
    local N = math.floor((height - 2*B + C) / (rectY + C))
    
    if M <= 0 or N <= 0 then
      print("No rectangles can fit with current parameters")
      return false
    end
    
    -- Calculate actual dimensions of each rectangle
    local mX = (width - 2*A - (M-1)*C) / M   -- Actual width of each rectangle
    local mY = (height - 2*B - (N-1)*C) / N  -- Actual height of each rectangle
    
    print(string.format("Creating %dx%d grid (%d total rectangles)", M, N, M*N))
    print(string.format("Each rectangle: %.1f x %.1f mm", mX, mY))
    
    -- Set layer and thickness for rectangles
    G.setLayer("K_Freze10mm")
    G.setThickness(Z)
    
    local rectangleCount = 0
    
    -- Create grid using nested loops (rows and columns)
    for i = 0, N-1 do  -- rows (vertical position)
      for j = 0, M-1 do  -- columns (horizontal position)
        rectangleCount = rectangleCount + 1
        
        -- Calculate position for this rectangle
        local rectPosX = A + (j * mX) + (j * C) + mX/2  -- Center X position
        local rectPosY = B + (i * mY) + (i * C) + mY/2  -- Center Y position
        
        -- Create rectangle for this grid cell
        -- Rectangle corners relative to center
        local x1 = rectPosX - mX/2
        local y1 = rectPosY - mY/2
        local x2 = rectPosX + mX/2
        local y2 = rectPosY + mY/2
        
        -- Create the rectangle using polyline
        G.polyline(
          {x1, y1},  -- bottom-left
          {x2, y1},  -- bottom-right
          {x2, y2},  -- top-right
          {x1, y2},  -- top-left
          {x1, y1}   -- close the path
        )
        
        -- Start new shape for next rectangle (except for the last one)
        if rectangleCount < M * N then
          G.nextShape()
        end
        
        print(string.format("Rectangle [%d,%d]: center at (%.1f, %.1f)", 
              i+1, j+1, rectPosX, rectPosY))
      end
    end
    
    return true
  end
  
  -- Call the dynamic MxN grid function
  local success = dynamic_mxn_grid(A, B, C, rectX, rectY, -Z)
  
  if success then
    print(string.format("Dynamic MxN grid created with parameters:"))
    print(string.format("  A (left/right margins): %d mm", A))
    print(string.format("  B (top/bottom margins): %d mm", B))
    print(string.format("  C (gap between rectangles): %d mm", C))
    print(string.format("  Rectangle size: %dx%d mm", rectX, rectY))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Door size: %dx%d mm", X, Y))
  end
  
  return true
end

require "ADekoDebugMode"
