-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib 
  minimum = 150
  limit = 350
 
  a = 50          -- Kenardan Vbite
  aa = 20					-- Dar kapak için Kenardan mesafe
  ad = 6          -- Vbit derinliği (sunkenDepth)
  aV = 120        -- Vbit Açısı
  gaps = 20
  
  extEdgeVtoolExist       	= 0   -- Dı<PERSON> kenar Pah işlemi var mı? derinlik/0:yok
  intGrooveExist  			= 2   -- iç kanal var mı? derinlik/ 0:yok
  edgeCornerRExist          = 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
   local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  --local bd = 3    -- Acili V Ustu kanal derinliği
  --dd = 2          -- iç kanal derinliği
  
  local cT = 6          -- Açılı V üzerinde kanal bıçak çapı
	
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
	
  local D = edgeCornerRExist
   
  local B = math.tan(math.pi/8)
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
    
  local sunkenDepth = ad
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)--
  --burası ortadaki çizgiler
  local gobekGenislik = Y-2*ust-2*sunkenWidth
  
  local stepX = math.floor(gobekGenislik/gaps)
  
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
  
  local aprxGaps = gobekGenislik/(stepX+1)
  local e = ust+sunkenWidth+aprxGaps
  --burası ortadaki çizgiler^
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
  
  
  G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, sunkenDepth, aV, vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  if extEdgeVtoolExist > 0 then
  	G.setLayer("K_AciliV_Pah")
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  if intGrooveExist > 0 then      ------İç kanal varsa
    G.setLayer("K_Form")  -- 
    G.setThickness(-intGrooveExist)
  
  --burası ortadaki çizgiler
	for i=0,stepX-1 do
		local y = e+i*aprxGaps
		local offset = a + sunkenWidth
		point3 = {offset,y}
		--point3 = {x,0}
		point4 = {X-offset,y}  
		--point4 = {x,Y}  
		G.line(point3,point4,0)
		i = i+1
	end 
  --burası ortadaki çizgiler
  
  end
    
  return true
end

------------------------------------------------

require "ADekoDebugMode"
