-- Minimize Button Test Script
-- This script creates turtle graphics to test the minimize functionality

function modelMain()
    print("=== Minimize Button Test ===")
    
    -- Clear screen and set up
    wipe()
    pndn()  -- Put pen down
    pncl("blue")  -- Set pen color to blue
    pnsz(3)  -- Set pen size to 3
    
    -- Draw a colorful pattern to test the minimize functionality
    for i = 1, 8 do
        -- Change color for each iteration
        if i % 4 == 1 then
            pncl("red")
        elseif i % 4 == 2 then
            pncl("green")
        elseif i % 4 == 3 then
            pncl("blue")
        else
            pncl("orange")
        end
        
        -- Draw a square
        for j = 1, 4 do
            move(50)
            turn(90)
        end
        
        -- Move to next position and rotate
        turn(45)
        pnup()
        move(10)
        pndn()
    end
    
    -- Add some circles
    pnup()
    posn(100, 100)
    pndn()
    pncl("purple")
    crcl(0, 0, 30)
    
    pnup()
    posn(-100, -100)
    pndn()
    pncl("cyan")
    crcl(0, 0, 25)
    
    -- Add text
    pnup()
    posn(0, 150)
    text("Test the minimize button!", 0, 0, 0)
    
    pnup()
    posn(0, -150)
    text("Click minimize in visualization panel", 0, 0, 0)
    
    print("Minimize test pattern completed!")
    print("You should see a minimize button in the visualization panel header")
end
