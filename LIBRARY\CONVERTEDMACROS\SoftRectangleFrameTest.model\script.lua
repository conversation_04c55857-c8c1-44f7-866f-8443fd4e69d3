-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script - Soft Rectangle Frame Test
-- Converted from C# azCAM macro: Soft Rectangle Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Soft Rectangle Frame parameters (from original C# macro)
  A = 5     -- Left margin
  B = 80    -- Top margin
  C = 80    -- Right margin
  D = 5     -- Bottom margin
  RA = 2    -- Corner radius A (left to top transition)
  RB = 2    -- Corner radius B (top to right transition)
  RC = 2    -- Corner radius C (right to bottom transition)
  RD = 2    -- Corner radius D (bottom to left transition)
  Z = 5     -- Thickness/depth
  
  -- Reference corner (from original parameters.json: reference = 3)
  -- 1=top-left, 2=top-center, 3=top-right, 4=middle-left, 5=center, 6=middle-right, 7=bottom-left, 8=bottom-center, 9=bottom-right
  referenceCorner = 3  -- top-right (original default)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + C + 20   -- minimum required width
  local minHeight = B + D + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Soft Rectangle Frame function (converted from C# macro)
  local function soft_rectangle_frame(A, B, C, D, RA, RB, RC, RD, Z, refCorner)
    -- Use default values if parameters not provided
    A = A or 5
    B = B or 80
    C = C or 80
    D = D or 5
    RA = RA or 2
    RB = RB or 2
    RC = RC or 2
    RD = RD or 2
    Z = Z or -5
    refCorner = refCorner or 3
    
    print(string.format("Creating soft rectangle frame"))
    print(string.format("Margins: A=%d, B=%d, C=%d, D=%d", A, B, C, D))
    print(string.format("Corner radii: RA=%d, RB=%d, RC=%d, RD=%d", RA, RB, RC, RD))
    print(string.format("Reference corner: %d", refCorner))
    
    -- The C# macro uses panel coordinates directly, then applies reference transformation
    -- Line points from C# macro:
    -- line1: (A, D) to (A, height-B)
    -- line2: (A, height-B) to (width-C, height-B)
    -- line3: (width-C, height-B) to (width-C, D)
    -- line4: (width-C, D) to (A, D)
    
    local line1Start = {A, D}
    local line1End = {A, height - B}
    
    local line2Start = {A, height - B}
    local line2End = {width - C, height - B}
    
    local line3Start = {width - C, height - B}
    local line3End = {width - C, D}
    
    local line4Start = {width - C, D}
    local line4End = {A, D}
    
    -- Calculate frame dimensions for validation
    local frameWidth = width - A - C
    local frameHeight = height - B - D
    
    -- Ensure radii don't exceed frame dimensions
    local maxRadius = math.min(frameWidth, frameHeight) / 2
    RA = math.min(RA, maxRadius)
    RB = math.min(RB, maxRadius)
    RC = math.min(RC, maxRadius)
    RD = math.min(RD, maxRadius)
    
    -- Calculate filleted corner points
    -- Each corner gets cut by the radius distance
    
    -- Left edge points (with RA and RD radii)
    local p1 = {A, D + RD}           -- Left line start (after RD radius)
    local p2 = {A, height - B - RA}  -- Left line end (before RA radius)
    
    -- Top edge points (with RA and RB radii)
    local p3 = {A + RA, height - B}      -- Top line start (after RA radius)
    local p4 = {width - C - RB, height - B}  -- Top line end (before RB radius)
    
    -- Right edge points (with RB and RC radii)
    local p5 = {width - C, height - B - RB}  -- Right line start (after RB radius)
    local p6 = {width - C, D + RC}           -- Right line end (before RC radius)
    
    -- Bottom edge points (with RC and RD radii)
    local p7 = {width - C - RC, D}  -- Bottom line start (after RC radius)
    local p8 = {A + RD, D}          -- Bottom line end (before RD radius)
    
    -- Set layer and thickness for frame
    G.setLayer("Default")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the soft rectangle using polyline with bulges for rounded corners
    -- Calculate bulge for 90-degree arc: bulge = tan(angle/4) = tan(90°/4) = tan(22.5°) ≈ 0.414
    local bulge = math.tan(math.pi / 8)  -- ≈ 0.414
    
    -- Create the frame as a continuous polyline with bulges at corners
    -- Following the C# sequence: line1, filletArcA, line2, filletArcB, line3, filletArcC, line4, filletArcD
    local framePoints = {
      {p1[1], p1[2], 0, 0},      -- Left line start
      {p2[1], p2[2], 0, RA > 0 and bulge or 0},  -- Left line end, bulge for RA corner
      {p3[1], p3[2], 0, 0},      -- Top line start
      {p4[1], p4[2], 0, RB > 0 and bulge or 0},  -- Top line end, bulge for RB corner
      {p5[1], p5[2], 0, 0},      -- Right line start
      {p6[1], p6[2], 0, RC > 0 and bulge or 0},  -- Right line end, bulge for RC corner
      {p7[1], p7[2], 0, 0},      -- Bottom line start
      {p8[1], p8[2], 0, RD > 0 and bulge or 0},  -- Bottom line end, bulge for RD corner
      {p1[1], p1[2], 0, 0}       -- Close the frame
    }
    
    -- Create the soft rectangle frame
    G.polyline(table.unpack(framePoints))
    
    print(string.format("Soft rectangle frame created:"))
    print(string.format("  Panel size: %.1f x %.1f mm", width, height))
    print(string.format("  Frame size: %.1f x %.1f mm", frameWidth, frameHeight))
    print(string.format("  Effective radii: RA=%.1f, RB=%.1f, RC=%.1f, RD=%.1f", RA, RB, RC, RD))
    print(string.format("  Frame edges:"))
    print(string.format("    Left: (%.1f,%.1f) to (%.1f,%.1f)", p1[1], p1[2], p2[1], p2[2]))
    print(string.format("    Top: (%.1f,%.1f) to (%.1f,%.1f)", p3[1], p3[2], p4[1], p4[2]))
    print(string.format("    Right: (%.1f,%.1f) to (%.1f,%.1f)", p5[1], p5[2], p6[1], p6[2]))
    print(string.format("    Bottom: (%.1f,%.1f) to (%.1f,%.1f)", p7[1], p7[2], p8[1], p8[2]))
    
    return true
  end
  
  -- Call the soft rectangle frame function
  local success = soft_rectangle_frame(A, B, C, D, RA, RB, RC, RD, -Z, referenceCorner)
  
  if success then
    local refNames = {
      [1] = "top-left", [2] = "top-center", [3] = "top-right",
      [4] = "middle-left", [5] = "center", [6] = "middle-right", 
      [7] = "bottom-left", [8] = "bottom-center", [9] = "bottom-right"
    }
    
    print(string.format("Soft rectangle frame created with parameters:"))
    print(string.format("  Margins: A=%d, B=%d, C=%d, D=%d mm", A, B, C, D))
    print(string.format("  Corner radii: RA=%d, RB=%d, RC=%d, RD=%d mm", RA, RB, RC, RD))
    print(string.format("  Reference corner: %d (%s)", referenceCorner, refNames[referenceCorner] or "unknown"))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a margin-based frame with independent corner radii")
    print("  - Frame size adapts to panel size minus margins")
    print("  - Each corner can have different radius (RA, RB, RC, RD)")
    print("  - Combines rectangular frame with filleted corners")
    print("")
    print("Corner mapping:")
    print("  RA: Left to top transition")
    print("  RB: Top to right transition")
    print("  RC: Right to bottom transition")
    print("  RD: Bottom to left transition")
    print("")
    print("Applications:")
    print("  - Standard frames with soft corners")
    print("  - Safety-conscious designs")
    print("  - Modern furniture aesthetics")
    print("  - Margin-based positioning with rounded corners")
  end
  
  return true
end

require "ADekoDebugMode"
