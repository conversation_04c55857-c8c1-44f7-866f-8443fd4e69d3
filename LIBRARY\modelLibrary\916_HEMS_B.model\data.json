{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV* \t\t*aV* Acili V bicagi \nK_Freze*cW*mm\t\t*cW*mm Freze Bicagi \nK_Freze*cT*mm\t\t*cT*mm Ic kanal freze Bicagi  \nH_Freze*cT*mm_Ic\t*cT*mm Cam yeri kesim bicagi \nH_Freze*cW*mm_Ic_SF\t*cW*mm cam yeri tarama bicagi \nK_AciliV-K_Ballnose-K_Freze \tDis kanal1 ve 2 bicagi  \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 350, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar kapakda acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 10, "description": "IC kenardan DIS Kanal_1 e Mesafe", "parameterName": "b"}, {"defaultValue": 20, "description": "IC kenardan DIS Kanal_2 e mesafe", "parameterName": "c"}, {"defaultValue": 90, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "Cam yeri tarama bicak vapi", "parameterName": "cW"}, {"defaultValue": 8, "description": "ic Kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 10, "description": "Cam yeri fatura genisligi", "parameterName": "glassMargin"}, {"defaultValue": 14, "description": "mm, window depth from top", "parameterName": "windowDepthFront"}, {"defaultValue": 4, "description": "mm, window depth from bottom", "parameterName": "windowDepthBack"}, {"defaultValue": 0, "description": "----------------------DIS KANAL ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 0, "description": "DIS kanal1 islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 1, "description": "DIS kanal1 :Ballnose mu? Flat mi? V kanal mi?(B:0/F:1/V:2)", "parameterName": "extGrvBorForV"}, {"defaultValue": 0, "description": "DIS kanal2 islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGroove2Exist"}, {"defaultValue": 0, "description": "DIS kanal2 :Ballnose mu? Flat mi? V kanal mi?(B:0/F:1/V:2)", "parameterName": "extGrv2BorForV"}, {"defaultValue": 0, "description": "----------------------IC <PERSON><PERSON>AR ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 1.5, "description": "Ic kenar ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 6, "description": "Kanal islemi bicak capi", "parameterName": "dT"}, {"defaultValue": 0, "description": "Ic kenar ustu kanal kenar uzatma islemi var mi?(Var :1/ Yok:0)", "parameterName": "topGrvExtExist"}, {"defaultValue": 0, "description": "Ic kenar ustu kanal islemi Ballnose mu? Flat-Duz mu? (B:0/ F:1)", "parameterName": "topGrooveBorF"}, {"defaultValue": 7, "description": "Ic kenarl<PERSON>a pah var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intEdgeToolExist"}, {"defaultValue": 0, "description": "Ic kenarda pah:V mi?SivrikoseV mi?Raduslu mu?(V:0/SV:1/R:2)", "parameterName": "intEdgeVorSVorR"}, {"defaultValue": 0, "description": "----------------------DIS KENAR ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeToolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi V mi? Raduslu mu? (V :0/ R:1)", "parameterName": "extEdgeVorR"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}