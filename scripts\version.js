#!/usr/bin/env node

/**
 * Manual version management script
 * Usage:
 *   node scripts/version.js patch    # increment patch version (1.0.0 -> 1.0.1)
 *   node scripts/version.js minor    # increment minor version (1.0.0 -> 1.1.0)
 *   node scripts/version.js major    # increment major version (1.0.0 -> 2.0.0)
 *   node scripts/version.js current  # show current version
 */

import { main } from './increment-version.js';
import fs from 'fs';
import path from 'path';

function showCurrentVersion() {
  try {
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const cargoPath = path.join(process.cwd(), 'src-tauri', 'Cargo.toml');
    const cargoContent = fs.readFileSync(cargoPath, 'utf8');
    const cargoMatch = cargoContent.match(/^version = "([^"]+)"$/m);
    
    const tauriConfPath = path.join(process.cwd(), 'src-tauri', 'tauri.conf.json');
    const tauriConf = JSON.parse(fs.readFileSync(tauriConfPath, 'utf8'));
    
    console.log('📦 Current versions:');
    console.log(`   package.json: ${packageJson.version}`);
    console.log(`   Cargo.toml:   ${cargoMatch ? cargoMatch[1] : 'not found'}`);
    console.log(`   tauri.conf:   ${tauriConf.version}`);
    
    // Check if versions are in sync
    const versions = [
      packageJson.version,
      cargoMatch ? cargoMatch[1] : null,
      tauriConf.version
    ].filter(v => v !== null);
    
    const allSame = versions.every(v => v === versions[0]);
    
    if (allSame) {
      console.log('✅ All versions are in sync');
    } else {
      console.log('⚠️  Versions are out of sync!');
    }
    
  } catch (error) {
    console.error('❌ Error reading versions:', error.message);
    process.exit(1);
  }
}

function showHelp() {
  console.log('📋 Version Management Script');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/version.js current  # Show current versions');
  console.log('  node scripts/version.js patch    # Increment patch version (1.0.0 -> 1.0.1)');
  console.log('  node scripts/version.js minor    # Increment minor version (1.0.0 -> 1.1.0)');
  console.log('  node scripts/version.js major    # Increment major version (1.0.0 -> 2.0.0)');
  console.log('  node scripts/version.js help     # Show this help');
  console.log('');
  console.log('The script updates versions in:');
  console.log('  - package.json');
  console.log('  - src-tauri/Cargo.toml');
  console.log('  - src-tauri/tauri.conf.json');
}

function main_version() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'current':
    case 'show':
      showCurrentVersion();
      break;
    case 'patch':
    case 'minor':
    case 'major':
      main();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      if (!command) {
        showCurrentVersion();
      } else {
        console.log(`❌ Unknown command: ${command}`);
        console.log('');
        showHelp();
        process.exit(1);
      }
  }
}

// Check if this module is being run directly
const isMainModule = import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'));
if (isMainModule) {
  main_version();
}
