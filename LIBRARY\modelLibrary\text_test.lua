-- Text function test
-- This script tests the text function specifically

-- Move to center and draw some text
posn(0, 0)
pncl("black")
text("Hello World!")

-- Move and draw text with offset
posn(-100, 50)
pncl("blue")
text("Blue Text", 0, 10, 0)  -- 10 pixel offset to the right

-- Draw text at different positions
posn(50, -50)
pncl("red")
text("Red Text")

posn(-50, -100)
pncl("green")
text("Green Text", 0, 0, 10)  -- 10 pixel offset down

print("Text test completed!")
print("You should see 'Hello World!' at center, and colored text at various positions.")
