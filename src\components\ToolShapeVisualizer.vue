<template>
  <div class="tool-shape-visualizer">
    <!-- Header -->
    <div class="visualizer-header">
      <h3>{{ $t('toolVisualizer.title') }}</h3>
      <p class="visualizer-description">{{ $t('toolVisualizer.description') }}</p>
      
      <!-- Controls -->
      <div class="visualizer-controls">
        <div class="control-group">
          <label>{{ $t('toolVisualizer.viewMode') }}:</label>
          <div class="view-mode-tabs">
            <button 
              @click="setViewMode('gallery')"
              :class="['tab-button', { 'active': viewMode === 'gallery' }]"
            >
              {{ $t('toolVisualizer.gallery') }}
            </button>
            <button 
              @click="setViewMode('comparison')"
              :class="['tab-button', { 'active': viewMode === 'comparison' }]"
            >
              {{ $t('toolVisualizer.comparison') }}
            </button>
            <button 
              @click="setViewMode('single')"
              :class="['tab-button', { 'active': viewMode === 'single' }]"
            >
              {{ $t('toolVisualizer.single') }}
            </button>
          </div>
        </div>
        
        <div class="control-group">
          <label>{{ $t('toolVisualizer.toolHeight') }}:</label>
          <input 
            v-model.number="toolHeight" 
            type="range" 
            min="10" 
            max="100" 
            step="5"
            class="height-slider"
            @input="updateToolHeight"
          />
          <span class="height-value">{{ toolHeight }}mm</span>
        </div>
        
        <div class="control-group">
          <button 
            @click="generateBReps" 
            :disabled="isGeneratingBReps"
            class="btn btn-primary"
          >
            <Zap :size="16" />
            {{ isGeneratingBReps ? $t('toolVisualizer.generating') : $t('toolVisualizer.generateBReps') }}
          </button>
          
          <button 
            @click="exportToolGLBs" 
            :disabled="!brepResults || isExporting"
            class="btn btn-secondary"
          >
            <Download :size="16" />
            {{ isExporting ? $t('toolVisualizer.exporting') : $t('toolVisualizer.exportGLBs') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
    </div>

    <!-- Error State -->
    <div v-if="error" class="error-state">
      <div class="error-content">
        <h4>{{ $t('toolVisualizer.error') }}</h4>
        <p>{{ error }}</p>
        <button @click="clearError" class="btn btn-outline">
          {{ $t('common.dismiss') }}
        </button>
      </div>
    </div>

    <!-- Gallery View -->
    <div v-if="viewMode === 'gallery' && !isLoading && !error" class="gallery-view">
      <div class="tool-grid">
        <div 
          v-for="tool in availableTools" 
          :key="tool.id"
          class="tool-card"
          :class="{ 'selected': selectedTool?.id === tool.id }"
          @click="selectTool(tool)"
        >
          <div class="tool-preview">
            <div ref="toolPreviewRefs" :data-tool-id="tool.id" class="three-preview"></div>
          </div>
          <div class="tool-info">
            <div class="tool-header">
              <component :is="getToolIcon(tool.shape)" :size="20" />
              <h4>{{ tool.name }}</h4>
            </div>
            <div class="tool-specs">
              <span class="spec-item">⌀{{ tool.diameter }}mm</span>
              <span class="spec-item">L{{ tool.length }}mm</span>
              <span class="spec-item">{{ $t(`cncTools.${tool.shape}`) }}</span>
            </div>
            <div v-if="tool.description" class="tool-description">
              {{ tool.description }}
            </div>
            <div v-if="getBRepStatus(tool.id)" class="brep-status">
              <span :class="['status-badge', getBRepStatus(tool.id)?.success ? 'success' : 'error']">
                {{ getBRepStatus(tool.id)?.success ? $t('toolVisualizer.brepReady') : $t('toolVisualizer.brepError') }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Single Tool View -->
    <div v-if="viewMode === 'single' && selectedTool && !isLoading && !error" class="single-view">
      <div class="single-tool-container">
        <div class="tool-3d-view">
          <div ref="singleToolRef" class="three-viewer-large"></div>
          
          <!-- Tool Controls -->
          <div class="tool-controls">
            <button @click="resetSingleView" class="control-btn" :title="$t('toolVisualizer.resetView')">
              <RotateCcw :size="16" />
            </button>
            <button @click="toggleWireframe" class="control-btn" :title="$t('toolVisualizer.wireframe')">
              <Grid3x3 :size="16" />
            </button>
            <button @click="toggleAutoRotate" class="control-btn" :title="$t('toolVisualizer.autoRotate')">
              <RotateCw :size="16" />
            </button>
          </div>
        </div>
        
        <div class="tool-details-panel">
          <h3>{{ selectedTool.name }}</h3>
          
          <!-- Tool Specifications -->
          <div class="spec-section">
            <h4>{{ $t('toolVisualizer.specifications') }}</h4>
            <div class="spec-grid">
              <div class="spec-row">
                <span class="spec-label">{{ $t('toolVisualizer.shape') }}:</span>
                <span class="spec-value">{{ $t(`cncTools.${selectedTool.shape}`) }}</span>
              </div>
              <div class="spec-row">
                <span class="spec-label">{{ $t('toolVisualizer.diameter') }}:</span>
                <span class="spec-value">{{ selectedTool.diameter }}mm</span>
              </div>
              <div class="spec-row">
                <span class="spec-label">{{ $t('toolVisualizer.length') }}:</span>
                <span class="spec-value">{{ selectedTool.length }}mm</span>
              </div>
              <div v-if="selectedTool.shape === 'conical'" class="spec-row">
                <span class="spec-label">{{ $t('toolVisualizer.tipAngle') }}:</span>
                <span class="spec-value">{{ (selectedTool as any).tipAngle }}°</span>
              </div>
              <div v-if="selectedTool.shape === 'ballnose'" class="spec-row">
                <span class="spec-label">{{ $t('toolVisualizer.ballRadius') }}:</span>
                <span class="spec-value">{{ (selectedTool as any).ballRadius || selectedTool.diameter/2 }}mm</span>
              </div>
              <div v-if="selectedTool.shape === 'radial'" class="spec-row">
                <span class="spec-label">{{ $t('toolVisualizer.cornerRadius') }}:</span>
                <span class="spec-value">{{ (selectedTool as any).cornerRadius || 'N/A' }}mm</span>
              </div>
            </div>
          </div>
          
          <!-- BRep Information -->
          <div v-if="getBRepStatus(selectedTool.id)" class="brep-section">
            <h4>{{ $t('toolVisualizer.brepInfo') }}</h4>
            <div class="brep-status-detail">
              <div class="status-indicator" :class="{ 'success': getBRepStatus(selectedTool.id)?.success }">
                {{ getBRepStatus(selectedTool.id)?.success ? '✓' : '✗' }}
              </div>
              <div class="status-text">
                {{ getBRepStatus(selectedTool.id)?.success ? 
                   $t('toolVisualizer.brepGenerated') : 
                   $t('toolVisualizer.brepFailed') }}
              </div>
            </div>
            <div v-if="getBRepStatus(selectedTool.id)?.glbData" class="glb-info">
              <span class="glb-size">
                GLB: {{ formatBytes(getBRepStatus(selectedTool.id)?.glbData?.byteLength || 0) }}
              </span>
              <button @click="downloadToolGLB(selectedTool)" class="btn btn-sm btn-outline">
                <Download :size="14" />
                {{ $t('toolVisualizer.downloadGLB') }}
              </button>
            </div>
          </div>
          
          <!-- Tool Operations -->
          <div class="operations-section">
            <h4>{{ $t('toolVisualizer.operations') }}</h4>
            <div class="operation-buttons">
              <button @click="generateSingleBRep(selectedTool)" class="btn btn-primary btn-sm">
                <Zap :size="14" />
                {{ $t('toolVisualizer.generateBRep') }}
              </button>
              <button @click="exportSingleGLB(selectedTool)" class="btn btn-secondary btn-sm">
                <Download :size="14" />
                {{ $t('toolVisualizer.exportGLB') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Comparison View -->
    <div v-if="viewMode === 'comparison' && !isLoading && !error" class="comparison-view">
      <div class="comparison-container">
        <div class="comparison-selectors">
          <div class="selector-group">
            <label>{{ $t('toolVisualizer.tool1') }}:</label>
            <select v-model="comparisonTool1" class="tool-select">
              <option value="">{{ $t('toolVisualizer.selectTool') }}</option>
              <option v-for="tool in availableTools" :key="tool.id" :value="tool.id">
                {{ tool.name }} (⌀{{ tool.diameter }}mm)
              </option>
            </select>
          </div>
          <div class="selector-group">
            <label>{{ $t('toolVisualizer.tool2') }}:</label>
            <select v-model="comparisonTool2" class="tool-select">
              <option value="">{{ $t('toolVisualizer.selectTool') }}</option>
              <option v-for="tool in availableTools" :key="tool.id" :value="tool.id">
                {{ tool.name }} (⌀{{ tool.diameter }}mm)
              </option>
            </select>
          </div>
        </div>
        
        <div v-if="comparisonTool1 && comparisonTool2" class="comparison-viewers">
          <div class="comparison-tool">
            <h4>{{ getToolById(comparisonTool1)?.name }}</h4>
            <div ref="comparisonTool1Ref" class="three-viewer-comparison"></div>
          </div>
          <div class="comparison-tool">
            <h4>{{ getToolById(comparisonTool2)?.name }}</h4>
            <div ref="comparisonTool2Ref" class="three-viewer-comparison"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- BRep Generation Results -->
    <div v-if="brepResults && !isLoading" class="brep-results">
      <div class="results-header">
        <h4>{{ $t('toolVisualizer.brepResults') }}</h4>
        <span class="results-summary">
          {{ brepResults.successCount }}/{{ brepResults.count }} {{ $t('toolVisualizer.successful') }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import * as THREE from 'three'
import { 
  Zap, Download, RotateCcw, Grid3x3, RotateCw, 
  Circle, Triangle, CornerDownRight, Star 
} from 'lucide-vue-next'
import { cncToolService } from '@/services/cncToolService'
import { ocjsService } from '@/services/ocjsService'
import type { CNCTool } from '@/types'

// Reactive data
const viewMode = ref<'gallery' | 'comparison' | 'single'>('gallery')
const toolHeight = ref(50)
const selectedTool = ref<CNCTool | null>(null)
const comparisonTool1 = ref('')
const comparisonTool2 = ref('')
const isLoading = ref(false)
const isGeneratingBReps = ref(false)
const isExporting = ref(false)
const loadingMessage = ref('')
const error = ref('')
const brepResults = ref<any>(null)

// Three.js refs
const toolPreviewRefs = ref<HTMLElement[]>([])
const singleToolRef = ref<HTMLElement | null>(null)
const comparisonTool1Ref = ref<HTMLElement | null>(null)
const comparisonTool2Ref = ref<HTMLElement | null>(null)

// Three.js objects storage
const previewScenes = new Map<string, { scene: THREE.Scene, renderer: THREE.WebGLRenderer, camera: THREE.Camera }>()
let singleViewScene: { scene: THREE.Scene, renderer: THREE.WebGLRenderer, camera: THREE.Camera, controls: any } | null = null
const comparisonScenes = new Map<string, { scene: THREE.Scene, renderer: THREE.WebGLRenderer, camera: THREE.Camera }>()

// Computed properties
const availableTools = computed(() => cncToolService.getAllTools())

// Methods
const setViewMode = (mode: 'gallery' | 'comparison' | 'single') => {
  viewMode.value = mode
  nextTick(() => {
    if (mode === 'gallery') {
      initializeGalleryPreviews()
    } else if (mode === 'single' && selectedTool.value) {
      initializeSingleView()
    } else if (mode === 'comparison') {
      initializeComparisonViews()
    }
  })
}

const selectTool = (tool: CNCTool) => {
  selectedTool.value = tool
  if (viewMode.value === 'single') {
    nextTick(() => initializeSingleView())
  }
}

const updateToolHeight = () => {
  // Update all tool visualizations with new height
  if (viewMode.value === 'gallery') {
    updateGalleryPreviews()
  } else if (viewMode.value === 'single') {
    updateSingleView()
  } else if (viewMode.value === 'comparison') {
    updateComparisonViews()
  }
}

const generateBReps = async () => {
  isGeneratingBReps.value = true
  loadingMessage.value = 'Generating BReps for all tools...'
  error.value = ''

  try {
    const tools = availableTools.value
    const result = await ocjsService.createAllToolBReps({
      tools,
      height: toolHeight.value,
      includeGLB: true
    })

    brepResults.value = result
    console.log('BRep generation completed:', result)
  } catch (err) {
    error.value = `Failed to generate BReps: ${err instanceof Error ? err.message : String(err)}`
    console.error('BRep generation error:', err)
  } finally {
    isGeneratingBReps.value = false
    loadingMessage.value = ''
  }
}

const generateSingleBRep = async (tool: CNCTool) => {
  isLoading.value = true
  loadingMessage.value = `Generating BRep for ${tool.name}...`

  try {
    const result = await ocjsService.createToolBRep({
      tool,
      height: toolHeight.value,
      includeGLB: true
    })

    // Update the results
    if (!brepResults.value) {
      brepResults.value = { results: [] }
    }

    const existingIndex = brepResults.value.results.findIndex((r: any) => r.toolId === tool.id)
    if (existingIndex >= 0) {
      brepResults.value.results[existingIndex] = result
    } else {
      brepResults.value.results.push(result)
    }

    console.log('Single BRep generated:', result)
  } catch (err) {
    error.value = `Failed to generate BRep for ${tool.name}: ${err instanceof Error ? err.message : String(err)}`
    console.error('Single BRep generation error:', err)
  } finally {
    isLoading.value = false
    loadingMessage.value = ''
  }
}

const exportToolGLBs = async () => {
  if (!brepResults.value) return

  isExporting.value = true

  try {
    const successfulResults = brepResults.value.results.filter((r: any) => r.success && r.glbData)

    for (const result of successfulResults) {
      const blob = new Blob([result.glbData], { type: 'model/gltf-binary' })
      const url = URL.createObjectURL(blob)

      const a = document.createElement('a')
      a.href = url
      a.download = `${result.toolName.replace(/[^a-zA-Z0-9]/g, '_')}_tool.glb`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  } catch (err) {
    error.value = `Failed to export GLBs: ${err instanceof Error ? err.message : String(err)}`
  } finally {
    isExporting.value = false
  }
}

const exportSingleGLB = async (tool: CNCTool) => {
  const status = getBRepStatus(tool.id)
  if (!status?.glbData) {
    await generateSingleBRep(tool)
    return
  }

  try {
    const blob = new Blob([status.glbData], { type: 'model/gltf-binary' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `${tool.name.replace(/[^a-zA-Z0-9]/g, '_')}_tool.glb`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (err) {
    error.value = `Failed to export GLB for ${tool.name}: ${err instanceof Error ? err.message : String(err)}`
  }
}

const downloadToolGLB = (tool: CNCTool) => {
  exportSingleGLB(tool)
}

const getBRepStatus = (toolId: string) => {
  if (!brepResults.value?.results) return null
  return brepResults.value.results.find((r: any) => r.toolId === toolId)
}

const getToolById = (toolId: string) => {
  return availableTools.value.find(t => t.id === toolId)
}

const getToolIcon = (shape: string) => {
  const icons = {
    cylindrical: Circle,
    conical: Triangle,
    ballnose: Zap,
    radial: CornerDownRight,
    special: Star
  }
  return icons[shape as keyof typeof icons] || Circle
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const clearError = () => {
  error.value = ''
}

// Three.js initialization methods
const initializeGalleryPreviews = () => {
  nextTick(() => {
    availableTools.value.forEach(tool => {
      const container = document.querySelector(`[data-tool-id="${tool.id}"]`) as HTMLElement
      if (container && !previewScenes.has(tool.id)) {
        createToolPreview(tool, container)
      }
    })
  })
}

const createToolPreview = (tool: CNCTool, container: HTMLElement) => {
  const width = container.clientWidth || 200
  const height = container.clientHeight || 150

  // Scene
  const scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf8f9fa)

  // Camera - positioned for side view (looking from the side)
  const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000)

  // Set camera for straight side view - looking along X axis
  camera.position.set(80, 0, 0)  // Side view from positive X
  camera.lookAt(0, 0, 0)
  camera.up.set(0, 1, 0)  // Ensure Y is up

  // Renderer with enhanced settings
  const renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true,
    preserveDrawingBuffer: true
  })
  renderer.setSize(width, height)
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
  // Temporarily disable shadow mapping to fix proxy error in Three.js 0.160.0
  renderer.shadowMap.enabled = false
  // renderer.shadowMap.type = THREE.PCFSoftShadowMap
  container.appendChild(renderer.domElement)

  // Enhanced lighting setup
  const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(50, 50, 50)
  // Disable shadow casting since shadow mapping is disabled
  directionalLight.castShadow = false
  // directionalLight.shadow.mapSize.width = 1024
  // directionalLight.shadow.mapSize.height = 1024
  scene.add(directionalLight)

  // Add rim light for better definition
  const rimLight = new THREE.DirectionalLight(0x4080ff, 0.3)
  rimLight.position.set(-30, 20, -30)
  scene.add(rimLight)

  // Create tool mesh with enhanced materials
  const toolGeometry = cncToolService.createToolGeometry(tool, toolHeight.value)

  // Fix tool orientation - rotate to be upright and facing correct direction
  toolGeometry.mesh.rotation.x = 0  // Keep upright
  toolGeometry.mesh.rotation.y = 0  // Face forward
  toolGeometry.mesh.rotation.z = 0  // No roll

  // Position tool so cutting end is down (negative Y)
  toolGeometry.mesh.position.set(0, toolHeight.value / 2, 0)

  // Enhance material based on tool type
  const material = toolGeometry.mesh.material as THREE.MeshLambertMaterial
  // Disable shadows since shadow mapping is disabled
  toolGeometry.mesh.castShadow = false
  toolGeometry.mesh.receiveShadow = false

  // Set tool-specific colors and properties
  switch (tool.shape) {
    case 'cylindrical':
      material.color.setHex(0x888888)
      break
    case 'conical':
      material.color.setHex(0x666666)
      break
    case 'ballnose':
      material.color.setHex(0x999999)
      break
    case 'radial':
      material.color.setHex(0x777777)
      break
    case 'special':
      material.color.setHex(0x555555)
      break
  }

  // Shadows already disabled above
  scene.add(toolGeometry.mesh)

  // Add subtle ground plane for shadow (representing workpiece surface)
  const groundGeometry = new THREE.PlaneGeometry(100, 100)
  const groundMaterial = new THREE.MeshLambertMaterial({
    color: 0xffffff,
    transparent: true,
    opacity: 0.3
  })
  const ground = new THREE.Mesh(groundGeometry, groundMaterial)
  ground.rotation.x = -Math.PI / 2
  ground.position.y = 0  // At the cutting level
  ground.receiveShadow = false  // Shadows disabled
  scene.add(ground)

  // Store scene data
  previewScenes.set(tool.id, { scene, renderer, camera })

  // Enhanced animation loop with hover effects
  let animationId: number | undefined
  let rotationSpeed = 0.01

  // Add hover detection
  container.addEventListener('mouseenter', () => {
    rotationSpeed = 0.02
  })

  container.addEventListener('mouseleave', () => {
    rotationSpeed = 0.01
  })

  const animate = () => {
    animationId = requestAnimationFrame(animate)

    // Smooth rotation around Y-axis (vertical) to show tool profile
    toolGeometry.mesh.rotation.y += rotationSpeed

    // Keep the tool properly positioned (no bobbing that would affect orientation)
    // The tool should stay at its correct height

    try {
      // Workaround for Three.js 0.160.x proxy issue
      scene.updateMatrixWorld(true)
      camera.updateMatrixWorld(true)
      renderer.render(scene, camera)
    } catch (error) {
      console.error('Tool visualizer render error:', error)
    }
  }
  animate()

  // Store animation ID for cleanup
  toolGeometry.mesh.userData.animationId = animationId
}

const updateGalleryPreviews = () => {
  previewScenes.forEach((sceneData, toolId) => {
    const tool = getToolById(toolId)
    if (tool) {
      // Remove old mesh
      const oldMesh = sceneData.scene.children.find(child => child.userData?.tool)
      if (oldMesh) {
        sceneData.scene.remove(oldMesh)
      }

      // Add new mesh with updated height and correct positioning
      const toolGeometry = cncToolService.createToolGeometry(tool, toolHeight.value)

      // Fix tool orientation and position
      toolGeometry.mesh.rotation.x = 0
      toolGeometry.mesh.rotation.y = 0
      toolGeometry.mesh.rotation.z = 0
      toolGeometry.mesh.position.set(0, toolHeight.value / 2, 0)

      sceneData.scene.add(toolGeometry.mesh)
    }
  })
}

const initializeSingleView = () => {
  if (!selectedTool.value || !singleToolRef.value) return

  // Clean up existing scene
  if (singleViewScene) {
    singleViewScene.renderer.dispose()
    singleToolRef.value.innerHTML = ''
  }

  const container = singleToolRef.value
  const width = container.clientWidth || 600
  const height = container.clientHeight || 400

  // Scene
  const scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf0f0f0)

  // Camera - positioned for clear side view
  const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000)
  camera.position.set(120, toolHeight.value / 2, 0)  // Side view from positive X, centered on tool
  camera.lookAt(0, toolHeight.value / 2, 0)  // Look at tool center
  camera.up.set(0, 1, 0)  // Ensure Y is up

  // Renderer
  const renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  // Temporarily disable shadow mapping to fix proxy error in Three.js 0.160.0
  renderer.shadowMap.enabled = false
  // renderer.shadowMap.type = THREE.PCFSoftShadowMap
  container.appendChild(renderer.domElement)

  // Controls (OrbitControls would be imported separately)
  // const controls = new OrbitControls(camera, renderer.domElement)
  // controls.enableDamping = true

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(100, 100, 100)
  // Disable shadow casting since shadow mapping is disabled
  directionalLight.castShadow = false
  // directionalLight.shadow.mapSize.width = 2048
  // directionalLight.shadow.mapSize.height = 2048
  scene.add(directionalLight)

  // Create tool mesh
  const toolGeometry = cncToolService.createToolGeometry(selectedTool.value, toolHeight.value)

  // Fix tool orientation and position
  toolGeometry.mesh.rotation.x = 0
  toolGeometry.mesh.rotation.y = 0
  toolGeometry.mesh.rotation.z = 0
  toolGeometry.mesh.position.set(0, toolHeight.value / 2, 0)  // Position so cutting end is at Y=0

  // Disable shadows since shadow mapping is disabled
  toolGeometry.mesh.castShadow = false
  toolGeometry.mesh.receiveShadow = false
  scene.add(toolGeometry.mesh)

  // Add ground plane (workpiece surface)
  const groundGeometry = new THREE.PlaneGeometry(200, 200)
  const groundMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff })
  const ground = new THREE.Mesh(groundGeometry, groundMaterial)
  ground.rotation.x = -Math.PI / 2
  ground.position.y = 0  // At the cutting level
  ground.receiveShadow = false  // Shadows disabled
  scene.add(ground)

  // Store scene data
  singleViewScene = { scene, renderer, camera, controls: null }

  // Animation loop
  const animate = () => {
    // if (singleViewScene?.controls) {
    //   singleViewScene.controls.update()
    // }
    renderer.render(scene, camera)
    requestAnimationFrame(animate)
  }
  animate()
}

const updateSingleView = () => {
  if (singleViewScene && selectedTool.value) {
    // Remove old mesh
    const oldMesh = singleViewScene.scene.children.find(child => child.userData?.tool)
    if (oldMesh) {
      singleViewScene.scene.remove(oldMesh)
    }

    // Add new mesh with updated height and correct positioning
    const toolGeometry = cncToolService.createToolGeometry(selectedTool.value, toolHeight.value)

    // Fix tool orientation and position
    toolGeometry.mesh.rotation.x = 0
    toolGeometry.mesh.rotation.y = 0
    toolGeometry.mesh.rotation.z = 0
    toolGeometry.mesh.position.set(0, toolHeight.value / 2, 0)

    toolGeometry.mesh.castShadow = true
    toolGeometry.mesh.receiveShadow = true
    singleViewScene.scene.add(toolGeometry.mesh)

    // Update camera position for new tool height
    singleViewScene.camera.position.set(120, toolHeight.value / 2, 0)
    singleViewScene.camera.lookAt(0, toolHeight.value / 2, 0)

    // Ground stays at Y=0 (cutting level)
  }
}

const initializeComparisonViews = () => {
  if (!comparisonTool1.value || !comparisonTool2.value) return

  const tool1 = getToolById(comparisonTool1.value)
  const tool2 = getToolById(comparisonTool2.value)

  if (!tool1 || !tool2) return

  // Initialize first tool viewer
  if (comparisonTool1Ref.value) {
    createComparisonViewer(tool1, comparisonTool1Ref.value, 'tool1')
  }

  // Initialize second tool viewer
  if (comparisonTool2Ref.value) {
    createComparisonViewer(tool2, comparisonTool2Ref.value, 'tool2')
  }
}

const createComparisonViewer = (tool: CNCTool, container: HTMLElement, viewerId: string) => {
  // Clean up existing scene
  const existingScene = comparisonScenes.get(viewerId)
  if (existingScene) {
    existingScene.renderer.dispose()
    container.innerHTML = ''
  }

  const width = container.clientWidth || 300
  const height = container.clientHeight || 250

  // Scene
  const scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf0f0f0)

  // Camera - positioned for side view
  const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000)
  camera.position.set(100, height / 2, 0)  // Side view from positive X
  camera.lookAt(0, height / 2, 0)
  camera.up.set(0, 1, 0)  // Ensure Y is up

  // Renderer
  const renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  // Temporarily disable shadow mapping to fix proxy error in Three.js 0.160.0
  renderer.shadowMap.enabled = false
  // renderer.shadowMap.type = THREE.PCFSoftShadowMap
  container.appendChild(renderer.domElement)

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.5)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(60, 60, 60)
  // Disable shadow casting since shadow mapping is disabled
  directionalLight.castShadow = false
  scene.add(directionalLight)

  // Create tool mesh
  const toolGeometry = cncToolService.createToolGeometry(tool, toolHeight.value)

  // Fix tool orientation and position
  toolGeometry.mesh.rotation.x = 0
  toolGeometry.mesh.rotation.y = 0
  toolGeometry.mesh.rotation.z = 0
  toolGeometry.mesh.position.set(0, toolHeight.value / 2, 0)

  // Disable shadows since shadow mapping is disabled
  toolGeometry.mesh.castShadow = false
  scene.add(toolGeometry.mesh)

  // Add cutting profile visualization (like in the image)
  createCuttingProfile(scene, tool, toolHeight.value)

  // Store scene data
  comparisonScenes.set(viewerId, { scene, renderer, camera })

  // Animation loop
  const animate = () => {
    toolGeometry.mesh.rotation.y += 0.008
    try {
      // Workaround for Three.js 0.160.x proxy issue
      scene.updateMatrixWorld(true)
      camera.updateMatrixWorld(true)
      renderer.render(scene, camera)
    } catch (error) {
      console.error('Tool comparison render error:', error)
    }
    requestAnimationFrame(animate)
  }
  animate()
}

const createCuttingProfile = (scene: THREE.Scene, tool: CNCTool, _height: number) => {
  // Create a material block to show cutting profile (like the orange block in image)
  const blockWidth = 60
  const blockHeight = 20
  const blockDepth = 40

  // Create the material block
  const blockGeometry = new THREE.BoxGeometry(blockWidth, blockHeight, blockDepth)
  const blockMaterial = new THREE.MeshLambertMaterial({
    color: 0xCD853F, // Sandy brown color like in the image
    transparent: true,
    opacity: 0.8
  })
  const materialBlock = new THREE.Mesh(blockGeometry, blockMaterial)
  materialBlock.position.set(0, -blockHeight/2, 0)  // Position below the cutting level (Y=0)
  materialBlock.receiveShadow = false  // Shadows disabled
  scene.add(materialBlock)

  // Create cutting profile based on tool type
  const radius = tool.diameter / 2
  let profileGeometry: THREE.BufferGeometry

  switch (tool.shape) {
    case 'cylindrical':
      // PLAIN - rectangular cut
      profileGeometry = new THREE.BoxGeometry(tool.diameter, blockHeight + 2, blockDepth)
      break

    case 'conical':
      // DOVETAIL/ANGLE/VEE - angled cut
      // conicalTool, tipAngle and bottomWidth calculations removed as they're not used in current implementation
      const cutDepth = blockHeight + 2
      const topWidth = tool.diameter

      // Create angled profile using ConeGeometry
      profileGeometry = new THREE.ConeGeometry(topWidth/2, cutDepth, 8)
      break

    case 'ballnose':
      // ROUND - rounded bottom cut
      const ballRadius = (tool as any).ballRadius || radius
      const cylinderHeight = Math.max(0, blockHeight - ballRadius)

      // Create cylinder + hemisphere
      const cylinderGeom = new THREE.CylinderGeometry(radius, radius, cylinderHeight)
      // sphereGeom creation removed as it's not used in current implementation

      // Merge geometries (simplified approach)
      profileGeometry = cylinderGeom
      break

    case 'radial':
      // ROUND OVER - rounded edge cut
      // cornerRadius calculation removed as it's not used in current implementation
      profileGeometry = new THREE.CylinderGeometry(radius, radius, blockHeight + 2)
      break

    default:
      profileGeometry = new THREE.BoxGeometry(tool.diameter, blockHeight + 2, blockDepth)
  }

  // Create the cut profile (negative space)
  const cutMaterial = new THREE.MeshLambertMaterial({
    color: 0xffffff,
    transparent: true,
    opacity: 0.3,
    side: THREE.DoubleSide
  })
  const cutProfile = new THREE.Mesh(profileGeometry, cutMaterial)
  cutProfile.position.set(0, -blockHeight/2 + 1, 0)  // Position in the material block
  scene.add(cutProfile)

  // Add grid lines on the material block for reference
  const gridHelper = new THREE.GridHelper(blockWidth, 4, 0x666666, 0x888888)
  gridHelper.position.set(0, -blockHeight, 0)  // Below the material block
  gridHelper.material.transparent = true
  gridHelper.material.opacity = 0.3
  scene.add(gridHelper)
}

const updateComparisonViews = () => {
  // Re-initialize comparison views with new tool height
  if (comparisonTool1.value && comparisonTool2.value) {
    initializeComparisonViews()
  }
}

const resetSingleView = () => {
  if (singleViewScene?.camera) {
    singleViewScene.camera.position.set(120, toolHeight.value / 2, 0)
    singleViewScene.camera.lookAt(0, toolHeight.value / 2, 0)
    singleViewScene.camera.up.set(0, 1, 0)
  }
}

const toggleWireframe = () => {
  if (singleViewScene) {
    singleViewScene.scene.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshLambertMaterial) {
        child.material.wireframe = !child.material.wireframe
      }
    })
  }
}

const toggleAutoRotate = () => {
  // Implementation for auto-rotate toggle
}

// Watchers for reactive updates
watch([comparisonTool1, comparisonTool2], () => {
  if (viewMode.value === 'comparison') {
    nextTick(() => initializeComparisonViews())
  }
})

watch(viewMode, (newMode) => {
  nextTick(() => {
    if (newMode === 'gallery') {
      initializeGalleryPreviews()
    } else if (newMode === 'single' && selectedTool.value) {
      initializeSingleView()
    } else if (newMode === 'comparison') {
      initializeComparisonViews()
    }
  })
})

// Lifecycle hooks
onMounted(() => {
  if (viewMode.value === 'gallery') {
    initializeGalleryPreviews()
  }
})

onUnmounted(() => {
  // Clean up Three.js resources
  previewScenes.forEach(sceneData => {
    sceneData.renderer.dispose()
  })

  if (singleViewScene) {
    singleViewScene.renderer.dispose()
  }

  comparisonScenes.forEach(sceneData => {
    sceneData.renderer.dispose()
  })
})
</script>

<style scoped>
.tool-shape-visualizer {
  padding: 16px;
  background: var(--vscode-editor-background);
  color: var(--vscode-foreground);
  height: 100%;
  overflow-y: auto;
}

.visualizer-header {
  margin-bottom: 24px;
  border-bottom: 1px solid var(--vscode-widget-border);
  padding-bottom: 16px;
}

.visualizer-header h3 {
  margin: 0 0 8px 0;
  color: var(--vscode-foreground);
  font-size: 18px;
}

.visualizer-description {
  margin: 0 0 16px 0;
  color: var(--vscode-descriptionForeground);
  font-size: 14px;
}

.visualizer-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-size: 12px;
  color: var(--vscode-foreground);
  white-space: nowrap;
}

.view-mode-tabs {
  display: flex;
  border: 1px solid var(--vscode-widget-border);
  border-radius: 4px;
  overflow: hidden;
}

.tab-button {
  padding: 6px 12px;
  border: none;
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.tab-button:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.tab-button.active {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.height-slider {
  width: 100px;
}

.height-value {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  min-width: 40px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  cursor: pointer;
  font-size: 12px;
  text-decoration: none;
  transition: all 0.2s;
}

.btn:hover {
  background: var(--vscode-button-hoverBackground);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.btn-secondary {
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--vscode-widget-border);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--vscode-widget-border);
  border-top: 3px solid var(--vscode-button-background);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-content {
  max-width: 400px;
}

.error-content h4 {
  color: var(--vscode-errorForeground);
  margin: 0 0 8px 0;
}

.gallery-view {
  margin-top: 16px;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.tool-card {
  border: 1px solid var(--vscode-widget-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--vscode-editor-background);
  cursor: pointer;
  transition: all 0.2s;
}

.tool-card:hover {
  border-color: var(--vscode-button-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tool-card.selected {
  border-color: var(--vscode-button-background);
  background: var(--vscode-list-activeSelectionBackground);
}

.tool-preview {
  height: 150px;
  background: #f8f9fa;
  position: relative;
}

.three-preview {
  width: 100%;
  height: 100%;
}

.tool-info {
  padding: 12px;
}

.tool-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.tool-header h4 {
  margin: 0;
  font-size: 14px;
  color: var(--vscode-foreground);
}

.tool-specs {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.spec-item {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
  background: var(--vscode-badge-background);
  padding: 2px 6px;
  border-radius: 3px;
}

.tool-description {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  margin-bottom: 8px;
}

.brep-status {
  margin-top: 8px;
}

.status-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: bold;
}

.status-badge.success {
  background: var(--vscode-testing-iconPassed);
  color: white;
}

.status-badge.error {
  background: var(--vscode-testing-iconFailed);
  color: white;
}

/* Single View Styles */
.single-view {
  margin-top: 16px;
}

.single-tool-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  height: 600px;
}

.tool-3d-view {
  position: relative;
  border: 1px solid var(--vscode-widget-border);
  border-radius: 8px;
  overflow: hidden;
  background: #f0f0f0;
}

.three-viewer-large {
  width: 100%;
  height: 100%;
}

.tool-controls {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--vscode-widget-border);
  border-radius: 4px;
  background: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.control-btn:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

.tool-details-panel {
  padding: 16px;
  border: 1px solid var(--vscode-widget-border);
  border-radius: 8px;
  background: var(--vscode-editor-background);
  overflow-y: auto;
}

.tool-details-panel h3 {
  margin: 0 0 16px 0;
  color: var(--vscode-foreground);
  font-size: 16px;
}

.spec-section, .brep-section, .operations-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--vscode-widget-border);
}

.spec-section:last-child, .brep-section:last-child, .operations-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.spec-section h4, .brep-section h4, .operations-section h4 {
  margin: 0 0 12px 0;
  color: var(--vscode-foreground);
  font-size: 14px;
}

.spec-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.spec-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.spec-label {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
}

.spec-value {
  font-size: 12px;
  color: var(--vscode-foreground);
  font-weight: 500;
}

.brep-status-detail {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--vscode-testing-iconFailed);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.status-indicator.success {
  background: var(--vscode-testing-iconPassed);
}

.status-text {
  font-size: 12px;
  color: var(--vscode-foreground);
}

.glb-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 8px;
  background: var(--vscode-badge-background);
  border-radius: 4px;
}

.glb-size {
  font-size: 11px;
  color: var(--vscode-descriptionForeground);
}

.operation-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Comparison View Styles */
.comparison-view {
  margin-top: 16px;
}

.comparison-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comparison-selectors {
  display: flex;
  gap: 24px;
  padding: 16px;
  background: var(--vscode-editor-background);
  border: 1px solid var(--vscode-widget-border);
  border-radius: 8px;
}

.selector-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.selector-group label {
  font-size: 12px;
  color: var(--vscode-foreground);
  font-weight: 500;
}

.tool-select {
  padding: 6px 8px;
  border: 1px solid var(--vscode-widget-border);
  border-radius: 4px;
  background: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-size: 12px;
}

.comparison-viewers {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  height: 400px;
}

.comparison-tool {
  border: 1px solid var(--vscode-widget-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--vscode-editor-background);
}

.comparison-tool h4 {
  margin: 0;
  padding: 12px;
  background: var(--vscode-badge-background);
  color: var(--vscode-foreground);
  font-size: 14px;
  border-bottom: 1px solid var(--vscode-widget-border);
}

.three-viewer-comparison {
  width: 100%;
  height: calc(100% - 45px);
  background: #f0f0f0;
}

/* BRep Results */
.brep-results {
  margin-top: 24px;
  padding: 16px;
  background: var(--vscode-editor-background);
  border: 1px solid var(--vscode-widget-border);
  border-radius: 8px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h4 {
  margin: 0;
  color: var(--vscode-foreground);
  font-size: 14px;
}

.results-summary {
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  background: var(--vscode-badge-background);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .single-tool-container {
    grid-template-columns: 1fr;
    height: auto;
  }

  .tool-3d-view {
    height: 400px;
  }

  .comparison-viewers {
    grid-template-columns: 1fr;
    height: 800px;
  }

  .comparison-selectors {
    flex-direction: column;
    gap: 16px;
  }

  .tool-grid {
    grid-template-columns: 1fr;
  }
}
</style>
