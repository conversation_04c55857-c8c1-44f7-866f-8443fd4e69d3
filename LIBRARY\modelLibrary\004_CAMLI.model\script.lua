-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib


  
  minimum = 150
  limit = 350


  a = 75
  aa = 30					-- Dar <PERSON>pak i<PERSON>in <PERSON> mesafe
  c = 10  --kenardan cama mesafe
  cW = 20
  cT = 8
  windowDepth = 6   --fatura derinli<PERSON>i
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köþe Radüsü var mý? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köþe radüs<PERSON> yapsýn mý
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
    local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end	
  
  --local sunkenDepth1 = 2
  --local finalDepth = 8
  --local cThinDiameter = 5
  --local vMidAngle = 90
  --local vMidDiameter = 40
  --local sunkenWidth1 = sunkenDepth1*math.tan((math.pi*vMidAngle/180)/2.0)
  --local sunkenWidth2 = finalDepth*math.tan((math.pi*vMidAngle/180)/2.0)
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
     
  G.setLayer("H_Freze"..cW.."mm_Fatura_Ic")
  G.setThickness(-windowDepth)
  G.rectangle({a, ust}, {X-a, Y-ust})
  
  G.setLayer("H_Freze"..cT.."mm_Cam_Ic")
  G.setThickness(-materialThickness)
  point1 = {a+c, ust+c}
  point2 = {X-(a+c), Y-(ust+c)}
  G.rectangle(point1,point2)
  
  G.showPar({X,a+20},{X-a,a+20},"a",2)
  G.showPar({X-(a+c),(a+c)+50},{X-a,(a+c)+50},"c",2)


  return true
end

require "ADekoDebugMode"