-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()

  G = ADekoLib
  minimum = 150
  
  a = 10
  ad = 8
  cW = 10                   -- Tarama Bıçak Çapı
  cT = 5
    
  windowDepthFront = 14
  windowDepthBack = 6
  
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> i<PERSON>lem<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> rad<PERSON> yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  local vWideAngle = 165
  local vWideDiameter = 300
  local n1, n2 = {}, {}
  
  if (X>xMin and Y>yMin) then
    G.setLayer("K_AciliV45")
    G.setThickness(0)
    local p1 = {a, a}
    local p2 = {X-a, Y-a}
    n1, n2 = G.sunkenFrame(p1, p2, ad, vWideAngle, vWideDiameter)
    n1[3] = 0
    n2[3] = 0
  end
  
  G.setLayer("H_Freze"..cT.."mm_Ic")
  G.setThickness(-windowDepthFront)
  G.rectangle(n1, n2)
  
  G.setFace("bottom")
  G.setLayer("H_Freze"..cW.."mm_Ic")
  G.setThickness(-windowDepthBack)
  G.rectangle(G.ptAdd(n1, {-cW/2, -cW/2}), G.ptAdd(n2, {cW/2, cW/2}))
  
  return true
end

require "ADekoDebugMode"
