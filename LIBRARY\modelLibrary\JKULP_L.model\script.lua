-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  minimum = 250
  jk_side = 4				--(Sol-1,Ust-2,Sag-3,Alt-4,solsag-13,altust-24)
  
  a = 5				--<PERSON><PERSON><PERSON> mesafe
  b = 7					--<PERSON><PERSON><PERSON> mesafe
  
  m = 10					--Yaklasma
  
  K1Off = 10              -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  K1Opr = 1              --Yapilacak islem 0-Bo<PERSON>,1-K_Kanal, 2-K_Form
  K1Depth = 2           -- Kanal Derinligi
  K1ToolNumber = 41 				-- Cap veya Vbit Acisi
 
  K2Off = 5             -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  K2Opr = 2             --<PERSON><PERSON><PERSON><PERSON><PERSON> islem 0-Bo<PERSON>,1-K_<PERSON><PERSON>, 2-K_Form
  K2Depth = 5            -- Kanal Derinligi
  K2ToolNumber = 90 				-- Cap veya Vbit Acisi
  
  K3Off = 6              -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  K3Opr = 0              --Yapilacak islem 0-Bos,1-K_Kanal, 2-K_Form
  K3Depth = 5            -- Kanal Derinligi
  K3ToolNumber = 42 				-- Cap veya Vbit Acisi
  
  jNotchToolDia = 6		--Fazla malzeme kesen takım capı
  edgeCornerRExist          = 0   -- Kapak kose Radusu var mi? derinlik/0:yok
  widIncrExist  = 10  
	
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  if a < 0 or b < 0 then
    print("a veya b degeri 0 dan küçük olamaz")
    return true
  end
  
  if m < 0 then
	m=0
  end
  
  function Decimals(W)
    W = string.gsub(W,"[.]",",")
    return W
  end
  
  
  local extWidth = m  
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  local leftWidth = 0
  local rightWidth = 0
  local topWidth = 0
  local bottomWidth = 0
  local aSide = 0
  local bSide = 0
  local leftRight = false
  local bottomTop = false
  local aWider = false
  local aTank = 0
  local jNotchOffset = jNotchToolDia/2
  
	if widIncrExist >0 then											--BUYUTME YAPILACAKSA
		 
		if a >= 0 then                              --Uzatma yapılacak uzunluk secimi -a-
			if m == 0 then						
				aSide = 2*a
			elseif m > 0 then
				aSide = a+m
			end
			aWider = true
		end
		
		if b >= 0 then                     --Uzatma yapılacak uzunluk secimi -b-
			if m == 0 then
				bSide = 2*b
			elseif m > 0 then
				bSide = b+m
			end
			bWider = true
		end
		
		G.setLayer("K_JNotch")	
		G.setThickness(-materialThickness)
		if jk_side == 1 then               							 --parçayı büyüten kenara eklenecek çizgiler
			leftRight = true
			leftWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 3 then
			leftRight = true
			rightWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 13 then
			leftRight = true
			leftWidth = extWidth
			rightWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 2 then
			bottomTop = true
			bottomWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 4 then
			bottomTop = true
			topWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		elseif jk_side == 24 then
			bottomTop = true
			topWidth = extWidth
			bottomWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			end
		end
	end
  
  function Kanallar(offset,operation,ad,W)	-- KENARDAN MESAFE, İSLEM, DERİNLİK, CAP VEYA ACI
					--ust--
				   ----4----
				--			--
				--			--
				--1sol		--3sag
				--			--		
				--			--
				   ----2----
					--alt--
	 W=Decimals(W)				
    G.setThickness(-ad)
    local xx = X
    local yy = Y
	local uzunluk = yy+b
    local side1 = offset
    local side2 = -a
    local side3 = offset
    local side4 = uzunluk
	

    if jk_side == 1 or jk_side == 3 or jk_side == 13 then								--sol sag kenarlarda mı
			points = {{side1,side2},{side3,side4}}
			if jk_side == 13 then														--sol ve sağ kenarlada mı
				points = {{side1,side2},{side3,side4}}
				points1 = G.mirror(points, "x", X, Y)
			elseif jk_side == 3 then
				points = G.mirror(points, "x", X, Y)
			end
    elseif jk_side == 4 or jk_side == 2 or jk_side == 24 then								--alt ust kenarlarda mı
		xx = Y
		yy = X
		uzunluk = yy+b
		side1 = -a
		side2 = offset
		side3 = uzunluk
		side4 = offset
		
			points = {{side1,side2},{side3,side4}}
			if jk_side == 24 then
				points = {{side1,side2},{side3,side4}}
				points1 = G.mirror(points, "y", X, Y)
			elseif jk_side == 4 then														--ust kenarda mı
				points = G.mirror(points, "y", X, Y)
			end
    end
    
    if operation == 1 then
      G.setLayer("K_Kanal_TN_"..W)		
      G.polylineimp(points)
	  if jk_side == 24 or jk_side == 13 then
		G.polylineimp(points1)
	  end
    end
    
    if operation == 2 then
      G.setLayer("K_Form_TN_"..W)		
      G.polylineimp(points)
	  if jk_side == 24 or jk_side == 13 then
		G.polylineimp(points1)
	  end
    end
    --return
  end
	  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
 elseif widIncrExist>0 then
	if bottomTop then                     ---kanallar ustte ve altta
		G.makePartShape({-leftWidth-aSide,-bottomWidth,0},
		{X+rightWidth+bSide,-bottomWidth,0},         
		{X+rightWidth+bSide,Y+topWidth,0},           
		{-leftWidth-aSide,Y+topWidth,0},             
		{-leftWidth-aSide,-bottomWidth,0})
	end
	if leftRight then                       ---kanallar sag ve solda
		G.makePartShape({-leftWidth,-bottomWidth-aSide,0},
		{X+rightWidth,-bottomWidth-aSide,0},         
		{X+rightWidth,Y+topWidth+bSide,0},           
		{-leftWidth,Y+topWidth+bSide,0},             
		{-leftWidth,-bottomWidth-aSide,0})
	end
  else
    G.makePartShape()
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
	Kanallar(K1Off,K1Opr,K1Depth,K1ToolNumber)
	
	if K2Opr>0 then
		Kanallar(K1Off-K2Off,K2Opr,K2Depth,K2ToolNumber)
		if K3Opr>0 then
			Kanallar(K1Off-K2Off-K3Off,K3Opr,K3Depth,K3ToolNumber)
		end
	end
	
  return true
end


require "ADekoDebugMode"
