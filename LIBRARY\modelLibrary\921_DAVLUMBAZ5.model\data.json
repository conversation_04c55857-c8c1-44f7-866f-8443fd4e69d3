{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV* \t\t*aV* Acili V bicagi \nH_Freze*CT*mm_Ic\t\t*cT*mm Freze Bicagi \nCep_Acma\t\tTarama Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 40, "description": "Kenardan Acili V Kenara Mesafe", "parameterName": "a"}, {"defaultValue": 5, "description": "Acili V Derinligi", "parameterName": "ad"}, {"defaultValue": 120, "description": "Acili V Uc Acisi", "parameterName": "aV"}, {"defaultValue": 6, "description": "Kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 80, "description": "<PERSON><PERSON>", "parameterName": "yy"}, {"defaultValue": 75, "description": "<PERSON><PERSON><PERSON> yaya mesafe", "parameterName": "m"}, {"defaultValue": 1.5, "description": "Ic kenar ustu kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "topGrooveExist"}, {"defaultValue": 0, "description": "Ic kenar ustu kanal kenar uzatma islemi var mi?(Var :1/ Yok:0)", "parameterName": "topGrvExtExist"}, {"defaultValue": 3, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}