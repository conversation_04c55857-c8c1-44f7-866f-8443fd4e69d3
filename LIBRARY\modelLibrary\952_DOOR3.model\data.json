{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV*aV*-K_Ballnose*dT*-K_Freze*dT* \tKanal bicagi \nK_AciliV*aV* \tKanal bicagi \nCep_Acma \tCep acma islemi tarama bicagi\nH_Freze_Ebatlama_ic \tCam acma islemi ebatlama bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 75, "description": "<PERSON><PERSON><PERSON> ofset (<PERSON><PERSON><PERSON> sag sol a gore belirlenir)", "parameterName": "a"}, {"defaultValue": 100, "description": "Ust kenardan mesafe (0 girilirse alt ve ustden esit araliklanır)", "parameterName": "ust"}, {"defaultValue": 150, "description": "Alt kenardan mesafe (0 girilirse alt ve ustden esit araliklanır)", "parameterName": "alt"}, {"defaultValue": 75, "description": "Sol kenardan ic kenara mesafe(a:0 sa girilir)", "parameterName": "sag"}, {"defaultValue": 75, "description": "Sol kenardan ic kenara mesafe(a:0 sa girilir)", "parameterName": "sol"}, {"defaultValue": 7, "description": "<PERSON><PERSON> derin<PERSON>gi", "parameterName": "ad"}, {"defaultValue": 3, "description": "Bolum adeti (0 girilirse adet gaps e gore belirlenir)", "parameterName": "how<PERSON><PERSON>"}, {"defaultValue": 100, "description": "Bolum yuksekligi(howMany:0 sa yaklasik girilir)", "parameterName": "gaps"}, {"defaultValue": 25, "description": "Acili V Kenardan Gobege Mesafe", "parameterName": "h"}, {"defaultValue": 20, "description": "Tarama <PERSON>", "parameterName": "cW"}, {"defaultValue": 6, "description": "ic Kose Finis Bicak Capi", "parameterName": "cT"}, {"defaultValue": 135, "description": "Acili Vbit uc acisi(varsa)", "parameterName": "aV"}, {"defaultValue": 0, "description": "----------------------DIS KANAL ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 2, "description": "Yan kanallar var mi?(Var :Derinlik/ Yok:0)", "parameterName": "sideGrooveExist"}, {"defaultValue": 8, "description": "Kanal islemi bicak capi", "parameterName": "dT"}, {"defaultValue": 1, "description": "Kanallar :Ballnose mu? Flat mi? V kanal mi?(B:0/F:1/V:2)", "parameterName": "GrvBorForV"}, {"defaultValue": 0, "description": "----------------------GOBEK ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 1, "description": "Duz kapakmi camli mi?(Duz :1/ Cam:0)", "parameterName": "windowOpExist"}, {"defaultValue": 1, "description": "Gobek var mi?(Var:1/ Yok:0)", "parameterName": "cabCoreExist"}, {"defaultValue": 5, "description": "Gobek desen bicagi var mi?(Var: Derinlik / Yok: 0)", "parameterName": "shapeToolExist"}, {"defaultValue": 0, "description": "----------------------DIS KENAR ISLEMLERI-----------------------------", "parameterName": "-----"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeToolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi V mi? Raduslu mu? (V :0/ R:1)", "parameterName": "extEdgeVorR"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}