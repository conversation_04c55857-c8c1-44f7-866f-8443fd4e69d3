{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nCep_A<PERSON>ma\t\tTarama Freze Bicagi\nK_AciliV\t\tTarama kenar ustu V Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 150, "description": "X minimum kenar u<PERSON>u", "parameterName": "xMin"}, {"defaultValue": 150, "description": "<PERSON> <PERSON> kenar u<PERSON>u", "parameterName": "yMin"}, {"defaultValue": 0, "description": "offset from the left-right edges", "parameterName": "a"}, {"defaultValue": 0, "description": "offset from the top-bottom edges", "parameterName": "b"}, {"defaultValue": 10, "description": "gap", "parameterName": "c"}, {"defaultValue": 10, "description": "pocket width", "parameterName": "y"}, {"defaultValue": 5, "description": "pocket depth", "parameterName": "z"}, {"defaultValue": 1, "description": "direction (1 for horizontal, 0 for vertical)", "parameterName": "d"}, {"defaultValue": 0, "description": "IC kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}