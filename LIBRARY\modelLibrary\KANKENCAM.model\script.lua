-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"
--tostring o<PERSON><PERSON><PERSON>na bakmalı
------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
	minimum = 250
  
	KD1Off = 0.1              -- <PERSON><PERSON><PERSON> Offset
	KD1Opr = 3              --Yapilacak islem 1-K_<PERSON><PERSON><PERSON>, 2-<PERSON>_<PERSON><PERSON>, 3-K_<PERSON><PERSON>irt<PERSON>, 4-<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5-K_<PERSON><PERSON><PERSON><PERSON>, 6-<PERSON><PERSON><PERSON>
	KD1Depth = 5          -- <PERSON>nal Derinligi
	KD1Dia = 36 				-- Cap veya Vbit Acisi
	 
	KD2Off = 19              -- <PERSON><PERSON><PERSON> Offset
	KD2Opr = 6              --Yapilacak islem 1-K_<PERSON>ez<PERSON>, 2-K_<PERSON><PERSON>, 3-K_<PERSON>k<PERSON>irt<PERSON>, 4-<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5-<PERSON>_<PERSON><PERSON><PERSON><PERSON>, 6-<PERSON><PERSON><PERSON>
	KD2Depth = 2.5            -- <PERSON><PERSON> Derinligi
	KD2Dia = 90 				-- Cap veya Vbit Acisi
	 
	KD3Off = 27              -- <PERSON><PERSON><PERSON> Offset
	KD3Opr = 3              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	KD3Depth = 3.3            -- Kanal Derinligi
	KD3Dia = 12 				-- Cap veya Vbit Acisi
	 
	KD4Off = 25              -- Kenardan Offset
	KD4Opr = 3              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	KD4Depth = 3.3            -- Kanal Derinligi
	KD4Dia = 20 				-- Cap veya Vbit Acisi
	 
	KD5Off = 55             -- Kenardan Offset
	KD5Opr = 4              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	KD5Depth = 3.3            -- Kanal Derinligi
	KD5Dia = 20 				-- Cap veya Vbit Acisi
	 
	KD6Off = 100             -- Kenardan Offset
	KD6Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	KD6Depth = 6            -- Kanal Derinligi
	KD6Dia = 90 				-- Cap veya Vbit Acisi

	K1Off = 53              -- Kenardan Offset
	K1Opr = 3              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	K1Depth = 3.3           -- Kanal Derinligi
	K1Dia = 12 				-- Cap veya Vbit Acisi
	 
	K2Off = 62             -- Kenardan Offset
	K2Opr = 3              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	K2Depth = 3.3            -- Kanal Derinligi
	K2Dia = 12 				-- Cap veya Vbit Acisi
	 
	K3Off = 63              -- Kenardan Offset
	K3Opr = 1              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	K3Depth = 14            -- Kanal Derinligi
	K3Dia = 6 				-- Cap veya Vbit Acisi
	 
	K4Off = 60              -- Kenardan Offset
	K4Opr = 0              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	K4Depth = 5            -- Kanal Derinligi
	K4Dia = 10 				-- Cap veya Vbit Acisi
	 
	K5Off = 90             -- Kenardan Offset
	K5Opr = 3              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	K5Depth = 5            -- Kanal Derinligi
	K5Dia = 10 				-- Cap veya Vbit Acisi
	 
	K6Off = 100             -- Kenardan Offset
	K6Opr = 6              --Yapilacak islem 1-K_Freze, 2-K_Ballnose, 3-K_BalikSirti, 4-Cep_Acma,5-K_AciliV, 6-SunkenV
	K6Depth = 6            -- Kanal Derinligi
	K6Dia = 90 				-- Cap veya Vbit Acisi

	extEdgeVToolExist 			= 0  --Dış kenar Pah işlemi var mı? (Var: Derinlik / Yok: 0)
	edgeCornerRExist 			= 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
	
	KBcOff = 8              -- Kenardan Offset
	KBcOpr = 1				--Arka yuzey kanallar var mı 1 yok mu 0
	KBcDepth = 7.5           -- Kanal Derinligi
	KBcDia	 = 20 				-- Cap veya Vbit Acisi
	
	KFrOff = 5              -- Kenardan Offset
	KFrOpr = 0				--On yuzey kanallar var mı 1 yok mu 0
	KFrDepth = 2           -- Kanal Derinligi
	KFrDia	 = 10 				-- Cap veya Vbit Acisi
	
	KLineAvailable = 0
	KLineRotation = 1   --yatay:0, dikey:1
	KLineOffset = 20
	KLineDepth = 1.4
	
	c = 4			--pencere araları düz mesafe
	
	blockNoX = 2
	blockNoY = 2
	
	if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
	
	local bulge = math.tan(math.pi/8)
	
	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist >0 then           ---kose radusu yapsin mi
		G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
		{X-edgeCornerRExist,0,0,bulge},
		{X,edgeCornerRExist},
		{X,Y-edgeCornerRExist,0,bulge},
		{X-edgeCornerRExist,Y},
		{edgeCornerRExist,Y,0,bulge},
		{0,Y-edgeCornerRExist},
		{0,edgeCornerRExist,0,bulge},
		{edgeCornerRExist,0})
	else
		G.makePartShape()
	end
	
	if extEdgeVToolExist  > 0 then
		G.setLayer("K_AciliV_Pah")	
		G.setThickness(-extEdgeVToolExist)
		G.rectangle({0,0},{X,Y})
	end
	
	if KLineAvailable == 1 then
		G.setLayer("K_Cizgi")	
		G.setThickness(-KLineDepth)
		if KLineRotation == 0 then
			G.line({0, KLineOffset}, {X, KLineOffset})
			G.line({0, Y - KLineOffset}, {X, Y-KLineOffset})
		end
		if KLineRotation == 1 then
			G.line({KLineOffset, 0},{KLineOffset, Y})
			G.line({X-KLineOffset, 0}, {X-KLineOffset, Y})
		end
	end
	--cam yerleri için elle girilen sınırlar---
	
	local xLimit1 = 400 -- yana 2. pencere koymak icin alt limit "Dahil"
	local xLimit2 = 800 -- yana 3. pencere koymak icin alt limit "Dahil"
	local xLimit3 = 1200 -- yana 4. pencere koymak icin alt limit "Dahil"
	
	local yLimit1 = 400 -- uste 2. pencere koymak icin alt limit "Dahil"
	local yLimit2 = 800 -- uste 3. pencere koymak icin alt limit "Dahil"
	local yLimit3 = 1200 -- uste 4. pencere koymak icin alt limit "Dahil"
	
  	
	if blockNoX == 0 then
		blockNoX = 1
		if X >= xLimit1 and X < xLimit2 then
		blockNoX = 2
		elseif X >= xLimit2 and X < xLimit3 then
		blockNoX = 3
		elseif X >= xLimit3 then
		blockNoX = 4
		end
	end
	
	local stepX = (blockNoX-1)		--Number of laths on X axis
	local stepY = (blockNoY-1)		--Number of laths on Y axis
	 
  
	local innerX = X-2*K1Off-stepX*c		--Width of the inner rectangle on the x axis
	local innerY = Y-2*K1Off-stepY*c		--Length of the inner rectangle on the y axis
  
	local correctedblockX = innerX / blockNoX
	local correctedblockY = innerY / blockNoY


        
  function Decimals(TN)
    TN = string.gsub(TN,"[.]",",")
    print("burda")
    return TN
  end
  
	--alt bolum hesaplamaları
    function rectToPoly (c1,c2)             -- gobekteki islemler icin gerekli
      points = { 
          {c1[1],c1[2]},
          {c2[1],c1[2]},
          {c2[1],c2[2]},
          {c1[1],c2[2]},
          {c1[1],c1[2]}
          }
		return points
	end
	
	--operasyon seçimleri
	function operations(operation,TN)	
		TN=Decimals(TN)
		if operation ==1 then
			layerName = "K_Freze"..TN.."mm"
		end
		
		if operation ==2 then
			layerName = "K_Ballnose"..TN.."mm"	
		end
		
		if operation ==3 then
			layerName = "K_BalikSirti"..TN.."mm"	
		end
		
		if operation ==4 then
			layerName = "Cep_Acma"	
		end
		
		if operation ==5 then
			layerName = "K_AciliV"..TN	
		end
			
		if operation ==6 then
			layerName = "V_Oyuk"..TN
		end
	
		if operation ==7 then
			layerName = "K_Desen"..TN	
		end
		
		return layerName
	end
	
	--pencerelerdeki işlemler
	function CabCoreOps (corner1,corner2,offset,opr,ad,TN)   --- bolumlerin içindeki işlemler buradan yapılıyor
		rPoints = rectToPoly (corner1,corner2)
		local lName	= tostring(operations(opr,TN))
		G.setLayer(lName)
		G.setThickness(-ad)
		G.polylineimp (G.offSet(rPoints, -offset))
	end
	
	--dış kenarlardaki işlemler
	function ExtOps (offset,opr,ad,TN)   --- bolumlerin içindeki işlemler buradan yapılıyor
		local lName	= tostring(operations(opr,TN))
		point1 = {offset,offset}
		point2 = {X-offset,Y-offset}
		G.setLayer(lName)
		G.setThickness(-ad)
		G.rectangle(point1,point2)
	end
  
  -- göbeklerdeki hesaplamalar
  local cabRects= {}
  if K1Opr>0 then
    for i=0, stepX, 1
      do
    
      x1 = K1Off+i*(correctedblockX+c)
      x2 = K1Off+(i+1)*(correctedblockX)+i*c
      for j=0, stepY, 1
        do
        y1 = K1Off+j*(correctedblockY+c)
        y2 = K1Off+(j+1)*(correctedblockY)+j*c
        point1 = {x1,y1}
        point2 = {x2,y2}
        table.insert(cabRects,{point1,point2})
      end
    end
  end
		
  for k, v in pairs(cabRects) do
      if K1Opr>0 then
        CabCoreOps (v[1],v[2],0,K1Opr,K1Depth,K1Dia)
        if K2Opr>0 then
					CabCoreOps (v[1],v[2],K2Off-K1Off,K2Opr,K2Depth,K2Dia)
					if K3Opr>0 then
						CabCoreOps (v[1],v[2],K3Off-K1Off,K3Opr,K3Depth,K3Dia)
						if K4Opr>0 then
							CabCoreOps (v[1],v[2],K4Off-K1Off,K4Opr,K4Depth,K4Dia)
							if K5Opr>0 then
								CabCoreOps (v[1],v[2],K5Off-K1Off,K5Opr,K5Depth,K5Dia)
								if K6Opr>0 then
									CabCoreOps (v[1],v[2],K6Off-K1Off,K6Opr,K6Depth,K6Dia)
								end
							end
						end
					end
				end
      end
  end 
  
  --dış kenarlardaki kanal eklemeler Pencere ofsetinden küçük olmalı
	if KD1Opr>0 then
		ExtOps (KD1Off,KD1Opr,KD1Depth,KD1Dia)
		if KD2Opr>0 then
			ExtOps (KD2Off,KD2Opr,KD2Depth,KD2Dia)
			if KD3Opr>0 then
				ExtOps (KD3Off,KD3Opr,KD3Depth,KD3Dia)
				if KD4Opr>0 then
					ExtOps (KD4Off,KD4Opr,KD4Depth,KD4Dia)
					if KD5Opr>0 then
						ExtOps (KD5Off,KD5Opr,KD5Depth,KD5Dia)
						if KD6Opr>0 then
							ExtOps (KD6Off,KD6Opr,KD6Depth,KD6Dia)
						end
					end
				end
			end
		end
	end
	
  
 
	if KFrOpr >0 then --on yuzeyde cerceve ustu islemler
		KFrDia = Decimals(KFrDia)
    G.setLayer("K_Freze"..KFrDia.."mm")		--Clearing procedures of the backside
		 for i = 1, stepX do
			G.setThickness(-KFrDepth)
			local y = K1Off-KFrOff
			local x = K1Off+i*correctedblockX+((i*2)-1)*(c/2)
			point1 = {x,y}
			point2 = {x,Y-y}
			G.line(point1,point2,0)
     end
		
		 for j = 1, stepY do
			 G.setThickness(-KFrDepth)
			 local x = K1Off-KFrOff
			 local y = K1Off+j*correctedblockY+ ((j*2)-1)*(c/2)
			 point1 = {x,y}
			 point2 = {X-x,y}
			 G.line(point1,point2,0)
		 end
	end
	
	
	G.setFace("bottom")
	if KBcOpr >0 then  ----arka yuzeyde cerceve ustu islemler 
    KBcDia = Decimals(KBcDia)
		G.setLayer("K_Freze"..KBcDia.."mm_SF")		--Clearing procedures of the backside
		for i = 1, stepX do
			G.setThickness(-KBcDepth)
			local b = K1Off-KBcOff
			local a = K1Off+i*correctedblockX+((i*2)-1)*(c/2)
			point1 = {a,b}
			point2 = {a,Y-b}
			G.line(point1,point2,0)
		end
		
		for j = 1, stepY do
			G.setThickness(-KBcDepth)
			local a = K1Off-KBcOff
			-- local y = KBcOff+j*correctedblockY+((j*2)-1)*(c/2)
			local b = K1Off+j*correctedblockY+ ((j*2)-1)*(c/2)
			--print(b)
			point1 = {a,b}
			point2 = {X-a,b}
			G.line(point1,point2,0)
		end
		G.setLayer("K_Freze"..KBcDia.."mm_SF")
		G.setThickness(-KBcDepth)
		G.rectangle({K1Off-KBcOff, K1Off-KBcOff}, {X-K1Off+KBcOff, Y-K1Off+KBcOff})
	end
  
  return true
end

require "ADekoDebugMode"
