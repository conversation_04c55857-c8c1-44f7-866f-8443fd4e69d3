<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sweep Operations Test Suite</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            min-height: 80vh;
        }

        .test-panel {
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
        }

        .results-panel {
            padding: 30px;
            display: flex;
            flex-direction: column;
        }

        .test-category {
            margin-bottom: 30px;
        }

        .test-category h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .test-button {
            width: 100%;
            padding: 15px;
            margin-bottom: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .test-button:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }

        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .test-button.success {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        .test-button.error {
            border-color: #e74c3c;
            background: #fdf2f2;
        }

        .test-button.running {
            border-color: #f39c12;
            background: #fef9e7;
        }

        .test-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .test-description {
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .status-indicator.running {
            color: #f39c12;
        }

        .status-indicator.success {
            color: #27ae60;
        }

        .status-indicator.error {
            color: #e74c3c;
        }

        .spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 2px solid #333;
        }

        .console-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .console-timestamp {
            color: #888;
            margin-right: 10px;
        }

        .console-success {
            color: #00ff00;
        }

        .console-error {
            color: #ff4444;
        }

        .console-warning {
            color: #ffaa00;
        }

        .console-info {
            color: #00aaff;
        }

        .visualization-area {
            background: #2a2a2a;
            border-radius: 8px;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .loading-spinner {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: spin 2s linear infinite;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e9ecef;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Sweep Operations Test Suite</h1>
            <p>Comprehensive testing for OpenCascade.js sweep operations and boolean geometry</p>
        </div>

        <div class="main-content">
            <!-- Test Panel -->
            <div class="test-panel">
                <div class="test-category">
                    <h3>🔨 Basic Operations</h3>
                    <button class="test-button" onclick="runTest('door-body')" id="test-door-body">
                        <div class="test-title">Door Body Creation</div>
                        <div class="test-description">Create basic door panel from PANEL layer</div>
                    </button>
                    <button class="test-button" onclick="runTest('tool-brep')" id="test-tool-brep">
                        <div class="test-title">Tool BRep Generation</div>
                        <div class="test-description">Generate 3D tool geometries</div>
                    </button>
                    <button class="test-button" onclick="runTest('simple-sweep')" id="test-simple-sweep">
                        <div class="test-title">Simple Sweep</div>
                        <div class="test-description">Basic material removal operation</div>
                    </button>
                </div>

                <div class="test-category">
                    <h3>⚙️ Advanced Operations</h3>
                    <button class="test-button" onclick="runTest('multi-tool')" id="test-multi-tool">
                        <div class="test-title">Multi-Tool Operations</div>
                        <div class="test-description">Multiple tools with different operations</div>
                    </button>
                    <button class="test-button" onclick="runTest('boolean-ops')" id="test-boolean-ops">
                        <div class="test-title">Boolean Operations</div>
                        <div class="test-description">Union, subtract, intersect tests</div>
                    </button>
                    <button class="test-button" onclick="runTest('complex-geometry')" id="test-complex-geometry">
                        <div class="test-title">Complex Geometry</div>
                        <div class="test-description">Advanced shapes and curves</div>
                    </button>
                </div>

                <div class="test-category">
                    <h3>🚀 Performance Tests</h3>
                    <button class="test-button" onclick="runTest('large-model')" id="test-large-model">
                        <div class="test-title">Large Model Test</div>
                        <div class="test-description">Complex door with many operations</div>
                    </button>
                    <button class="test-button" onclick="runTest('stress-test')" id="test-stress-test">
                        <div class="test-title">Stress Test</div>
                        <div class="test-description">Maximum complexity challenge</div>
                    </button>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="runAllTests()" id="run-all-btn">
                        Run All Tests
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        Clear Results
                    </button>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="tests-passed">0</div>
                        <div class="stat-label">Tests Passed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="tests-failed">0</div>
                        <div class="stat-label">Tests Failed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-time">0s</div>
                        <div class="stat-label">Total Time</div>
                    </div>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="results-panel">
                <div class="results-header">
                    <h2>Test Results</h2>
                    <div class="status-indicator" id="status-indicator">
                        <span>Ready to test</span>
                    </div>
                </div>

                <div class="console-output" id="console-output">
                    <div class="console-line">
                        <span class="console-timestamp">[Ready]</span>
                        <span class="console-info">Sweep Operations Test Suite initialized</span>
                    </div>
                    <div class="console-line">
                        <span class="console-timestamp">[Info]</span>
                        <span class="console-info">Select a test to begin or run all tests</span>
                    </div>
                </div>

                <div class="visualization-area" id="visualization-area">
                    <div>
                        <div style="font-size: 4rem; margin-bottom: 20px;">🔧</div>
                        <div>3D Visualization Area</div>
                        <div style="font-size: 0.9rem; margin-top: 10px; opacity: 0.7;">
                            Run a test to see sweep operation results
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test state
        let isRunning = false;
        let currentTest = '';
        let testResults = new Map();
        let startTime = 0;
        let worker = null;
        let pendingMessages = new Map();

        // Test definitions
        const testDefinitions = {
            'door-body': {
                name: 'Door Body Creation',
                description: 'Creates a basic door panel from PANEL layer geometry',
                duration: 2000
            },
            'tool-brep': {
                name: 'Tool BRep Generation',
                description: 'Tests creation of 3D tool geometries for different tool types',
                duration: 3000
            },
            'simple-sweep': {
                name: 'Simple Sweep Operation',
                description: 'Basic material removal with a single cylindrical tool',
                duration: 4000
            },
            'multi-tool': {
                name: 'Multi-Tool Operations',
                description: 'Tests multiple tools with different operations',
                duration: 6000
            },
            'boolean-ops': {
                name: 'Boolean Operations',
                description: 'Tests union, subtract, and intersect operations',
                duration: 5000
            },
            'complex-geometry': {
                name: 'Complex Geometry',
                description: 'Advanced shapes with curves and fillets',
                duration: 7000
            },
            'large-model': {
                name: 'Large Model Test',
                description: 'Performance test with complex door model',
                duration: 10000
            },
            'stress-test': {
                name: 'Stress Test',
                description: 'Maximum geometry complexity test',
                duration: 15000
            }
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', 'Test suite ready. OCJS Worker will be initialized when first test runs.');
            updateStats();
        });

        function addLog(level, message) {
            const console = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = 'console-line';
            line.innerHTML = `
                <span class="console-timestamp">[${timestamp}]</span>
                <span class="console-${level}">${message}</span>
            `;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const indicator = document.getElementById('status-indicator');
            indicator.className = `status-indicator ${type}`;
            
            let icon = '';
            if (type === 'running') icon = '<span class="spinner">⚙️</span>';
            else if (type === 'success') icon = '✅';
            else if (type === 'error') icon = '❌';
            
            indicator.innerHTML = `${icon} <span>${message}</span>`;
        }

        function updateTestButton(testId, state) {
            const button = document.getElementById(`test-${testId}`);
            if (button) {
                button.className = `test-button ${state}`;
                button.disabled = state === 'running' || isRunning;
            }
        }

        function updateStats() {
            const passed = Array.from(testResults.values()).filter(r => r.success).length;
            const failed = Array.from(testResults.values()).filter(r => !r.success).length;
            const totalTime = Array.from(testResults.values()).reduce((sum, r) => sum + (r.duration || 0), 0);
            
            document.getElementById('tests-passed').textContent = passed;
            document.getElementById('tests-failed').textContent = failed;
            document.getElementById('total-time').textContent = `${(totalTime / 1000).toFixed(1)}s`;
        }

        async function runTest(testId) {
            if (isRunning) return;
            
            const testDef = testDefinitions[testId];
            if (!testDef) {
                addLog('error', `Unknown test: ${testId}`);
                return;
            }

            isRunning = true;
            currentTest = testId;
            startTime = Date.now();
            
            updateStatus(`Running: ${testDef.name}`, 'running');
            updateTestButton(testId, 'running');
            addLog('info', `🧪 Starting test: ${testDef.name}`);
            addLog('info', testDef.description);
            
            // Show loading in visualization
            const vizArea = document.getElementById('visualization-area');
            vizArea.innerHTML = `
                <div class="loading-overlay">
                    <div class="loading-spinner">⚙️</div>
                    <div>Processing ${testDef.name}...</div>
                </div>
            `;

            try {
                // Simulate test execution
                await new Promise(resolve => setTimeout(resolve, testDef.duration));
                
                // Simulate success (in real implementation, this would call OCJS worker)
                const duration = Date.now() - startTime;
                const result = { success: true, duration, message: 'Test completed successfully' };
                testResults.set(testId, result);
                
                updateTestButton(testId, 'success');
                updateStatus(`Completed: ${testDef.name}`, 'success');
                addLog('success', `✅ Test completed: ${testDef.name} (${(duration/1000).toFixed(1)}s)`);
                
                // Show success visualization
                vizArea.innerHTML = `
                    <div style="text-align: center; color: #27ae60;">
                        <div style="font-size: 4rem; margin-bottom: 20px;">✅</div>
                        <div style="font-size: 1.2rem; margin-bottom: 10px;">${testDef.name}</div>
                        <div style="opacity: 0.8;">Completed successfully in ${(duration/1000).toFixed(1)}s</div>
                    </div>
                `;
                
            } catch (error) {
                const duration = Date.now() - startTime;
                const result = { success: false, duration, message: error.message };
                testResults.set(testId, result);
                
                updateTestButton(testId, 'error');
                updateStatus(`Failed: ${testDef.name}`, 'error');
                addLog('error', `❌ Test failed: ${testDef.name} - ${error.message}`);
                
                // Show error visualization
                vizArea.innerHTML = `
                    <div style="text-align: center; color: #e74c3c;">
                        <div style="font-size: 4rem; margin-bottom: 20px;">❌</div>
                        <div style="font-size: 1.2rem; margin-bottom: 10px;">Test Failed</div>
                        <div style="opacity: 0.8;">${error.message}</div>
                    </div>
                `;
            } finally {
                isRunning = false;
                currentTest = '';
                updateStats();
                
                // Re-enable all buttons
                document.querySelectorAll('.test-button').forEach(btn => {
                    if (!btn.classList.contains('success') && !btn.classList.contains('error')) {
                        btn.disabled = false;
                    }
                });
                document.getElementById('run-all-btn').disabled = false;
            }
        }

        async function runAllTests() {
            if (isRunning) return;
            
            const testIds = Object.keys(testDefinitions);
            addLog('info', `🚀 Starting full test suite (${testIds.length} tests)`);
            
            for (const testId of testIds) {
                await runTest(testId);
                if (!isRunning) break; // Allow interruption
                await new Promise(resolve => setTimeout(resolve, 1000)); // Pause between tests
            }
            
            addLog('success', '🎉 Full test suite completed!');
            updateStatus('All tests completed', 'success');
        }

        function clearResults() {
            testResults.clear();
            document.getElementById('console-output').innerHTML = `
                <div class="console-line">
                    <span class="console-timestamp">[Cleared]</span>
                    <span class="console-info">Results cleared. Ready for new tests.</span>
                </div>
            `;
            
            document.querySelectorAll('.test-button').forEach(btn => {
                btn.className = 'test-button';
                btn.disabled = false;
            });
            
            document.getElementById('visualization-area').innerHTML = `
                <div>
                    <div style="font-size: 4rem; margin-bottom: 20px;">🔧</div>
                    <div>3D Visualization Area</div>
                    <div style="font-size: 0.9rem; margin-top: 10px; opacity: 0.7;">
                        Run a test to see sweep operation results
                    </div>
                </div>
            `;
            
            updateStatus('Ready to test', 'info');
            updateStats();
        }
    </script>
</body>
</html>
