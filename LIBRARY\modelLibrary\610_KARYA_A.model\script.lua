-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
     
  minimum = 150
  limit = 550
  
  a = 75
  aa= 35
  aV = 120					-- Vbit Acisi
  ad = 8
  cW = 10                   -- Tarama Bicak capi
  cT = 5                   -- ince bicak capi (Kose Temizleme ve pencere kesim)
  
  h = 30
  
  extEdgeVtoolExist       	= 5   -- Dis kenar Pah islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak kose Radusu var mi? derinlik/0:yok
  
  if (G.parse<PERSON>odelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  local bulge = math.tan(math.pi/8)
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
   
  local notchDepth = 2
  ad = ad-notchDepth
  local notchWidth = 2
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
  local sunkenWidth2 = (ad+notchDepth)*math.tan((math.pi*aV/180)/2)
  local sunkenWidth3 = 0
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
	sunkenWidth3 = extEdgeVtoolExist*math.tan((math.pi*aV/180)/2)
  end
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cT or h<cW then
    print("Tool too large")
    return true
  end
  
  G.setLayer("K_AciliV"..aV)  -- V shaped deep with large angle 
  -- G.setThickness(-(ad+notchDepth))
  -- G.rectangle({0,0},{X,Y})
  
  G.setThickness(0)
  local corner1, corner2 = G.sunkenFrame({sunkenWidth3+a, sunkenWidth3+ust}, {X-a-sunkenWidth3, Y-ust-sunkenWidth3}, ad+notchDepth, aV, vWideDiameter)
  corner1[3] = 0
  corner2[3] = 0
  
  -- Middle angled surface
  G.setThickness(-(ad))
  G.rectangle({a+sunkenWidth3+sunkenWidth2+h+notchWidth, ust+sunkenWidth3+sunkenWidth2+h+notchWidth}, {X-(a+sunkenWidth3+sunkenWidth2+h+notchWidth), Y-(ust+sunkenWidth3+sunkenWidth2+h+notchWidth)})
  
  G.setThickness(-(notchDepth/2))
  G.rectangle({sunkenWidth3+sunkenWidth2+a+sunkenWidth+h+notchWidth, sunkenWidth3+sunkenWidth2+ust+sunkenWidth+h+notchWidth}, {X-(sunkenWidth3+sunkenWidth2+a+sunkenWidth+h+notchWidth), Y-(sunkenWidth3+sunkenWidth2+ust+sunkenWidth+h+notchWidth)})
  
  G.setLayer("K_Freze"..cW.."mm")  -- DEEP cleanup
  G.setThickness(-(ad+notchDepth))
  distance = a+sunkenWidth3+sunkenWidth2+cW/2
  distance1 = ust+sunkenWidth3+sunkenWidth2+cW/2
  point1 = {distance, distance1}
  point2 = {X-distance, Y-distance1}
  G.rectangle(point1,point2)
  
  if h > cW then
    distance = a+sunkenWidth3+sunkenWidth2+h-cW/2
    distance1 = ust+sunkenWidth3+sunkenWidth2+h-cW/2
    point3 = {distance, distance1}
    point4 = {X-distance, Y-distance1}
    G.rectangle(point3,point4)
    k = (h-cW)/(cW/2)
    for i=1, k, 1 do
      point1 = G.ptAdd(point1,{cW/2,cW/2})
      point2 = G.ptSubtract(point2, {cW/2,cW/2})
      if point1[1]>point3[1]-cW/2 then
        break
      end
      G.rectangle(point1,point2)
    end
  end
  
  G.setThickness(-(ad))
  distance = a+sunkenWidth3+sunkenWidth2+h+notchWidth-cW/2
  distance1 = ust+sunkenWidth3+sunkenWidth2+h+notchWidth-cW/2
  point1 = {distance, distance1}
  point2 = {X-distance, Y-distance1}
  G.rectangle(point1,point2)
  
  G.setLayer("K_Freze"..cT.."mm")
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,ad+notchDepth,cT)
  return true
end

------------------------------------------------

require "ADekoDebugMode"
