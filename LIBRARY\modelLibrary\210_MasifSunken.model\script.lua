-- <PERSON><PERSON><PERSON><PERSON><PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePart<PERSON>hape()
  
  a = 5
  marginX = 10
  marginY = 10
  structureSizeX = 35
  structureSizeY = 35
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local notchDepth = 2
  local sunkenDepth = 4
  local cThinDiameter = 5
  local vWideAngle = 120
  local vWideDiameter = 60
  
  G.setLayer("K_AciliV45")
  G.setThickness(-notchDepth)
  G.menderesB(a+marginX, a+marginY, structureSizeX, structureSizeY)
  G.rectangle({a, a}, {X-a, Y-a})
  
  G.setLayer("K_AciliV30")
  G.setThickness(0)
  local point1 = {a+marginX+structureSizeX, a+marginY+structureSizeY}
  local point2 = {X-(a+marginX+structureSizeX), Y-(a+marginY+structureSizeY)}
  local corner1, corner2 = G.sunkenFrame(point1, point2, sunkenDepth, vWideAngle, vWideDiameter)
        corner1, corner2 = G.sunkenFrame(corner1, corner2, sunkenDepth, vWideAngle, vWideDiameter)
		corner1[3] = 0
		corner2[3] = 0
		
  G.setLayer("Cep_Acma")
  G.setThickness(-2*sunkenDepth)
  G.rectangle(corner1, corner2)
  
  G.setLayer("K_TarKoseTemizl".."5mm")
  G.setThickness(0)
  G.cleanCorners(corner1, corner2, 2*sunkenDepth, cThinDiameter)
  
  return true
end

require "ADekoDebugMode"
