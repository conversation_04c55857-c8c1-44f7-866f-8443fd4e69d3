-- Test to check which version of ADekoLib is being loaded

print("=== ADekoLib Version Test ===")

-- Check what ADekoLib is available globally
print("Checking global ADekoLib...")
if ADekoLib then
    print("✓ ADekoLib is available globally")
    if ADekoLib.debug_arcs ~= nil then
        print("✓ ADekoLib has debug_arcs flag (NEW VERSION)")
    else
        print("✗ ADekoLib missing debug_arcs flag (OLD VERSION)")
    end
    
    if ADekoLib.engine ~= nil then
        print("✓ ADekoLib.engine is set")
    else
        print("✗ ADekoLib.engine is nil")
    end
    
    if ADekoLib.arc then
        print("✓ ADekoLib.arc function exists")
    else
        print("✗ ADekoLib.arc function missing")
    end
else
    print("✗ ADekoLib not available globally")
end

-- Test require("ADekoLib")
print("\nTesting require('ADekoLib')...")
local required_adekolib = require("ADekoLib")

if required_adekolib then
    print("✓ require('ADekoLib') successful")
    if required_adekolib.debug_arcs ~= nil then
        print("✓ Required ADekoLib has debug_arcs flag (NEW VERSION)")
    else
        print("✗ Required ADekoLib missing debug_arcs flag (OLD VERSION)")
    end
    
    if required_adekolib.engine ~= nil then
        print("✓ Required ADekoLib.engine is set")
    else
        print("✗ Required ADekoLib.engine is nil")
    end
    
    if required_adekolib.arc then
        print("✓ Required ADekoLib.arc function exists")
    else
        print("✗ Required ADekoLib.arc function missing")
    end
else
    print("✗ require('ADekoLib') failed")
end

-- Check if they're the same object
print("\nChecking if global and required are the same...")
if ADekoLib == required_adekolib then
    print("✓ Global and required ADekoLib are the same object")
else
    print("✗ Global and required ADekoLib are DIFFERENT objects!")
    print("This explains why the fixes aren't working!")
end

function modelMain()
    print("\n=== Inside modelMain ===")
    G = ADekoLib
    
    print("Checking ADekoLib inside modelMain...")
    if G.debug_arcs ~= nil then
        print("✓ ADekoLib has debug_arcs flag (NEW VERSION)")
    else
        print("✗ ADekoLib missing debug_arcs flag (OLD VERSION)")
    end
    
    if G.engine ~= nil then
        print("✓ ADekoLib.engine is set")
    else
        print("✗ ADekoLib.engine is nil")
    end
    
    return true
end

-- Don't require ADekoDebugMode yet - let's see what happens
print("\n=== Test Complete ===")
print("Now requiring ADekoDebugMode to see if it changes anything...")

require "ADekoDebugMode"
