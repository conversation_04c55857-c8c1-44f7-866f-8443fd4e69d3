// Import OpenCascade.js dynamically to avoid issues with WASM loading
// FIXED VERSION 2.9 - <PERSON>ze Debug - Cache Buster: 2025-07-05-22:35
import type { CNCTool, DrawCommand } from '../types'

// Worker message types
export interface OCJSWorkerMessage {
  id: string
  type: 'createDoorBody' | 'createToolGeometry' | 'performSweepOperation' | 'exportGLB' | 'createToolBRep' | 'createAllToolBReps' | 'createPositionedToolShapes' | 'createSimpleBoxGLB'
  data: any
}

export interface OCJSWorkerResponse {
  id: string
  type: 'success' | 'error'
  data?: any
  error?: string
}

// Door body creation parameters
export interface DoorBodyParams {
  width: number
  height: number
  thickness: number
  cornerRadius?: number
  offsetX?: number
  offsetY?: number
}

// Tool geometry creation parameters
export interface ToolGeometryParams {
  tool: CNCTool
  commands: DrawCommand[]
  depth: number
  isBottomFace: boolean
  doorWidth?: number
  doorHeight?: number
}

// Sweep operation parameters
export interface SweepOperationParams {
  doorBodyShape: any // OC shape reference
  toolGeometries: any[] // Array of OC tool shapes
  operation: 'subtract' | 'union' | 'intersect'
}

// Tool BRep creation parameters
export interface ToolBRepParams {
  tool: CNCTool
  height?: number
  includeGLB?: boolean
}

// All tools BRep creation parameters
export interface AllToolBRepsParams {
  tools: CNCTool[]
  height?: number
  includeGLB?: boolean
}

let oc: any = null
let isInitialized = false

// Shape cache for storing OpenCascade shapes between operations
const shapeCache = new Map<string, any>()
const toolCache = new Map<string, any>()



// Initialize OpenCascade.js
async function initializeOC() {
  if (!isInitialized) {
    try {
      // Dynamic import to handle WASM loading properly
      const { default: initOpenCascade } = await import('opencascade.js')
      oc = await initOpenCascade()
      isInitialized = true
      console.log('OpenCascade.js initialized in worker')
    } catch (error) {
      console.error('Failed to initialize OpenCascade.js:', error)
      throw error
    }
  }
  return oc
}

// Create door body from PANEL layer data
function createDoorBody(params: DoorBodyParams): any {
  const { width, height, thickness, cornerRadius: _cornerRadius = 0 } = params

  try {
    // For now, create a simple rectangular door body to avoid OpenCascade.js constructor issues
    // TODO: Add rounded corners support once the correct API is determined
    console.log('Creating box with dimensions:', width, height, thickness)

    // Create a simple box
    const box = new oc.BRepPrimAPI_MakeBox_2(width, height, thickness)
    console.log(`🚪 Door body dimensions: W=${width}m, H=${height}m, T=${thickness}m`)

    // Center the box at origin
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(-width/2, -height/2, -thickness/2))
    const loc = new oc.TopLoc_Location_2(tf)
    const centeredBox = box.Shape().Moved(loc, false)
    console.log(`🚪 Door body centered at origin: X=[-${width/2}, ${width/2}], Y=[-${height/2}, ${height/2}], Z=[-${thickness/2}, ${thickness/2}]`)

    // Cache the door body shape
    const shapeId = `door_${Date.now()}`
    shapeCache.set(shapeId, centeredBox)
    console.log('✅ Door body cached with ID:', shapeId)

    return {
      success: true,
      shapeId: shapeId,
      dimensions: { width, height, thickness }
    }
  } catch (error) {
    console.error('Error creating door body:', error)
    throw error
  }
}

// Create tool geometry based on tool type and commands
function createToolGeometry(params: ToolGeometryParams): any {
  const { tool, commands, depth, isBottomFace, doorWidth, doorHeight } = params
  
  try {
    const toolGeometries: any[] = []
    
    commands.forEach(command => {
      let toolShape: any = null
      
      if (tool.shape === 'cylindrical') {
        // Create cylindrical tool
        const radius = tool.diameter / 2
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, depth)
        toolShape = cylinder.Shape()
      } else if (tool.shape === 'conical') {
        // Create conical tool (V-bit)
        const topRadius = tool.diameter / 2
        const bottomRadius = 0.1 // Small tip radius
        const cone = new oc.BRepPrimAPI_MakeCone_1(bottomRadius, topRadius, depth)
        toolShape = cone.Shape()
      } else if (tool.shape === 'ballnose') {
        // Create ballnose tool (hemisphere + cylinder)
        const radius = tool.diameter / 2
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, depth - radius)
        const sphere = new oc.BRepPrimAPI_MakeSphere_1(radius)
        
        // Position sphere at bottom of cylinder
        const tf = new oc.gp_Trsf_1()
        tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -radius))
        const loc = new oc.TopLoc_Location_2(tf)
        const movedSphere = sphere.Shape().Moved(loc, false)
        
        // Fuse cylinder and sphere
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          cylinder.Shape(),
          movedSphere,
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        toolShape = fuse.Shape()
      }
      
      if (toolShape) {
        // Position tool based on command coordinates
        const tf = new oc.gp_Trsf_1()
        
        if (command.command_type === 'line') {
          // Convert from Lua coordinate system (0 to doorWidth/doorHeight) to centered coordinate system
          const midX = (command.x1 + command.x2) / 2 - (doorWidth || 0) / 2
          const midZ = (command.y1 + command.y2) / 2 - (doorHeight || 0) / 2
          const posY = isBottomFace ? -depth : 0
          console.log(`🔧 Positioning line tool at: X=${midX}, Y=${posY}, Z=${midZ} (converted from Lua coords)`)
          tf.SetTranslation_1(new oc.gp_Vec_4(midX, posY, midZ))
        } else if (command.command_type === 'circle') {
          const posY = isBottomFace ? -depth : 0
          // Convert from Lua coordinate system to centered coordinate system
          const centerX = command.x1 - (doorWidth || 0) / 2
          const centerZ = command.y1 - (doorHeight || 0) / 2
          console.log(`🔧 Positioning circle tool at: X=${centerX}, Y=${posY}, Z=${centerZ} (converted from Lua coords)`)
          tf.SetTranslation_1(new oc.gp_Vec_4(centerX, posY, centerZ))
        }
        
        const loc = new oc.TopLoc_Location_2(tf)
        const positionedTool = toolShape.Moved(loc, false)
        toolGeometries.push(positionedTool)
      }
    })
    
    // Return serializable data instead of OpenCascade objects
    return {
      count: toolGeometries.length,
      success: true,
      // Don't return the actual geometries as they can't be serialized
      // Store them in a global cache for later use
      geometryIds: toolGeometries.map((_, index) => `tool_${Date.now()}_${index}`)
    }
  } catch (error) {
    console.error('Error creating tool geometry:', error)
    throw error
  }
}

// Helper function to determine operation type from layer name
function determineOperationFromLayerName(layerName: string): string {
  const layer = layerName.toLowerCase()

  if (layer.includes('drill') || layer.includes('delme')) {
    return 'drilling'
  } else if (layer.includes('pocket') || layer.includes('cep')) {
    return 'pocketing'
  } else if (layer.includes('k_') || layer.includes('groove') || layer.includes('kanal')) {
    return 'grooving'
  } else if (layer.includes('h_') || layer.includes('contour')) {
    return 'profiling'
  } else if (layer.includes('v') && (layer.includes('acili') || layer.includes('chamfer'))) {
    return 'chamfering'
  } else if (layer.includes('radius') || layer.includes('fillet')) {
    return 'filleting'
  }

  return 'profiling' // Default operation
}

// Helper function to generate spiral toolpath positions for pocketing
function generateSpiralToolpath(centerX: number, centerY: number, circleRadius: number, toolRadius: number): Array<{x: number, y: number}> {
  const positions: Array<{x: number, y: number}> = []
  const stepover = toolRadius * 1.6 // 60% overlap for good surface finish
  const maxRadius = circleRadius - toolRadius // Stay within circle bounds

  // Generate spiral from center outward
  let currentRadius = 0
  let angle = 0
  const angleStep = Math.PI / 16 // 16 steps per half circle

  while (currentRadius <= maxRadius) {
    const x = centerX + currentRadius * Math.cos(angle)
    const y = centerY + currentRadius * Math.sin(angle)
    positions.push({ x, y })

    // Increment angle and radius for spiral
    angle += angleStep
    currentRadius += stepover / (2 * Math.PI) // Gradual radius increase

    // Prevent infinite loop
    if (positions.length > 1000) break
  }

  return positions
}

// Helper function to create circular groove shape (annular cylinder for surface groove)
function createCircularGrooveShape(centerX: number, centerY: number, grooveRadius: number, toolRadius: number, height: number): any {
  try {
    // Create an annular cylinder (ring) that represents the groove cut
    // This creates a cylindrical groove on the surface, not a torus
    const outerRadius = grooveRadius + toolRadius / 2
    const innerRadius = grooveRadius - toolRadius / 2

    // Ensure inner radius is positive
    const actualInnerRadius = Math.max(innerRadius, toolRadius / 4)

    console.log(`🔧 Creating groove: outer=${outerRadius*1000}mm, inner=${actualInnerRadius*1000}mm, height=${height*1000}mm`)

    // Create outer cylinder
    const outerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(outerRadius, height)

    // Create inner cylinder to subtract
    const innerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(actualInnerRadius, height)

    // Subtract inner from outer to create annular groove
    const cut = new oc.BRepAlgoAPI_Cut_3(
      outerCylinder.Shape(),
      innerCylinder.Shape(),
      new oc.Message_ProgressRange_1()
    )
    cut.Build(new oc.Message_ProgressRange_1())

    // Position the groove at the correct location
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, 0))
    const loc = new oc.TopLoc_Location_2(tf)

    return cut.Shape().Moved(loc, false)
  } catch (error) {
    console.error('Error creating groove shape:', error)
    // Fallback: create a simple cylinder at the groove radius
    const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(toolRadius, height)
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(centerX + grooveRadius, centerY, 0))
    const loc = new oc.TopLoc_Location_2(tf)
    return cylinder.Shape().Moved(loc, false)
  }
}

// Helper function to create pocket shape from spiral toolpath
function createPocketShape(centerX: number, centerY: number, circleRadius: number, toolRadius: number, height: number): any {
  try {
    // For now, create a simple cylinder that represents the pocketed area
    // TODO: Implement proper spiral toolpath shape generation
    const pocketRadius = circleRadius - toolRadius / 2
    const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(pocketRadius, height)

    // Position the cylinder at the center
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, 0))
    const loc = new oc.TopLoc_Location_2(tf)

    return cylinder.Shape().Moved(loc, false)
  } catch (error) {
    console.error('Error creating pocket shape:', error)
    // Fallback: create a simple cylinder
    const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(circleRadius, height)
    return cylinder.Shape()
  }
}

// Create positioned tool shapes for each command
function createPositionedToolShapes(params: {
  tool: CNCTool
  commands: DrawCommand[]
  depth: number
  isBottomFace: boolean
  doorWidth: number
  doorHeight: number
  doorOffsetX?: number
  doorOffsetY?: number
}): any {
  const { tool, commands, depth, isBottomFace, doorWidth, doorHeight, doorOffsetX = 0, doorOffsetY = 0 } = params

  try {
    const positionedShapes: any[] = []
    const shapeIds: string[] = []
    // Convert tool diameter from mm to meters
    const radius = (tool.diameter / 1000) / 2
    console.log(`🔧 Tool ${tool.name}: diameter=${tool.diameter}mm, radius=${radius}m (${radius*1000}mm)`)

    console.log(`🔧 Creating ${commands.length} positioned ${tool.shape} tool shapes for ${tool.name}`)

    commands.forEach((command, index) => {
      let toolShape: any = null

      // Create base tool shape with appropriate height based on operation
      const doorThickness = 0.02 // 20mm door thickness in meters

      // Determine operation type first to set correct tool height
      const layerName = command.layer_name || tool.name || ''
      const operation = determineOperationFromLayerName(layerName)

      // Set tool height based on operation type
      let toolHeight: number
      if (operation === 'drilling') {
        toolHeight = depth // Use actual drilling depth for drilling operations
      } else {
        toolHeight = Math.max(depth, doorThickness * 1.5) // Ensure tool is tall enough for other operations
      }

      switch (tool.shape) {
        case 'cylindrical':
          const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, toolHeight)
          toolShape = cylinder.Shape()
          break

        case 'conical':
          const conicalTool = tool as any
          const tipRadius = (conicalTool.tipDiameter || 0.1) / 2
          const cone = new oc.BRepPrimAPI_MakeCone_1(tipRadius, radius, toolHeight)
          toolShape = cone.Shape()
          break

        case 'ballnose':
          const ballnoseTool = tool as any
          const ballRadius = ballnoseTool.ballRadius || radius
          const cylHeight = Math.max(toolHeight - ballRadius, 0)
          const ballCylinder = new oc.BRepPrimAPI_MakeCylinder_1(ballRadius, cylHeight)
          const hemisphere = new oc.BRepPrimAPI_MakeSphere_1(ballRadius)

          // Position hemisphere at bottom
          const tf = new oc.gp_Trsf_1()
          tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -ballRadius))
          const loc = new oc.TopLoc_Location_2(tf)
          const movedHemisphere = hemisphere.Shape().Moved(loc, false)

          // Fuse cylinder and hemisphere
          const fuse = new oc.BRepAlgoAPI_Fuse_3(
            ballCylinder.Shape(),
            movedHemisphere,
            new oc.Message_ProgressRange_1()
          )
          fuse.Build(new oc.Message_ProgressRange_1())
          toolShape = fuse.Shape()
          break

        default:
          console.warn(`Unknown tool shape: ${tool.shape}`)
          return
      }

      if (toolShape) {
        // Position tool based on command coordinates
        const positionTf = new oc.gp_Trsf_1()

        if (command.command_type === 'line') {
          // Convert from Lua coordinate system (mm) to centered coordinate system (meters)
          // Lua coordinates are in mm and include door offset, door dimensions are in meters
          const midX_mm = (command.x1 + command.x2) / 2
          const midZ_mm = (command.y1 + command.y2) / 2

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          // Subtract door offset first, then center relative to door size
          const midX = ((midX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const midY = ((midZ_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

          // Position tool to cut from the surface into the door
          // Door is centered at origin with Z range [-thickness/2, thickness/2]
          // Position tool so it starts at the surface and extends into the door
          const doorThickness = 0.02 // 20mm door thickness in meters
          const toolHeight = Math.max(depth, doorThickness * 1.5)

          // For bottom face operations, position tool to start at bottom surface and extend upward
          // For top face operations, position tool to start at top surface and extend downward
          const posZ = isBottomFace ?
            (-doorThickness/2 + toolHeight/2) : // Bottom face: start at -0.01, extend to +0.02
            (doorThickness/2 - toolHeight/2)    // Top face: start at +0.01, extend to -0.02

          console.log(`🔧 Positioning line tool ${index} at: X=${midX.toFixed(4)}m, Y=${midY.toFixed(4)}m, Z=${posZ.toFixed(4)}m (from ${midX_mm}, ${midZ_mm} mm, offset: ${doorOffsetX}, ${doorOffsetY}, door: ${doorWidth_mm}x${doorHeight_mm} mm, toolHeight: ${toolHeight.toFixed(4)}m)`)
          positionTf.SetTranslation_1(new oc.gp_Vec_4(midX, midY, posZ))
        } else if (command.command_type === 'circle') {
          // Convert from Lua coordinate system (mm) to centered coordinate system (meters)
          const centerX_mm = command.x1
          const centerY_mm = command.y1
          const circleRadius_mm = command.radius || 0 // Circle radius from DrawCommand

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000
          const circleRadius = circleRadius_mm / 1000 // Convert to meters

          // Position tool to cut from the surface into the door
          const doorThickness = 0.02 // 20mm door thickness in meters
          const toolHeight = Math.max(depth, doorThickness * 1.5)

          // For bottom face operations, position tool to start at bottom surface and extend upward
          // For top face operations, position tool to start at top surface and extend downward
          const posZ = isBottomFace ?
            (-doorThickness/2 + toolHeight/2) : // Bottom face: start at -0.01, extend to +0.02
            (doorThickness/2 - toolHeight/2)    // Top face: start at +0.01, extend to -0.02

          const toolRadius = radius // Tool radius in meters
          console.log(`🔧 Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling' || circleRadius <= 0) {
            // DRILLING: Single point operation at circle center
            // Use actual drilling depth, not toolHeight
            const actualDrillDepth = depth // Use the specified depth (10mm = 0.01m)
            const drillZ = isBottomFace ?
              (-doorThickness/2) : // Bottom face: start at bottom surface
              (doorThickness/2 - actualDrillDepth) // Top face: start at top surface, extend down by drill depth only
            console.log(`🔧 DRILLING: Positioning drill tool ${index} at center: X=${centerX.toFixed(4)}m, Y=${centerY.toFixed(4)}m, Z=${drillZ.toFixed(4)}m (depth=${actualDrillDepth*1000}mm, doorThickness=${doorThickness*1000}mm, isBottomFace=${isBottomFace})`)
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // GROOVE: Create circular groove shape around circle perimeter (borderline)
            console.log(`🔧 GROOVE: Creating groove toolpath around circle perimeter (radius=${circleRadius_mm}mm, tool=${toolRadius*1000}mm)`)

            if (circleRadius > toolRadius) {
              // Create shallow circular groove shape (surface cut only)
              const grooveRadius = circleRadius
              const grooveDepth = Math.min(depth, toolRadius) // Limit groove depth to tool radius or specified depth
              toolShape = createCircularGrooveShape(centerX, centerY, grooveRadius, toolRadius, grooveDepth)
              console.log(`🔧 GROOVE: Created circular groove shape at radius ${grooveRadius*1000}mm, depth=${grooveDepth*1000}mm`)

              // Position the groove to start at surface and extend into material
              // For bottom face: start at bottom surface (-doorThickness/2) and extend upward (positive Z)
              // For top face: start at top surface (+doorThickness/2) and extend downward (negative Z)
              const surfaceZ = isBottomFace ?
                (-doorThickness/2) : // Bottom: start at bottom surface
                (doorThickness/2 - grooveDepth)  // Top: start at top surface, extend down by groove depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, surfaceZ))
            } else {
              // Circle too small for groove - use drill shape at center
              console.log(`🔧 GROOVE→DRILL: Circle too small for grooving, using drill shape at center`)
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
            }

          } else if (operation === 'pocketing' || operation === 'pocket') {
            // POCKET: Create pocket shape to clear entire circle area
            console.log(`🔧 POCKET: Creating pocket shape inside circle (radius=${circleRadius_mm}mm, tool=${toolRadius*1000}mm)`)

            if (circleRadius > toolRadius * 1.5) {
              // Create shallow pocket shape (surface cut only)
              const pocketDepth = Math.min(depth, circleRadius / 2) // Reasonable pocket depth
              toolShape = createPocketShape(centerX, centerY, circleRadius, toolRadius, pocketDepth)
              console.log(`🔧 POCKET: Created pocket shape with radius ${circleRadius*1000}mm, depth=${pocketDepth*1000}mm`)

              // Position the pocket to start at surface and extend into material
              // For bottom face: start at bottom surface (-doorThickness/2) and extend upward
              // For top face: start at top surface (+doorThickness/2) and extend downward
              const surfaceZ = isBottomFace ?
                (-doorThickness/2) : // Bottom: start at bottom surface
                (doorThickness/2 - pocketDepth)  // Top: start at top surface, extend down by pocket depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, surfaceZ))
            } else {
              // Circle too small for pocketing - use drill shape at center
              console.log(`🔧 POCKET→DRILL: Circle too small for pocketing, using drill shape at center`)
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
            }

          } else {
            // Default operation (profiling/finishing) - drill at center
            console.log(`🔧 DEFAULT: Unknown operation '${operation}', drilling at center`)
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
          }

          console.log(`🔧 Circle operation: center=(${centerX_mm}, ${centerY_mm})mm, radius=${circleRadius_mm}mm, tool=${tool.name}(${toolRadius*1000}mm)`)
        }

        const positionLoc = new oc.TopLoc_Location_2(positionTf)
        const positionedTool = toolShape.Moved(positionLoc, false)
        positionedShapes.push(positionedTool)

        // Cache the positioned tool with unique ID
        const toolId = `positioned_tool_${tool.name}_${index}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        toolCache.set(toolId, positionedTool)
        shapeIds.push(toolId)
      }
    })

    console.log(`✅ Created ${positionedShapes.length} positioned tool shapes`)

    return {
      success: true,
      count: positionedShapes.length,
      shapeIds: shapeIds
    }
  } catch (error) {
    console.error('Error creating positioned tool shapes:', error)
    throw error
  }
}

// Create individual tool BRep for sweep operations
function createToolBRep(params: ToolBRepParams): any {
  const { tool, height = 50, includeGLB = false } = params

  try {
    let toolShape: any = null
    const radius = tool.diameter / 2

    console.log(`Creating BRep for ${tool.shape} tool: ${tool.name} (⌀${tool.diameter}mm)`)

    switch (tool.shape) {
      case 'cylindrical':
        // Create cylindrical tool BRep
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = cylinder.Shape()
        break

      case 'conical':
        // Create conical tool (V-bit) BRep
        const conicalTool = tool as any // ConicalTool
        const tipRadius = (conicalTool.tipDiameter || 0.1) / 2
        const cone = new oc.BRepPrimAPI_MakeCone_1(tipRadius, radius, height)
        toolShape = cone.Shape()
        break

      case 'ballnose':
        // Create ballnose tool BRep (hemisphere + cylinder)
        const ballnoseTool = tool as any // BallnoseTool
        const ballRadius = ballnoseTool.ballRadius || radius

        // Create cylinder part
        const cylHeight = Math.max(height - ballRadius, 0)
        const ballCylinder = new oc.BRepPrimAPI_MakeCylinder_1(ballRadius, cylHeight)

        // Create hemisphere part
        const hemisphere = new oc.BRepPrimAPI_MakeSphere_1(ballRadius)

        // Position hemisphere at bottom
        const tf = new oc.gp_Trsf_1()
        tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -ballRadius))
        const loc = new oc.TopLoc_Location_2(tf)
        const movedHemisphere = hemisphere.Shape().Moved(loc, false)

        // Fuse cylinder and hemisphere
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          ballCylinder.Shape(),
          movedHemisphere,
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        if (fuse.IsDone()) {
          toolShape = fuse.Shape()
        } else {
          toolShape = ballCylinder.Shape() // Fallback to cylinder
        }
        break

      case 'radial':
        // Create radial tool BRep (cylinder with rounded corners)
        // radialTool and cornerRadius calculation removed as they're not used in current implementation

        // For now, create a simple cylinder (could be enhanced with actual corner radius)
        const radialCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = radialCylinder.Shape()
        break

      case 'special':
        // Create special tool BRep (default to cylinder for now)
        const specialCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = specialCylinder.Shape()
        break

      default:
        // Default to cylindrical
        const defaultCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = defaultCylinder.Shape()
    }

    if (!toolShape) {
      throw new Error(`Failed to create BRep for tool: ${tool.name}`)
    }

    // Cache the tool shape
    const shapeId = `tool_${tool.id}_${Date.now()}`
    toolCache.set(shapeId, toolShape)
    console.log(`✅ Tool BRep cached with ID: ${shapeId}`)

    // Generate GLB if requested
    let glbData: ArrayBuffer | null = null
    if (includeGLB) {
      glbData = exportToGLB(toolShape)
    }

    return {
      success: true,
      toolId: tool.id,
      toolName: tool.name,
      toolShape: tool.shape,
      diameter: tool.diameter,
      height: height,
      shapeId: shapeId,
      glbData: glbData,
      // Actual BRep shape is stored in toolCache for later use
    }
  } catch (error) {
    console.error(`Error creating BRep for tool ${tool.name}:`, error)
    throw error
  }
}

// Create BReps for all tools
function createAllToolBReps(params: AllToolBRepsParams): any {
  const { tools, height = 50, includeGLB = false } = params

  try {
    const results: any[] = []

    console.log(`Creating BReps for ${tools.length} tools...`)

    tools.forEach(tool => {
      try {
        const toolResult = createToolBRep({ tool, height, includeGLB })
        results.push(toolResult)
        console.log(`✓ Created BRep for ${tool.name}`)
      } catch (error) {
        console.error(`✗ Failed to create BRep for ${tool.name}:`, error)
        results.push({
          success: false,
          toolId: tool.id,
          toolName: tool.name,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    })

    return {
      success: true,
      count: results.length,
      successCount: results.filter(r => r.success).length,
      results: results
    }
  } catch (error) {
    console.error('Error creating tool BReps:', error)
    throw error
  }
}

// Perform sweep operation (boolean subtraction)
function performSweepOperation(params: SweepOperationParams): any {
  const { doorBodyShape, toolGeometries, operation } = params

  try {
    console.log(`🔧 Starting sweep operation: ${operation}`)

    // Get the door body shape from cache
    let resultShape: any = null
    if (typeof doorBodyShape === 'string') {
      resultShape = shapeCache.get(doorBodyShape)
      if (!resultShape) {
        throw new Error(`Door body shape not found in cache: ${doorBodyShape}`)
      }
    } else if (doorBodyShape && doorBodyShape.shapeId) {
      resultShape = shapeCache.get(doorBodyShape.shapeId)
      if (!resultShape) {
        throw new Error(`Door body shape not found in cache: ${doorBodyShape.shapeId}`)
      }
    } else {
      // Assume doorBodyShape is the actual shape
      resultShape = doorBodyShape
    }

    console.log(`🔧 Processing ${toolGeometries.length} tool geometries`)

    // Debug: Log initial processing
    console.log(`🔍 Starting CSG operations on door body`)

    let successfulOperations = 0

    toolGeometries.forEach((toolGeometry, index) => {
      try {
        let toolShape: any = null

        // Get tool shape from cache - check both toolCache and shapeCache
        if (typeof toolGeometry === 'string') {
          toolShape = toolCache.get(toolGeometry) || shapeCache.get(toolGeometry)
          console.log(`🔍 Looking for tool shape ID: ${toolGeometry}`)
          console.log(`🔍 Found in toolCache: ${!!toolCache.get(toolGeometry)}`)
          console.log(`🔍 Found in shapeCache: ${!!shapeCache.get(toolGeometry)}`)
        } else if (toolGeometry && toolGeometry.shapeId) {
          toolShape = toolCache.get(toolGeometry.shapeId) || shapeCache.get(toolGeometry.shapeId)
          console.log(`🔍 Looking for tool shape ID: ${toolGeometry.shapeId}`)
        } else {
          // Assume toolGeometry is the actual shape
          toolShape = toolGeometry
          console.log(`🔍 Using direct tool shape object`)
        }

        if (!toolShape) {
          console.warn(`⚠️ Tool shape ${index} not found in any cache, skipping`)
          console.log(`🔍 Available toolCache keys: ${Array.from(toolCache.keys()).join(', ')}`)
          console.log(`🔍 Available shapeCache keys: ${Array.from(shapeCache.keys()).join(', ')}`)
          return
        }

        console.log(`✅ Found tool shape ${index} for operation`)

        if (operation === 'subtract') {
          console.log(`🔧 Attempting to subtract tool ${index}...`)
          const cut = new oc.BRepAlgoAPI_Cut_3(
            resultShape,
            toolShape,
            new oc.Message_ProgressRange_1()
          )
          cut.Build(new oc.Message_ProgressRange_1())
          if (cut.IsDone()) {
            const newShape = cut.Shape()

            console.log(`✅ Tool ${index} subtracted successfully`)

            // Simple validation that we have a valid shape after subtraction
            if (newShape && !newShape.IsNull()) {
              console.log(`📊 Boolean operation completed - result shape is valid`)
            } else {
              console.warn(`⚠️ Boolean operation may have failed - invalid result shape`)
            }

            resultShape = newShape
            successfulOperations++
          } else {
            console.warn(`⚠️ Tool ${index} subtraction failed - boolean operation did not complete`)
          }
        } else if (operation === 'union') {
          const fuse = new oc.BRepAlgoAPI_Fuse_3(
            resultShape,
            toolShape,
            new oc.Message_ProgressRange_1()
          )
          fuse.Build(new oc.Message_ProgressRange_1())
          if (fuse.IsDone()) {
            resultShape = fuse.Shape()
            console.log(`✅ Tool ${index} union completed successfully`)
          } else {
            console.warn(`⚠️ Tool ${index} union failed`)
          }
        }
      } catch (toolError) {
        console.error(`❌ Error processing tool ${index}:`, toolError)
      }
    })

    // Cache the result shape
    const resultShapeId = `result_${Date.now()}`
    shapeCache.set(resultShapeId, resultShape)
    console.log(`✅ Sweep operation completed, result cached with ID: ${resultShapeId}`)
    console.log(`📊 Successfully processed ${successfulOperations} out of ${toolGeometries.length} tools`)

    if (successfulOperations === 0) {
      console.warn(`⚠️ No tools were successfully processed - result may be unchanged`)
    }

    return {
      success: true,
      shapeId: resultShapeId,
      operation: operation,
      toolsProcessed: successfulOperations
    }
  } catch (error) {
    console.error('❌ Error performing sweep operation:', error)
    throw error
  }
}

// Export shape to GLB format
function exportToGLB(shapeOrId: any): ArrayBuffer {
  try {
    console.log('🔧 Exporting to GLB...')

    // Get the actual shape
    let shape: any = null
    if (typeof shapeOrId === 'string') {
      // It's a shape ID, get from cache
      shape = shapeCache.get(shapeOrId)
      if (!shape) {
        throw new Error(`Shape not found in cache: ${shapeOrId}`)
      }
    } else if (shapeOrId && shapeOrId.shapeId) {
      // It's an object with shapeId
      shape = shapeCache.get(shapeOrId.shapeId)
      if (!shape) {
        throw new Error(`Shape not found in cache: ${shapeOrId.shapeId}`)
      }
    } else {
      // Assume it's the actual shape
      shape = shapeOrId
    }

    if (!shape) {
      throw new Error('No valid shape provided for GLB export')
    }

    console.log(`🔍 Exporting final shape to GLB format`)

    // Create a document and add our shape
    const docHandle = new oc.Handle_TDocStd_Document_2(
      new oc.TDocStd_Document(new oc.TCollection_ExtendedString_1())
    )
    const shapeTool = oc.XCAFDoc_DocumentTool.ShapeTool(docHandle.get().Main()).get()
    shapeTool.SetShape(shapeTool.NewShape(), shape)

    // Mesh the shape with higher resolution for better wireframe visibility
    // Using smaller deflection values for finer mesh detail
    new oc.BRepMesh_IncrementalMesh_2(shape, 0.01, false, 0.01, false)

    // Export GLB file
    const cafWriter = new oc.RWGltf_CafWriter(
      new oc.TCollection_AsciiString_2('./result.glb'),
      true
    )
    cafWriter.Perform_2(
      docHandle,
      new oc.TColStd_IndexedDataMapOfStringString_1(),
      new oc.Message_ProgressRange_1()
    )

    // Read the GLB file from virtual file system
    const glbFile = oc.FS.readFile('./result.glb', { encoding: 'binary' })
    console.log('✅ GLB export completed, size:', glbFile.buffer.byteLength, 'bytes')
    return glbFile.buffer
  } catch (error) {
    console.error('❌ Error exporting to GLB:', error)
    throw error
  }
}

// Create a simple box GLB file
function createSimpleBoxGLB(params: DoorBodyParams): ArrayBuffer {
  try {
    const { width, height, thickness } = params
    console.log(`Creating simple box GLB: ${width} x ${height} x ${thickness}`)

    // Create a minimal valid GLB file with a simple box
    // GLB format: 12-byte header + JSON chunk + BIN chunk

    // Create a simple box geometry in JSON format
    const gltfJson = {
      asset: { version: "2.0" },
      scene: 0,
      scenes: [{ nodes: [0] }],
      nodes: [{
        mesh: 0,
        translation: [0, 0, 0]
      }],
      meshes: [{
        primitives: [{
          attributes: { POSITION: 0 },
          indices: 1
        }]
      }],
      accessors: [
        {
          bufferView: 0,
          componentType: 5126, // FLOAT
          count: 8,
          type: "VEC3",
          min: [-width/2, -height/2, 0],
          max: [width/2, height/2, thickness]
        },
        {
          bufferView: 1,
          componentType: 5123, // UNSIGNED_SHORT
          count: 36,
          type: "SCALAR"
        }
      ],
      bufferViews: [
        {
          buffer: 0,
          byteOffset: 0,
          byteLength: 96 // 8 vertices * 3 components * 4 bytes
        },
        {
          buffer: 0,
          byteOffset: 96,
          byteLength: 72 // 36 indices * 2 bytes
        }
      ],
      buffers: [{
        byteLength: 168 // 96 + 72
      }]
    }

    // Create vertex data for a box
    const vertices = new Float32Array([
      // Bottom face
      -width/2, -height/2, 0,
      width/2, -height/2, 0,
      width/2, height/2, 0,
      -width/2, height/2, 0,
      // Top face
      -width/2, -height/2, thickness,
      width/2, -height/2, thickness,
      width/2, height/2, thickness,
      -width/2, height/2, thickness
    ])

    // Create indices for a box (12 triangles)
    const indices = new Uint16Array([
      // Bottom face
      0, 1, 2, 0, 2, 3,
      // Top face
      4, 6, 5, 4, 7, 6,
      // Front face
      0, 4, 5, 0, 5, 1,
      // Back face
      2, 6, 7, 2, 7, 3,
      // Left face
      0, 3, 7, 0, 7, 4,
      // Right face
      1, 5, 6, 1, 6, 2
    ])

    // Convert JSON to buffer
    const jsonString = JSON.stringify(gltfJson)
    const jsonBuffer = new TextEncoder().encode(jsonString)

    // Pad JSON to 4-byte boundary
    const jsonPadding = (4 - (jsonBuffer.length % 4)) % 4
    const paddedJsonLength = jsonBuffer.length + jsonPadding

    // Create binary data
    const binaryData = new ArrayBuffer(vertices.byteLength + indices.byteLength)
    const binaryView = new Uint8Array(binaryData)
    binaryView.set(new Uint8Array(vertices.buffer), 0)
    binaryView.set(new Uint8Array(indices.buffer), vertices.byteLength)

    // Pad binary to 4-byte boundary
    const binaryPadding = (4 - (binaryData.byteLength % 4)) % 4
    const paddedBinaryLength = binaryData.byteLength + binaryPadding

    // Calculate total size
    const totalSize = 12 + 8 + paddedJsonLength + 8 + paddedBinaryLength

    // Create GLB buffer
    const glbBuffer = new ArrayBuffer(totalSize)
    const view = new DataView(glbBuffer)
    const uint8View = new Uint8Array(glbBuffer)

    let offset = 0

    // GLB header
    view.setUint32(offset, 0x46546C67, true) // "glTF" magic
    offset += 4
    view.setUint32(offset, 2, true) // version
    offset += 4
    view.setUint32(offset, totalSize, true) // total length
    offset += 4

    // JSON chunk header
    view.setUint32(offset, paddedJsonLength, true) // chunk length
    offset += 4
    view.setUint32(offset, 0x4E4F534A, true) // "JSON" type
    offset += 4

    // JSON chunk data
    uint8View.set(jsonBuffer, offset)
    offset += jsonBuffer.length

    // JSON padding
    for (let i = 0; i < jsonPadding; i++) {
      uint8View[offset++] = 0x20 // space character
    }

    // Binary chunk header
    view.setUint32(offset, paddedBinaryLength, true) // chunk length
    offset += 4
    view.setUint32(offset, 0x004E4942, true) // "BIN\0" type
    offset += 4

    // Binary chunk data
    uint8View.set(new Uint8Array(binaryData), offset)
    offset += binaryData.byteLength

    // Binary padding
    for (let i = 0; i < binaryPadding; i++) {
      uint8View[offset++] = 0
    }

    console.log(`Created GLB file: ${glbBuffer.byteLength} bytes`)
    return glbBuffer

  } catch (error) {
    console.error('Error creating simple box GLB:', error)
    throw error
  }
}

// Worker message handler
self.onmessage = async (event: MessageEvent<OCJSWorkerMessage>) => {
  const { id, type, data } = event.data

  try {
    console.log(`Worker received message: ${type}`)
    await initializeOC()

    let result: any = null

    switch (type) {
      case 'createDoorBody':
        result = createDoorBody(data as DoorBodyParams)
        break
      case 'createToolGeometry':
        result = createToolGeometry(data as ToolGeometryParams)
        break
      case 'performSweepOperation':
        result = performSweepOperation(data as SweepOperationParams)
        break
      case 'exportGLB':
        result = exportToGLB(data)
        break
      case 'createToolBRep':
        result = createToolBRep(data as ToolBRepParams)
        break
      case 'createAllToolBReps':
        result = createAllToolBReps(data as AllToolBRepsParams)
        break
      case 'createPositionedToolShapes':
        result = createPositionedToolShapes(data)
        break
      default:
        throw new Error(`Unknown operation type: ${type}`)
    }

    const response: OCJSWorkerResponse = {
      id,
      type: 'success',
      data: result
    }

    console.log(`Worker completed: ${type}`)
    self.postMessage(response)
  } catch (error) {
    console.error(`Worker error for ${type}:`, error)
    const response: OCJSWorkerResponse = {
      id,
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }

    self.postMessage(response)
  }
}

// Handle worker errors
self.onerror = (error) => {
  console.error('Worker global error:', error)
}

self.onunhandledrejection = (event) => {
  console.error('Worker unhandled rejection:', event.reason)
}
