{"modelNote": "Automatic Tool Detection Demo\n\nThis model demonstrates how layer names automatically determine CNC tools:\n\nK = Kanal (Groove from center)\n- K_Freze10mm → 10mm cylindrical end mill\n- K_Ballnose6mm → 6mm ball nose end mill\n- K_AciliV90 → 90° V-bit\n\nH = Contour operations\n- H_Freze20mm_DIS → 20mm end mill, outer contour\n- H_Ballnose6mm_IC → 6mm ball nose, inner contour\n- H_Freze10mm_IC_SF → 10mm end mill, inner contour, bottom surface\n\nV = V-tool operations\n- V_Oyuk45 → 45° V-bit\n\nSuffixes:\n- _SF = Bottom surface\n- _DIS = Outer contour\n- _IC = Inner contour\n\nThe system automatically:\n1. Parses layer names\n2. Identifies operation types\n3. Extracts tool parameters\n4. Selects appropriate tools\n5. Recommends cutting parameters", "modelParameters": [{"defaultValue": 600, "description": "Door width (mm)", "parameterName": "doorWidth"}, {"defaultValue": 800, "description": "Door height (mm)", "parameterName": "doorHeight"}, {"defaultValue": 18, "description": "Door thickness (mm)", "parameterName": "doorThickness"}], "tags": ["automatic", "tool-detection", "layer-naming", "demo", "tutorial", "kanal", "contour", "v-tool", "cnc", "machining"], "version": 1, "category": "Examples", "subcategory": "Tool Detection", "difficulty": "beginner", "layerConventions": {"K": "<PERSON><PERSON> (Groove from center)", "H": "Contour operations", "V": "V-tool operations", "suffixes": {"_SF": "Bottom surface", "_DIS": "Outer contour", "_IC": "Inner contour"}}, "demonstratedTools": ["10mm Cylindrical End Mill", "6mm Ball Nose End Mill", "90° V-Bit", "20mm Cylindrical End Mill", "45° V-Bit"]}