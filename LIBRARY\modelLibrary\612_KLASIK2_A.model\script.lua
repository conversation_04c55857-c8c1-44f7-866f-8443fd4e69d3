-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  xMin = 250
  yMin = 250
  
  a = 55
  ad = 8
  aV = 90
  h = 25
  g = 145
  cW = 10
  cT = 5
  
  extEdgeVtoolExist       	= 5   -- <PERSON>s kenar <PERSON> islemi var mi? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak kose Radusu var mi? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
  local vMidDiameter = 40
  local sunkenWidth = ad * math.tan((math.pi*aV/180)/2.0)
  
  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  if h<cW or h<cT then
    print("Tool too large")
    return true
  end
  
  G.setLayer("K_AciliV"..aV)		--Angled surface
  G.setThickness(0)
  local corner1,corner2 = G.sunkenFrame({a, a}, {X-a, Y-a}, ad, aV, vMidDiameter)
  corner1[3] = 0
  corner2[3] = 0
    
  G.setLayer("K_Freze".. cW.."mm")		--Middle shape
  G.setThickness(-ad)
  
  distance = a+sunkenWidth+h
  point1 = {distance-cW/2, distance-cW/2}
  point2 = {distance+g+cW/2, Y-distance+cW/2}
  point3 = {X-distance-g-cW/2, distance-cW/2}
  point4 = {X-distance+cW/2, Y-distance+cW/2}
  G.rectangle(point1, point2)
  G.rectangle(point3, point4)
  
  l = X-2*(a+sunkenWidth+h+g)
  k = math.floor((l-cW)/(cW/2))
  point5 = {distance+g+cW/2,distance-cW/2}
  point6 = {distance+g+cW/2,Y-distance+cW/2}
  
  if l>2*cW then
    for i = 1, k ,1 do
      point5 = G.ptAdd(point5, {cW/2,0})
      point6 = G.ptAdd(point6, {cW/2,0})
      if point5[1]>=point3[1]-cW/4 then
        break
      end
      G.line(point5,point6,0)
    end
  end
  
  G.setLayer("K_Freze".. cW.."mm")		--Channel cleaning
  G.setThickness(-ad)
  
  distance = a+sunkenWidth+cW/2
  point1 = {distance,distance}
  point2 = {X-distance, Y-distance}
  G.rectangle(point1,point2)
  
  k = math.floor((h-cW)/(cW/2))
  
  if h>cW then
    for i = 1, k ,1 do
      point1 = G.ptAdd(point1, {cW/2,cW/2})
      point2 = G.ptSubtract(point2, {cW/2,cW/2})
      if point2[1]<=point4[1] then
        break
      end
      G.rectangle(point1,point2)
    end
  end
  
  G.setLayer("K_TarKoseTemizl".. cT.."mm")		--Corner cleaning
  G.setThickness(0)
  G.cleanCorners(corner1,corner2,ad,cT)
  return true
end


require "ADekoDebugMode"
