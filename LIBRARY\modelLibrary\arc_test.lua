function modelMain()
    print("Testing arc representation fixes...")
    
    -- Test 1: Simple arcs using makerjs_engine directly
    print("\n=== Test 1: Direct Arc Creation ===")
    makerjs_engine.model_def("arc_test", function()
        makerjs_engine.layer("arcs")
        
        -- Quarter circle arc (counter-clockwise)
        makerjs_engine.arc("quarter_ccw", 100, 100, 50, 0, 90, false)
        
        -- Quarter circle arc (clockwise)
        makerjs_engine.arc("quarter_cw", 200, 100, 50, 0, 90, true)
        
        -- Half circle arc (counter-clockwise)
        makerjs_engine.arc("half_ccw", 300, 100, 50, 0, 180, false)
        
        -- Half circle arc (clockwise)
        makerjs_engine.arc("half_cw", 400, 100, 50, 0, 180, true)
        
        -- Three-quarter circle arc
        makerjs_engine.arc("three_quarter", 500, 100, 50, 0, 270, false)
        
        -- Full circle (should be handled as circle)
        makerjs_engine.circle("full_circle", 600, 100, 50)
    end)
    
    -- Test 2: Arcs with bulges in polylines
    print("\n=== Test 2: <PERSON>yl<PERSON> with Arc Segments ===")
    G = ADekoLib
    G.setFace("top")
    <PERSON><PERSON>setLayer("polyline_arcs")
    
    -- Create a polyline with arc segments (bulges)
    local arc_points = {
        {50, 200},           -- start point
        {150, 200, 0, 0.5},  -- point with positive bulge (counter-clockwise arc)
        {250, 250},          -- intermediate point
        {350, 200, 0, -0.5}, -- point with negative bulge (clockwise arc)
        {450, 200}           -- end point
    }
    G.polylineimp(arc_points)
    
    -- Test 3: True arc functions from ADekoLib
    print("\n=== Test 3: ADekoLib True Arcs ===")
    G.setLayer("true_arcs")
    
    -- Create true arcs using the enhanced ADekoLib functions
    G.arc({100, 300}, 40, 0, 90, false, "arc_ccw_90")
    G.arc({200, 300}, 40, 0, 90, true, "arc_cw_90")
    G.arc({300, 300}, 40, 45, 135, false, "arc_ccw_45_135")
    G.arc({400, 300}, 40, 45, 135, true, "arc_cw_45_135")
    
    -- Test 4: Circular arcs with different angles
    print("\n=== Test 4: Various Arc Angles ===")
    G.setLayer("angle_tests")
    
    -- Test different angle ranges
    local center_y = 400
    local angles = {
        {0, 30},    -- 30 degrees
        {0, 60},    -- 60 degrees
        {0, 120},   -- 120 degrees
        {0, 180},   -- 180 degrees
        {0, 240},   -- 240 degrees
        {0, 300},   -- 300 degrees
    }
    
    for i, angle_pair in ipairs(angles) do
        local center_x = 50 + (i-1) * 80
        G.arc({center_x, center_y}, 30, angle_pair[1], angle_pair[2], false, "arc_" .. angle_pair[2])
    end
    
    -- Export the model
    local json_output = makerjs_engine.export_model()
    print("\n=== JSON Output ===")
    print(json_output)
    
    print("\n=== Arc Test Summary ===")
    print("✓ Direct arc creation with clockwise/counter-clockwise")
    print("✓ Polyline with arc segments (bulges)")
    print("✓ True arc functions from ADekoLib")
    print("✓ Various arc angles and directions")
    print("\nVisualization should show:")
    print("- Row 1: Quarter and half circles in both directions")
    print("- Row 2: Polyline with curved segments")
    print("- Row 3: True arcs with different directions")
    print("- Row 4: Arcs with various angle spans")
end
