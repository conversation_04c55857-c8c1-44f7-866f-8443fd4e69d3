import { computed, reactive } from 'vue'

export interface LoadingState {
  isLoading: boolean
  progress: number
  message: string
  hasError: boolean
  errorMessage?: string
}

export interface LoadingOptions {
  message?: string
  showProgress?: boolean
  duration?: number
}

// Global loading state for app-wide operations
const globalLoadingState = reactive<LoadingState>({
  isLoading: false,
  progress: 0,
  message: '',
  hasError: false,
  errorMessage: undefined
})

export function useLoading(key?: string) {
  // Local loading state for component-specific operations
  const localLoadingState = reactive<LoadingState>({
    isLoading: false,
    progress: 0,
    message: '',
    hasError: false,
    errorMessage: undefined
  })

  // Choose between global and local state
  const state = key === 'global' ? globalLoadingState : localLoadingState

  // Computed properties
  const isLoading = computed(() => state.isLoading)
  const progress = computed(() => state.progress)
  const message = computed(() => state.message)
  const hasError = computed(() => state.hasError)
  const errorMessage = computed(() => state.errorMessage)

  // Start loading
  const startLoading = (options: LoadingOptions = {}) => {
    state.isLoading = true
    state.progress = 0
    state.message = options.message || 'Loading...'
    state.hasError = false
    state.errorMessage = undefined
  }

  // Update progress
  const updateProgress = (progress: number, message?: string) => {
    state.progress = Math.min(100, Math.max(0, progress))
    if (message) {
      state.message = message
    }
  }

  // Update message
  const updateMessage = (message: string) => {
    state.message = message
  }

  // Set error state
  const setError = (errorMessage: string) => {
    state.hasError = true
    state.errorMessage = errorMessage
    state.message = `Error: ${errorMessage}`
  }

  // Stop loading
  const stopLoading = () => {
    state.isLoading = false
    state.progress = 100
    state.hasError = false
    state.errorMessage = undefined
  }

  // Reset state
  const reset = () => {
    state.isLoading = false
    state.progress = 0
    state.message = ''
    state.hasError = false
    state.errorMessage = undefined
  }

  // Async operation wrapper with automatic loading management
  const withLoading = async <T>(
    operation: () => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T> => {
    startLoading(options)
    
    try {
      // If duration is specified, simulate progress
      if (options.duration && options.showProgress !== false) {
        const startTime = Date.now()
        const progressInterval = setInterval(() => {
          const elapsed = Date.now() - startTime
          const progress = Math.min(95, (elapsed / options.duration!) * 100)
          updateProgress(progress)
        }, 50)

        const result = await operation()
        clearInterval(progressInterval)
        updateProgress(100)
        
        // Small delay to show completion
        await new Promise(resolve => setTimeout(resolve, 200))
        stopLoading()
        
        return result
      } else {
        const result = await operation()
        stopLoading()
        return result
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'An error occurred'
      setError(errorMsg)
      throw error
    }
  }

  // Simulate loading with progress (useful for testing or fake operations)
  const simulateLoading = async (
    duration: number = 2000,
    message: string = 'Loading...'
  ): Promise<void> => {
    return withLoading(
      () => new Promise(resolve => setTimeout(resolve, duration)),
      { message, duration, showProgress: true }
    )
  }

  // Multi-step loading operation
  const withSteps = async (
    steps: Array<{
      name: string
      operation: () => Promise<void>
      duration?: number
    }>
  ): Promise<void> => {
    startLoading({ message: 'Initializing...' })
    
    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i]
        const stepProgress = (i / steps.length) * 100
        
        updateProgress(stepProgress, step.name)
        
        if (step.duration) {
          await withLoading(step.operation, {
            message: step.name,
            duration: step.duration,
            showProgress: false // We handle progress manually
          })
        } else {
          await step.operation()
        }
        
        // Update progress after step completion
        updateProgress(((i + 1) / steps.length) * 100)
      }
      
      stopLoading()
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'An error occurred'
      setError(errorMsg)
      throw error
    }
  }

  return {
    // State
    isLoading,
    progress,
    message,
    hasError,
    errorMessage,
    
    // Actions
    startLoading,
    updateProgress,
    updateMessage,
    setError,
    stopLoading,
    reset,
    
    // Utilities
    withLoading,
    simulateLoading,
    withSteps
  }
}

// Export global loading state for app-wide access
export const globalLoading = useLoading('global')
