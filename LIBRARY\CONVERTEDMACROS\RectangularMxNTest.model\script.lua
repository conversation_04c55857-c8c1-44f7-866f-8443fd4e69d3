-- ADekoCAM, Model Script - Rectangular MxN Grid Test
-- Converted from C# azCAM macro: Rectangular MxN.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Rectangular MxN Grid parameters (from original C# macro)
  A = 5     -- Outer margin
  B = 5     -- Gap between rectangles  
  M = 3     -- Number of columns (fixed)
  N = 2     -- Number of rows (fixed)
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * A + M * 10 + (M-1) * B  -- minimum required width
  local minHeight = 2 * A + N * 10 + (N-1) * B  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Rectangular MxN Grid function (converted from C# macro)
  local function rectangular_mxn_grid(A, B, M, N, Z)
    -- Use default values if parameters not provided
    A = A or 5
    B = B or 5
    M = M or 3
    N = N or 2
    Z = Z or -5
    
    -- Calculate rectangle dimensions (fixed grid, not dynamic)
    local x = (width - 2*A - (M-1)*B) / M   -- Width of each rectangle
    local y = (height - 2*A - (N-1)*B) / N  -- Height of each rectangle
    
    print(string.format("Creating %dx%d grid of simple rectangles", M, N))
    print(string.format("Each rectangle: %.1f x %.1f mm", x, y))
    
    -- Set layer and thickness for rectangles
    G.setLayer("K_Freze10mm")
    G.setThickness(Z)
    
    local rectangleCount = 0
    
    -- Create simple rectangle template function
    local function create_simple_rectangle(centerX, centerY, rectWidth, rectHeight)
      -- Calculate corner positions
      local halfW = rectWidth / 2
      local halfH = rectHeight / 2
      local x1 = centerX - halfW
      local y1 = centerY - halfH
      local x2 = centerX + halfW
      local y2 = centerY + halfH
      
      -- Create simple rectangle (no fillets)
      G.polyline(
        {x1, y1},  -- bottom-left
        {x2, y1},  -- bottom-right
        {x2, y2},  -- top-right
        {x1, y2},  -- top-left
        {x1, y1}   -- close the path
      )
    end
    
    -- Create grid using nested loops (rows and columns)
    for i = 0, N-1 do  -- rows (vertical position)
      for j = 0, M-1 do  -- columns (horizontal position)
        rectangleCount = rectangleCount + 1
        
        -- Calculate center position for this rectangle
        -- Translation formula from C#: A + i*(x+B) + x/2
        local rectCenterX = A + (j * x) + (j * B) + x/2
        local rectCenterY = A + (i * y) + (i * B) + y/2
        
        -- Create simple rectangle at this position
        create_simple_rectangle(rectCenterX, rectCenterY, x, y)
        
        -- Start new shape for next rectangle (except for the last one)
        if rectangleCount < M * N then
          G.nextShape()
        end
        
        print(string.format("Rectangle [%d,%d]: center at (%.1f, %.1f)", 
              i+1, j+1, rectCenterX, rectCenterY))
      end
    end
    
    return true
  end
  
  -- Call the rectangular MxN grid function
  local success = rectangular_mxn_grid(A, B, M, N, -Z)
  
  if success then
    print(string.format("Rectangular MxN grid created with parameters:"))
    print(string.format("  A (outer margin): %d mm", A))
    print(string.format("  B (gap between rectangles): %d mm", B))
    print(string.format("  Grid size: %dx%d", M, N))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Door size: %dx%d mm", X, Y))
  end
  
  return true
end

require "ADekoDebugMode"
