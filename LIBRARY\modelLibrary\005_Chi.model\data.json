{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nH_Freze*cT*mm_Ic\t*cT*mm Freze Bicagi \nH_Freze*cW*mm_Ic_SF\t*cW*mm Freze Bicagi \nK_Freze*cW*mm_SF\t*cW*mm Freze Bicagi \nPANEL\t\tEbatlama Bicagi", "modelParameters": [{"defaultValue": 50, "description": "offset from the edges", "parameterName": "a"}, {"defaultValue": 10, "description": "width of the block laths", "parameterName": "c"}, {"defaultValue": 2, "description": "width of every square block (*0* for using the limits in the script)", "parameterName": "blockNoX"}, {"defaultValue": 3, "description": "length of every square block (*0* for using the limits in the script)", "parameterName": "blockNoY"}, {"defaultValue": 12, "description": "mm, window depth from top", "parameterName": "windowDepth"}, {"defaultValue": 10, "description": "Cam yeri kesim bicak capi", "parameterName": "cT"}, {"defaultValue": 20, "description": "Cam yeri tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extEdgeVtoolExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}