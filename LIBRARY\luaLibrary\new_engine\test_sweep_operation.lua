-- Test Sweep Operation
-- This script tests the 3D sweep functionality in the CAD/CAM system

-- Load the engine
local engine = require("makerjs_engine")

print("=== Testing Sweep Operation ===")

-- Test 1: Basic Profile and Path Definition
print("\n1. Testing basic profile and path definition...")

engine.model_def("sweep_test_basic", function()
    -- Create a simple rectangular profile
    engine.layer("profile_layer")
    engine.rect("profile_rect", 0, 0, 10, 5)
    
    -- Define the profile for sweeping
    engine.define_profile("basic_profile", "sweep_test_basic")
    
    -- Define a simple linear 3D path
    engine.define_3d_path("linear_path", "line", {
        {0, 0, 0},    -- Start point
        {50, 0, 0},   -- End point (50mm along X-axis)
    })
    
    -- Create the sweep operation
    engine.define_sweep("basic_sweep", "basic_profile", "linear_path")
    
    print("✓ Basic profile and linear path defined successfully")
end)

-- Test 2: Complex Profile with Curved Path
print("\n2. Testing complex profile with curved path...")

engine.model_def("sweep_test_complex", function()
    -- Create a more complex profile with circle and rectangle
    engine.layer("complex_profile")
    engine.rect("outer_rect", 0, 0, 20, 15)
    engine.circle("inner_circle", 10, 7.5, 3)
    
    -- Define the complex profile
    engine.define_profile("complex_profile", "sweep_test_complex")
    
    -- Define a curved 3D path
    engine.define_3d_path("curved_path", "spline", {
        {0, 0, 0},      -- Start
        {25, 0, 10},    -- Rise
        {50, 0, 5},     -- Dip
        {75, 0, 15},    -- Peak
        {100, 0, 0}     -- End
    })
    
    -- Create the sweep operation with options
    engine.define_sweep("complex_sweep", "complex_profile", "curved_path", {
        twist = 0,      -- No twist
        scale = 1.0,    -- No scaling
        steps = 20      -- Number of interpolation steps
    })
    
    print("✓ Complex profile and curved path defined successfully")
end)

-- Test 3: Tool Profile Sweep (Simulating CNC Tool Path)
print("\n3. Testing tool profile sweep...")

engine.model_def("tool_sweep_test", function()
    -- Create a tool profile (endmill cross-section)
    engine.layer("tool_profile")
    engine.circle("tool_shape", 0, 0, 4)  -- 8mm diameter endmill
    
    -- Define the tool profile
    engine.define_profile("endmill_profile", "tool_sweep_test")
    
    -- Define a toolpath (rectangular pocket)
    engine.define_3d_path("pocket_path", "polyline", {
        {10, 10, -5},   -- Start at depth -5mm
        {90, 10, -5},   -- Move right
        {90, 90, -5},   -- Move up
        {10, 90, -5},   -- Move left
        {10, 10, -5}    -- Return to start
    })
    
    -- Create the tool sweep
    engine.define_sweep("pocket_sweep", "endmill_profile", "pocket_path", {
        operation = "subtract",  -- This will subtract material
        depth = 5               -- 5mm deep cut
    })
    
    print("✓ Tool profile sweep defined successfully")
end)

-- Test 4: Multiple Sweep Operations
print("\n4. Testing multiple sweep operations...")

engine.model_def("multi_sweep_test", function()
    -- Create different profiles
    engine.layer("profiles")
    
    -- Small circular profile
    engine.circle("small_circle", 0, 0, 2)
    engine.define_profile("small_profile", "multi_sweep_test")
    
    -- Medium square profile  
    engine.rect("medium_square", 0, 0, 6, 6)
    engine.define_profile("medium_profile", "multi_sweep_test")
    
    -- Define multiple paths
    engine.define_3d_path("path1", "line", {{0, 0, 0}, {30, 0, 0}})
    engine.define_3d_path("path2", "line", {{0, 20, 0}, {30, 20, 0}})
    
    -- Create multiple sweeps
    engine.define_sweep("sweep1", "small_profile", "path1")
    engine.define_sweep("sweep2", "medium_profile", "path2")
    
    print("✓ Multiple sweep operations defined successfully")
end)

-- Test 5: Export and Validation
print("\n5. Testing export and validation...")

-- Export all models to JSON
local json_output = engine.export_json()

-- Validate the JSON contains sweep data
if json_output and json_output:find('"sweeps"') then
    print("✓ JSON export contains sweep data")
else
    print("⚠ JSON export may not contain sweep data")
end

-- Print a sample of the JSON (first 200 characters)
if json_output then
    local sample = json_output:sub(1, 200)
    print("JSON sample: " .. sample .. "...")
end

print("\n=== Sweep Operation Test Complete ===")
print("All sweep operations have been defined and are ready for 3D processing.")
print("Note: Actual 3D geometry generation requires OpenCascade.js integration.")

-- Return test results
return {
    basic_sweep = "defined",
    complex_sweep = "defined", 
    tool_sweep = "defined",
    multi_sweep = "defined",
    json_export = json_output and "success" or "failed"
}
