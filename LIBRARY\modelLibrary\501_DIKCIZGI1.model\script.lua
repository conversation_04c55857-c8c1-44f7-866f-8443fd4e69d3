-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
   
  xMin = 140
  yMin = 140 
  a 	= -5
  xMin = 140
  yMin = 140
  toolDiameter = 15
  gaps = 20
  ad = 3		--toolDepth--kanal derinliği
  
  edgeCornerRExist        = 0   -- Kapak köşe Radüsü var mı?
  edgeChmfExist        = 0   --Kenarlarda aynı takımla işleme var mı "4" 4 kenarda var, "2" 2 kenarda var/ "0" yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  bulge = math.tan(math.pi/8)
  
  local D = edgeCornerRExist		-- köşe radüsü-yar<PERSON><PERSON>pı
  local B = math.tan(math.pi/8)
  if edgeCornerRExist > 0 then
    G<PERSON>makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
   
  local toolType = "Form" -- "Freze" , "Ballnose" or "Acili_V"
  --local toolDepth = 8
  --local toolDiameter = 16       --Ballnose or conical
  local toolAngle = 170
  local grooveThickness =0
  local toolRadius = toolDiameter/2
  --local gaps = 12.5
  local ara = 0
  local layerNameEd ="K_" .. toolType .. tostring(toolDiameter) .. "mm"
  local gorunenHal = true
  grooveThickness = toolDiameter
  
  --Aralık hesaplama
  
    local aprxGaps = gaps
    local e = gaps
    G.setLayer(layerNameEd)
    G.setThickness(-ad)
    
    local stepX = 0

    local fPcX = math.floor(X/gaps)

    if ((X/gaps)-fPcX)  >= 0.5 then
      stepX = math.ceil((X/gaps)-1)
    else
      stepX = math.floor((X/gaps)-1)
    end
      
    if edgeChmfExist == 2 then     --kenardaki işlem varsa
      point1 = {0, 0+a}
      point2 = {0, Y-a}
      point5 = {X, 0+a}
      point6 = {X, Y-a}
      G.line(point1,point2,0)
      G.line(point5,point6,0)
    elseif edgeChmfExist == 4 then
      G.rectangle({0, 0},{X, Y})
    elseif  edgeChmfExist == 0 then
      gorunenHal = false
    end

    if gorunenHal == false then
        e = ((X-(stepX)*grooveThickness)/(stepX+1))+grooveThickness/2
        aprxGaps = e+grooveThickness/2
    
      for i=0,stepX-1 do
        local x = e+i*aprxGaps
        point3 = {x,a}
        --point3 = {x,0}
        point4 = {x,Y-a}  
        --point4 = {x,Y}  
        G.line(point3,point4,0)
        i = i+1
      end
      --print("bu kenarlar farklı ortalar eşit aralık 'FALSE_'"..aprxGaps .. "_mm")
    else
      aprxGaps = (X/(stepX+1))
      for i=0,stepX-1 do
        local x = aprxGaps+i*aprxGaps
        point3 = {x,a}
        --point3 = {x,0}
        point4 = {x,Y-a}  
        --point4 = {x,Y}  
        G.line(point3,point4,0)
        i = i+1
      end
      --print("bu esit aralık TRUE_"..aprxGaps .. "_mm")
    end  
  
  
  return true
end

require "ADekoDebugMode"