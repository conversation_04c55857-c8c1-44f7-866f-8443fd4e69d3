-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
   
  G = ADekoLib
	minimum = 150
	limit = 350
	
  a = 60  
  aa = 30
  ad = 6                -- kanal derinliği (sunkenDepth)
  b = 20				-- köşedeki yay noktası ile kapak köşesi arası mesafe
  yy = 20				-- yay kısmının yüksekliği
  h = 50				-- Vbit göbek arası düz kısım
  cW = 20               -- Tarama Bıçak Çapı
  cT = 6                -- <PERSON>nce bıçak çapı (Köşe Temizleme)
 
  extEdgeVtoolExist      			= 0   -- D<PERSON><PERSON> kenar <PERSON>h işlemi var mı? derinlik/0:yok
  edgeCornerRExist          	  	= 0   -- Kapak köşe Radüsü var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  local aV = 90              -- Kenar Pah Açısı
  local extGrooveExist   		    = ad   -- Dış kanal var mı? derinlik/0:yok
  local extGrooveOffset			    = 0 --En dıştaki kanalın v bitden nekadar içerde mi "-" dışarda mı "+" tam çizgiden mi "0"
  
  local notchWidth = 0
  local sunkenWidth = 0

  local Dd = edgeCornerRExist
  local Bb = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  
  if edgeCornerRExist >0 then
    G.makePartShape(        ----chamfered part shape
      {Dd,0},		
      {X-Dd,0,0,Bb},
      {X,Dd},
      {X,Y-Dd,0,Bb},
      {X-Dd,Y},
      {Dd,Y,0,Bb},
      {0,Y-Dd},
      {0,Dd,0,Bb},
      {Dd,0}
      )
  else
    G.makePartShape()
  end

	if X < xMin or Y < yMin then
	print("Part dimension too small")
	return true
	end
  
	if h<cW then
		print("Tool too large")
		return true
	end
  
  --G.setLayer("H_Freze10mm_Dis")
  --G.setThickness(-notchDepth)
  --G.rectangle ({notchWidth,notchWidth},{X-notchWidth,Y-notchWidth})
	
  ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
    
	yh = ust+yy
	
	
	local bulge1 = G.bulge(
		{X-notchWidth-a, Y-notchWidth-yh, 0, 0},
		{X/2, Y-notchWidth-ust}, {a, Y-notchWidth-yh}
		)
	local points = {
    	{X-notchWidth-a, Y-notchWidth-yh, 0, bulge1},  --1
    	{notchWidth+a, Y-notchWidth-yh, 0, 0},       --2
    	{notchWidth+a, notchWidth+ust, 0, 0},         --3
    	{X-notchWidth-a, notchWidth+ust},             --4
    	{X-notchWidth-a, Y-notchWidth-yh, 0, bulge1}   --5
    	}
	
	local bulge2 = G.bulge(
		{a+cT/2, Y-yh, 0, 0},
		{a+cT/2-0.5, Y-yh+5}, 
		{b+cT/2, Y}
		)
		
	local points2 = {
		{a+cT/2,0,0,0},
		{a+cT/2,Y-yh,0,bulge2},
		{b+cT/2,Y,0,0},
		}
	
	local bulge3 = G.bulge(
		{X-cT/2-b, Y},
		{X-cT/2-(a-0.5), Y-yh+5}, 
		{X-cT/2-a, Y-yh}
		)
		
	local points3 = {
		{X-cT/2-b,Y,0,bulge3},
		{X-cT/2-a,Y-yh,0,0},
		{X-cT/2-a,0,0,0},
		}

	if extEdgeVtoolExist > 0 then
		-- G.setLayer("K_AciliV" .. aV)  -- 
		G.setLayer("K_AciliV_Pah")		
		G.setThickness(-extEdgeVtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
	
	
	G.setLayer("K_Freze"..cW.."mm")             --göbekteki tarama işlemi
	G.setThickness(-ad)
	point1 = G.offSet(points, -(sunkenWidth+cW/2))
	G.polylineimp(point1)
		
	if h > cW then
		point2 = G.offSet(points, -(sunkenWidth+h-cW/2))
		G.polylineimp(point2)
		
		k = (h-cW)/(cW/2)
		
		for i=1, k, 1 do
			point1 = G.offSet(point1, -cW/2)
			if point1[2][1]>point2[2][1]-cW/2 then
			break
			end
			G.polylineimp(point1)
		end
	end
  
  if extGrooveExist > 0 then              --dışarda kanal işlemi var mı?
    
    extGrooveOffset = extGrooveOffset + cT/2
    G.setLayer("K_Freze"..cT.."mm")
    G.setThickness(-extGrooveExist)
    
    --if extGrooveOffset ~= 0 then
    --  G.polylineimp (G.offSet(points, extGrooveOffset))
    --else
      G.polylineimp(points2)
      G.polylineimp(points3)
    --end
    
  end
  
  
  -- local distance1 = a + sunkenWidth
  -- local distance2 = yh + sunkenWidth - 1
  -- local distance3 = ust + sunkenWidth
  -- local cc1={distance1,distance3}
  -- local cc2={X-distance1,Y-distance2}
  -- cc1[3] = 0
  -- cc2[3] = 0
  -- G.setLayer("K_Freze"..cT.."mm")
  -- G.setThickness(0)
  -- G.cleanCorners(cc1,cc2,ad,cT)
  
  return true
end


------------------------------------------------
require "ADekoDebugMode"