-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 50                    -- Kenardan Vbite
  aa = 20					-- Dar kapak için Kenardan mesafe
  b = 15                    -- Vbitten Dıs kanala
  c = 2						-- Dıştaki Vbit üstü kanalın vbite mesafesi
  h = 25                    -- Vbitten göbeğe
  ad = 6                    -- Vbit derinliği (sunkenDepth)
  aV = 90                  -- Vbit Açısı
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 6                   -- İnce bıçak çapı (Köşe Temizleme)
        
  extGrooveExist          		= 2   -- Dış kanal ve-veya V bit üstü var mı? derinlik/0:yok
  extEdgeRtoolExist     		= 6   -- Dı<PERSON> kenar <PERSON> işlemi var mı? derinlik/0:yok
  edgeCornerRExist          	= 0   -- <PERSON>pak köşe Radüsü var mı? derinlik/0:yok
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local d = 2              		-- Göbekten iç kanala veya göbek vbit üstü kanal
  --local cR = 6		        -- Radus Veren form bıcak düz uç dar çapı veya parça dışına kaydırma mesafesi
  --bd = 3                    -- dış kanal derinliği
  --local hd = 5              -- Göbek Desen Bıcak derinliği
  --local dd = 2              -- iç kanal derinliği
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  
  local intGrooveExist        		= 0   -- Göbekte vbit üstü veya içeride kanal var mı? derinlik/ 0:yok
  local cabCoreExist            = true   -- Göbek var mı? Cam varsa buna göre bişeyler yapılabilir
  local D = edgeCornerRExist
  
  
  local vWideDiameter = 60
  local sunkenWidth = ad * math.tan((math.pi * aV/180)/2)       --indiği derinlikte yarısı oluyor
    
  local B = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end

  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  minXCalculated = 2*a + 2*sunkenWidth + 2*h
  minYCalculated = 2*ust + 2*sunkenWidth + 2*h

  if cW <= 0 then
    cW = cT
  end
  
  if X<minXCalculated or Y<minYCalculated then
    print("Part dimension too small")
    return true
  end
  
  if extGrooveExist > 0 then
    G.setLayer("K_Freze"..cT.."mm")  -- DEEP cleanup
    G.setThickness(-extGrooveExist)
    distance = a-b + cT/2
    distance2 = ust-b + cT/2
    point1 = {distance, distance2}
    point2 = {X-distance, Y-distance2}
    G.rectangle(point1,point2)
	
	distance = a + c
	distance2 = ust + c
	point3 = {distance, distance2}
	point4 = {X-distance, Y-distance2}
	G.rectangle(point3,point4)
	
  end
  
    
	G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	G.setThickness(0)
	local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	corner1[3] = 0
	corner2[3] = 0
	
	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point1 = {0-distance, 0-distance}
		point2 = {X+distance, Y+distance}
		G.rectangle(point1,point2)
	end
  
	if cabCoreExist then
	
		G.setLayer("K_AciliV" .. aV)  --- Gobek
		G.setThickness(-ad)
		distance = a + sunkenWidth + h
		distance2 = ust + sunkenWidth + h
		point1 = {distance, distance2}
		point2 = {X-distance, Y-distance2}
		G.rectangle(point1,point2)
	
		G.setLayer("K_Freze"..cW.."mm")  -- DEEP cleanup
		G.setThickness(-ad)
		distance1 = a + sunkenWidth+cW/2
		distance2 = ust + sunkenWidth+cW/2
		point1 = {distance1, distance2}
		point2 = {X-distance1, Y-distance2}
		G.rectangle(point1,point2)
	
	
		if h<cT or h<cW then
			print("Tool too large")
			return true
		end
	
		if h>cW then
			distance = a+sunkenWidth+h-cW/2
			distance2 = ust+sunkenWidth+h-cW/2
			point3 = {distance, distance2}
			point4 = {X-distance, Y-distance2}
			--G.rectangle(point3,point4)
			k = (h-cW)/(cW/2)
			for i=1, k, 1 do
				point1 = G.ptAdd(point1,{cW/2,cW/2})
				point2 = G.ptSubtract(point2, {cW/2,cW/2})
				if point1[1]>point3[1]-cW/2 then
					break
				end
			G.rectangle(point1,point2)
			end
		end
	
	
		if h ~= cW then
			distance = a+sunkenWidth+h-cW/2
			distance2 = ust+sunkenWidth+h-cW/2
			point10 = {distance, distance2}
			point11 = {X-distance, Y-distance2}
			G.rectangle(point10,point11)
		end
	
		if intGrooveExist > 0 then      ------İç kanal varsa
			local checkX = 2*a + 2*h + 2*d
			local checkY = 2*ust + 2*h + 2*d
			if X < checkX or Y < checkY then
				print("Part dimension too small, check a + h + d value")
				return true
			end
			G.setLayer("K_Freze"..cT.."mm") --içteki göbek vbit üstü kanal
			G.setThickness(-intGrooveExist)
			distance = a + 2*sunkenWidth + h + d
			distance2 = ust + 2*sunkenWidth + h + d
			point1 = {distance, distance2}
			point2 = {X-distance, Y-distance2}
			G.rectangle(point1,point2)

		end
	
		-- else ------------- bu araya 250 den küçükler için tarama gelebilir
		-- G.setLayer("Cep_Acma".. cW .. "mm")  -- DEEP cleanup
		-- G.setThickness(-ad)
		-- distance = a
		-- distance2 = ust
		-- point6 = {distance, distance2}
		-- point7 = {X-distance, Y-distance2}
		-- G.rectangle(point6,point7)
		-- end
		
		G.setLayer("K_TarKoseTemizl"..cT.."mm")
	
		if cW ~= cT then
			if h >= 3*cT then
				G.setThickness(0)
				G.cleanCorners(corner1,corner2,ad,cT)
			else
				G.setThickness(-ad)
				distance = a+sunkenWidth + cT/2
				distance2 = ust+sunkenWidth + cT/2
				point1 = {distance, distance2}
				point2 = {X-distance, Y-distance2}
				G.rectangle(point1,point2)
			end
		end
	end
  
  return true
end

------------------------------------------------

require "ADekoDebugMode"
