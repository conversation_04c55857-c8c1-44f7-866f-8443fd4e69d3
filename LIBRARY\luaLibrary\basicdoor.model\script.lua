-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  minimum = 150
  limit = 350
  
  a = 50    -- kenardan marjin
  aa = 30		-- <PERSON> mesafe


  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end

  if X<minimum or Y<minimum then
    print("Part dimension too small")
    return true
  end

  if aa <= 0 then
    aa = a
  end

  if X < limit and Y < limit then 
    ust = aa
    a = aa
  elseif  Y < limit and X > limit then 
    ust = aa
  elseif X < limit and Y > limit  then
    a = aa
  end


  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()


  return true
end

require "ADekoDebugMode"