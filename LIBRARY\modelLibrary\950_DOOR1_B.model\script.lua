-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  
  ust = 75
  sol = 75
  sag = 75
  ara = 100
  a = 0 --<PERSON>ardan Vbite
  alt = 100
  KD = 400
  altH = 0 

  b = 10 --Vbitten Dıs kanala
  c = 25 --Vbitten Dıs kanal 2 ye
  d = 35 --Göbekten iç kanala
  aV = 120    --Vbit Açısı
  h = 30 -- Vbitten göbeğe
  cT = 8      -- İnce bıçak çapı (Köşe Temizleme)
  cW = 20      -- Cam Yeri arkadan temizleme
  gaps = 40
  sbt = 20	
  
  extGroove2Exist   = 0  --<PERSON><PERSON><PERSON> kanal var mı (Var: Derinlik / Yok: 0)
  extGrv2BorForV	= 1		--Dış kanal2 Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)
  extGrooveExist    = 1.5  --Dış kanal var mı (Var: Derinlik / Yok: 0)
  extGrvBorForV	= 1		--Dış kanal1 Ballnose mu? Duz Kanal mi? V kanal mi? (B:0/F:1/V:2)
  
  topGrooveExist    = 1.5  --Dış kanal var mı (Var: Derinlik / Yok: 0)
  dt				= 8 --kanal islemi bicak capi
  topGrvExtExist	  = 0 --Ust kanal uzatma var mı? (Var:1/Yok:0)
  topGrooveBorF    	= 1  --Ust kanal Ballnose mu? Duz Kanal mi? (B:0/F:1)
  
  intEdgeToolExist	= 5  
  intEdgeVorSVorR	  = 1  --Ic kenar Pah işlemi V mi? Sunken V mi? Raduslu mu?(V:0/SV:1/R:2)
  
  bottomWindowExist = 0  --alt bolumde cam yeri var mı? (Var:1/Yok:0)
  cabCoreExist     = 1   --Göbek var mı (Var:1/Yok:0)
  intRectGrvExist = 2  --iç dikdörtgen kanal var mı (Var: Derinlik / Yok: 0)
  intGrvExist  = 2	 --iç kanal var mı (Var: Derinlik / Yok: 0)
  grooveVert_Hor = 1 	--intGrvExist varsa çalışır--1 vertical 0 horizontal
  shapeToolExist = 3.5   -- Göbek Desen bıçağı var mı?(Var: Derinlik / Yok: 0)
  
  extEdgeToolExist 	= 0  --Dış kenar Pah işlemi var mı? (Var: Derinlik / Yok: 0)
  extEdgeVorR 		  = 0  --Dış kenar Pah işlemi V mi? Raduslu mu? (V:0/R:1)
  edgeCornerRExist 	= 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
    
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end

  local minimum = 200
  local ad = intEdgeToolExist-- Vbit derinliği (sunkenDepth)
  local xMin = minimum
  local yMin = minimum

  local vWideDiameter = 60
  local R = 5
  local sunkenWidth = 0
  local ustKanal = 0
  
  local D = edgeCornerRExist
  local B = math.tan(math.pi/8)
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist > 0 then
    G.makePartShape({D,0},		--chamfered part shape
      {X-D,0,0,B},
      {X,D},
      {X,Y-D,0,B},
      {X-D,Y},
      {D,Y,0,B},
      {0,Y-D},
      {0,D,0,B},
      {D,0})
  else
    G.makePartShape()
  end
    
	if a>0 then
		ust = a
		sol = a
		sag = a
		ara = a
	end
	  
	if KD >0 then
		altH = KD-ara/2-alt
	end
  
	local limitX = sol+sag
	local limitY = alt+altH+ara+ust
	local ustH = Y-(alt+altH+ara+ust)
	local sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
   
	if X<limitX or Y<limitY then
		print("Part dimension too small")
		return true
	end
 
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	if altH < (2*sunkenWidth+2*h) or ustH < (2*sunkenWidth+2*h) then
		print("Part dimension too small")
		return true
	end
	
	if intRectGrvExist > 0 and intGrvExist > 0 then
		gKanalMesafesi = d
	end
	
	local gobekGenislik = X-sol-2*sunkenWidth-2*h-2*sbt-2*gKanalMesafesi-sag		--ustX
	local gobekGenislik3 = altH-2*sunkenWidth-2*h-2*sbt-2*gKanalMesafesi			--altY 
	
	--dikey çizgiler UST
	local stepX = math.floor(gobekGenislik/gaps)		
	
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local aprxGaps = gobekGenislik/(stepX+1)
	local e = sol+sunkenWidth+h+sbt+aprxGaps/2+gKanalMesafesi
	
	--dikey çizgiler ALT
	
	if ((gobekGenislik/gaps)-stepX)  >= 0.5 then
		stepX = stepX + 1
	end
	
	local e1 = sol+sunkenWidth+h+sbt+aprxGaps/2+gKanalMesafesi
	
	--yatay çizgiler ALT
	
	local stepYalt = math.floor(gobekGenislik3/gaps)
	
	if ((gobekGenislik3/gaps)-stepYalt)  >= 0.5 then		
		stepYalt = stepYalt + 1
	end
	
	local aprxGaps3 = gobekGenislik3/(stepYalt+1)
	local e3 = alt+sunkenWidth+h+sbt+aprxGaps3/2+gKanalMesafesi
	
	
	
	if intEdgeToolExist > 0 then
		
		sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
		if intEdgeVorSVorR == 0 then --(V:0/SV:1/R:2)
			G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
			G.setThickness(-intEdgeToolExist)
			G.rectangle({sol+sunkenWidth, alt+altH+ara+sunkenWidth}, {X-sag-sunkenWidth, Y-ust-sunkenWidth})
			G.rectangle({sol+sunkenWidth, alt+sunkenWidth}, {X-sag-sunkenWidth, Y-(ust+ustH+ara)-sunkenWidth})
		
		elseif intEdgeVorSVorR == 1 then
			G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
			G.setThickness(0)
			local corner1, corner2 = G.sunkenFrame({sol, alt+altH+ara}, {X-sag, Y-ust}, ad, aV, vWideDiameter)
			corner1[3] = 0
			corner2[3] = 0
			local corner3, corner4 = G.sunkenFrame({sol, alt}, {X-sag, Y-(ust+ustH+ara)}, ad, aV, vWideDiameter)
			corner3[3] = 0
			corner4[3] = 0
		
		elseif intEdgeVorSVorR == 2 then
			G.setLayer("H_Raduslu_Pah_" .. "Ic")  -- V shaped deep with large angle 
			G.setThickness(-intEdgeToolExist)
			distanceSag = sag + 0
			distanceSol = sol + 0
			distance1Ust = ust + 0
			distance1Alt = alt+altH+ara + 0
			distance2Ust = ust+ustH+ara + 0
			distance2Alt = alt + 0
			
			point1 = {distanceSol, distance1Alt}
			point2 = {X-distanceSag, Y-distance1Ust}
			G.rectangle(point1,point2)					--ustteki
			
			point3 = {distanceSol, distance2Alt}
			point4 = {X-distanceSag, Y-distance2Ust}
			G.rectangle(point3,point4)					--alttaki 
			sunkenWidth = 5  --radus degeri
		end 
	end
  
	if extEdgeToolExist  > 0 then
		if extEdgeVorR == 0 then --  (V:0/R:1)
			G.setLayer("K_AciliV_Pah")	
		elseif extEdgeVorR ==1 then
			G.setLayer("H_Raduslu_Pah_" .. "DIS")	
		end
		G.setThickness(-extEdgeToolExist)
		G.rectangle({0,0},{X,Y})
	end
	
	function BulgePoly(distance1sl, distance1sg, distance2al, distance2us, R)
		local points = {
			{distance1sl+R, distance2al,0,0},		
			{X-distance1sg-R, distance2al,0,B},		
			{X-distance1sg, distance2al+R,0,0},		
			{X-distance1sg, Y-distance2us-R,0,B},	
			{X-distance1sg-R, Y-distance2us,0,0},	
			{distance1sl+R, Y-distance2us,0,B},		
			{distance1sl, Y-distance2us-R,0,0},		
			{distance1sl, distance2al+R,0,B},		
			{distance1sl+R, distance2al,0,0}		
			}	
			return points
	end
 
	if topGrooveExist  > 0 then
      
		G.setThickness(-topGrooveExist)
		if topGrooveBorF == 0 then  --0 ballnose or 1 flat
			G.setLayer("K_Ballnose"..dT.."mm")  -- DEEP cleanup
			if ad > dT/2 then
				ustKanal = dT/2
			else
				ustKanal = ((dT/2)^2 - ((dT/2)-topGrooveExist)^2)^0.5
			end
		elseif topGrooveBorF == 1 then
			G.setLayer("K_Freze"..dT.."mm")  -- DEEP cleanup
			ustKanal = dT/2
		end
		distanceSag = sag + ustKanal
		distanceSol = sol + ustKanal
		distance1Ust = ust + ustKanal
		distance1Alt = alt + altH + ara + ustKanal
		distance2Ust = ust + ustH + ara + ustKanal
		distance2Alt = alt + ustKanal

		if topGrvExtExist == 1 then
			G.line({distanceSol, distance1Alt}, {distanceSol, Y})					--ustteki tek çizgiler
			G.line({X-distanceSag, distance1Alt}, {X-distanceSag, Y})
			G.line({distanceSol, Y-distance1Ust}, {X-distanceSag, Y-distance1Ust})
			G.line({distanceSol, distance1Alt}, {X-distanceSag, distance1Alt})
			
			G.line({distanceSol, Y-distance2Ust}, {distanceSol, 0})					--alttaki tek çizgiler
			G.line({X-distanceSag, Y-distance2Ust}, {X-distanceSag, 0})
			G.line({distanceSol, Y-distance2Ust}, {X-distanceSag, Y-distance2Ust})
			G.line({distanceSol, distance2Alt}, {X-distanceSag, distance2Alt})
		
		elseif topGrvExtExist == 0 then
			if intEdgeToolExist > 0 then
				if intEdgeVorSVorR == 0 then
					--points1 = BulgePoly(distance,distance1,sunkenWidth-ustKanal)
					points1 = BulgePoly(distanceSol,distanceSag,distance1Alt,distance1Ust,sunkenWidth-ustKanal)
					G.polylineimp(points1)
					points2 = BulgePoly(distanceSol,distanceSag,distance2Alt,distance2Ust,sunkenWidth-ustKanal)
					G.polylineimp(points2)
				else
					point1 = {distanceSol, distance1Alt}
					point2 = {X-distanceSag, Y-distance1Ust}
					G.rectangle(point1,point2)					--ustteki
					
					point3 = {distanceSol, distance2Alt}
					point4 = {X-distanceSag, Y-distance2Ust}
					G.rectangle(point3,point4)					--alttaki
				end
			else
				point1 = {distanceSol, distance1Alt}
				point2 = {X-distanceSag, Y-distance1Ust}
				G.rectangle(point1,point2)					--ustteki
				
				point3 = {distanceSol, distance2Alt}
				point4 = {X-distanceSag, Y-distance2Ust}
				G.rectangle(point3,point4)					--alttaki
			end
		end
	
	end
	
	if extGrooveExist  > 0 then
    
		if extGrvBorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			G.setLayer("K_Ballnose")
		elseif extGrvBorForV == 1 then
			G.setLayer("K_Freze")  
		elseif extGrvBorForV == 2 then
			G.setLayer("K_AciliV")  
		end	
		
		
		G.setThickness(-extGrooveExist)
		
		distanceSag = sag - b
		distanceSol = sol - b
		distance1Ust = ust - b
		distance1Alt = alt + altH + ara - b
		distance2Ust = ust + ustH + ara - b
		distance2Alt = alt - b

		point1 = {distanceSol, distance1Alt}
		point2 = {X-distanceSag, Y-distance1Ust}
		G.rectangle(point1,point2)					--ustteki
		
		point3 = {distanceSol, distance2Alt}
		point4 = {X-distanceSag, Y-distance2Ust}
		G.rectangle(point3,point4)					--alttaki
	end
	
	if extGroove2Exist  > 0 then
    
		if extGrv2BorForV == 0 then  --0 ballnose or 1 flat 2 V tool
			G.setLayer("K_Ballnose")
		elseif extGrv2BorForV == 1 then
			G.setLayer("K_Freze")  
		elseif extGrv2BorForV == 2 then
			G.setLayer("K_AciliV")  
		end	

		G.setThickness(-extGroove2Exist)
		distanceSag = sag - c
		distanceSol = sol - c
		distance1Ust = ust - c
		distance1Alt = alt + altH + ara - c
		distance2Ust = ust + ustH + ara - c
		distance2Alt = alt - c

		point1 = {distanceSol, distance1Alt}
		point2 = {X-distanceSag, Y-distance1Ust}
		G.rectangle(point1,point2)					--ustteki
		
		point3 = {distanceSol, distance2Alt}
		point4 = {X-distanceSag, Y-distance2Ust}
		G.rectangle(point3,point4)		
	end
	
	G.setLayer("H_Freze"..cT.."mm_Ic")
	G.setThickness(-materialThickness)
	if sunkenWidth > 0 then
		G.rectangle({sol+sunkenWidth, alt+altH+ara+sunkenWidth}, {X-sag-sunkenWidth, Y-ust-sunkenWidth})	--UST BOLUM
	elseif sunkenWidth == 0 then
		G.rectangle({sol+ustKanal, alt+altH+ara+ustKanal}, {X-sag-ustKanal, Y-ust-ustKanal})  --UST BOLUM
	end
	
	if bottomWindowExist == 1 then		--eger alt bolum calıysa
		G.setLayer("H_Freze"..cT.."mm_Ic")
		G.setThickness(-materialThickness)
		if sunkenWidth > 0 then
			G.rectangle({sol+sunkenWidth, alt+sunkenWidth}, {X-sag-sunkenWidth, Y-(ust+ustH+ara)-sunkenWidth})--ALT BOLUM
		elseif sunkenWidth == 0 then
			G.rectangle({sol+ustKanal, alt+ustKanal}, {X-sag-ustKanal, Y-(ust+ustH+ara)-ustKanal})	--ALT BOLUM
		end
	else	--alt bölümde cam yoksa
		if cabCoreExist == 1 then		--gobek varsa
	
			if h<cW and h>cT then
				cW = cT
			end
			
			if h > cT then
				-- if X>xLimit and Y>yLimit then
				G.setLayer("K_Freze" .. cW .. "mm")  -- DEEP cleanup
				G.setThickness(-ad)
				distanceSol = sol+sunkenWidth+cW/2		
				distanceSag = X-(sag+sunkenWidth+cW/2)		
				
				distance2Ust = alt+altH-sunkenWidth-cW/2			--alt bolum
				distance2Alt = alt+sunkenWidth+cW/2
				
				point3 = {distanceSol, distance2Ust}
				point4 = {distanceSag, distance2Alt}
				G.rectangle(point3,point4)
				
				if h>cW then
					distanceSol = sol+sunkenWidth+h-cW/2		
					distanceSag = X-(sag+sunkenWidth+h-cW/2)		
					distance1Ust = Y-(ust+sunkenWidth+h-cW/2)		--ust bolum
					distance1Alt = Y-(ust+ustH-sunkenWidth-h+cW/2)
					distance2Ust = alt+altH-sunkenWidth-h+cW/2			--alt bolum
					distance2Alt = alt+sunkenWidth+h-cW/2
					
					point5 = {distanceSag, distance1Alt}
					point6 = {distanceSol, distance1Ust}
					--G.rectangle(point5,point6)
					k = (h-cW)/(cW/2)
					for i=1, k, 1 do
						-- point1 = G.ptAdd(point1,{cW/2,-cW/2})
						-- point2 = G.ptSubtract(point2, {cW/2,-cW/2})
						point3 = G.ptAdd(point3,{cW/2,-cW/2})
						point4 = G.ptSubtract(point4, {cW/2,-cW/2})
						if point1[1]>point5[1]-cW/2 then
							break
						end
						G.rectangle(point3,point4)
					end
				end
				
				G.setThickness(-(ad))
				distanceSol = sol+sunkenWidth+h-cW/2		
				distanceSag = X-(sag+sunkenWidth+h-cW/2)		
				distance2Ust = alt+altH-sunkenWidth-h+cW/2			--alt bolum
				distance2Alt = alt+sunkenWidth+h-cW/2
				
				point3 = {distanceSol, distance2Ust}
				point4 = {distanceSag, distance2Alt}
				G.rectangle(point3,point4)
				
				if shapeToolExist  > 0 then  ----Göbekte desen bıçağı varsa
					G.setLayer("K_Desen")  -- DEEP cleanup
					G.setThickness(-shapeToolExist)
					
					distanceSol = sol+sunkenWidth+h	
					distanceSag = X-(sag+sunkenWidth+h)	
					distance2Ust = alt+altH-sunkenWidth-h		--alt bolum
					distance2Alt = alt+sunkenWidth+h
					
					point3 = {distanceSol, distance2Ust}
					point4 = {distanceSag, distance2Alt}
					G.rectangle(point3,point4)
				else 
					G.setLayer("K_AciliV" .. aV)  --- Gobek
					G.setThickness(-ad)
					
					distanceSol = sol+sunkenWidth+h	
					distanceSag = X-(sag+sunkenWidth+h)	
					distance2Ust = alt+altH-sunkenWidth-h		--alt bolum
					distance2Alt = alt+sunkenWidth+h
					
					point3 = {distanceSol, distance2Ust}
					point4 = {distanceSag, distance2Alt}
					G.rectangle(point3,point4)
				end
			end
			if h >= 0 then
		
				if intRectGrvExist  > 0 then      ------İç kanal varsa
					local check = sol + sag + 2*h + 2*d
					if X < check or Y < check then
						print("Part dimension too small, check a + h + d value")
						return true
					end
					G.setLayer("K_I_Kanal")  -- DEEP cleanup
					G.setThickness(-intRectGrvExist)
					
					distanceSol = sol+sunkenWidth+h	+ d
					distanceSag = X-(sag+sunkenWidth+h+d)	
					distance2Ust = alt+altH-sunkenWidth-h-d		--alt bolum
					distance2Alt = alt+sunkenWidth+h+d
					
					point3 = {distanceSol, distance2Ust}
					point4 = {distanceSag, distance2Alt}
					G.rectangle(point3,point4)
					
				end
				
				if intGrvExist > 0 then      ------İç kanal varsa
					if grooveVert_Hor == 1 then			--1 dikey
						G.setLayer("K_I_Kanal")  
						G.setThickness(-intGrvExist)
		
						for i=0,stepX do
							local x1 = e1+i*aprxGaps
							local offsetUst1 = ust + ustH + ara + sunkenWidth + h + gKanalMesafesi
							local offsetAlt1 = alt + sunkenWidth + h + gKanalMesafesi
							point5 = {x1,offsetAlt1}
							point6 = {x1,Y-offsetUst1}  
							G.line(point5,point6,0)
							-- end
							
							i = i+1
						end 
						
					elseif grooveVert_Hor == 0 then		--0 yatay
						G.setLayer("K_I_Kanal")  
						G.setThickness(-intGrvExist)
						for i=0,stepYalt do
							local y3 = e3+i*aprxGaps3
							local offsetSol3 = sol + sunkenWidth + h + gKanalMesafesi
							local offsetSag3 = sag + sunkenWidth + h + gKanalMesafesi
							point5 = {offsetSol3,y3}
							point6 = {X-offsetSag3,y3}  
							G.line(point5,point6,0)
							i = i+1
						end 
					end
				end
			end
		else    -------------- Göbek yoksa tüm kapaklarda
			G.setLayer("Cep_Acma")  -- DEEP cleanup
			G.setThickness(-ad)
			distanceSol = sol+sunkenWidth
			distanceSag = sag+sunkenWidth
			distance2ust = ust+ustH+ara+sunkenWidth
			distance2alt = alt + sunkenWidth
			point3 = {distanceSol, distance2alt}
			point4 = {X-distanceSag, Y-distance2ust}
			G.rectangle(point3,point4)					--alt desen alan tarama
		end
		
		--if h >= cT and h < 3*cT and cabCoreExist == 1 then
		if h >= cT then
			G.setLayer("K_Freze" .. cT .. "mm")
			G.setThickness(-ad)
			
			distanceSol = sol+sunkenWidth+cT/2
			distanceSag = sag+sunkenWidth+cT/2
			distance2ust = ust+ustH+ara+sunkenWidth+cT/2
			distance2alt = alt + sunkenWidth+cT/2

			point3 = {distanceSol, distance2alt}
			point4 = {X-distanceSag, Y-distance2ust}
			G.rectangle(point3,point4)
		end
	end
	
  return true
end

------------------------------------------------

require "ADekoDebugMode"
