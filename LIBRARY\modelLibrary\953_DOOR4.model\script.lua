-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  a = 0
  sag, sol= 100, 100
  ust, alt= 100 ,100
  ad = 6 -- toolDepth kanal derinliği
  c = 20			--pencere araları düz mesafe
  h = 25
  aV = 120					-- Vbit Acisi
  cW = 20                   -- Tarama Bıçak Çapı
  cT = 6                   -- İnce bıçak çapı (Köşe Temizleme ve pencere kesim)
  
  windowOpExist				= 1	--alt bolum Duz mu, cam mi (duz: 1 / Cam: 0)
  blockNoX = 1
  blockNoY = 2
  
  topGrooveExist    		= 2  --D<PERSON><PERSON> kanal var mı (Var: Derinlik / Yok: 0)
  dT						= 10 --Ic kenar ustu kanal islemi bicak capi
  topGrooveBorF    			= 1  --Ust kanal Ballnose mu? Duz Kanal mi? (B:0/F:1)
  cabCoreExist            	= 1 -- Gobek var mı Var:1 Yok:0 --gobek yoksa ortayı tarama yap
  shapeToolExist         	= 5 --Gobekte Desen bıcak var mı derinlik/0:yok
  
  bottomSectExist 			= 0  --alt bolum var mı? (Var:1/Yok:0)
  botWindowOpExist 			= 1  --alt bolum Duz mu, cam mi (duz: 1 / Cam: 0)
  ara 						= 50
  KD 						= 400
  altH          = 500
  botBlockNoX 				= 0
  botBlockNoY 				= 0 
  
  extEdgeToolExist 			= 0  --Dış kenar Pah işlemi var mı? (Var: Derinlik / Yok: 0)
  extEdgeVorR 				= 0  --Dış kenar Pah işlemi V mi? Raduslu mu? (V:0/R:1)
  edgeCornerRExist 			= 0   -- Kapak köşe Radüsü var mı? (Var :Yaricap/ Yok:0)
    
   
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)
 
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
	if a>0 then
		ust = a
		sol = a
		sag = a
		ara = a
	end
	
	if KD >0 then
		altH = KD-ara/2-alt
	end
	
	local limitX = sol+sag
	local limitY = alt+altH+ara+ust
	local ustH = Y-(alt+altH+ara+ust)
   
	if X<limitX or Y<limitY then
		print("Part dimension too small")
		return true
	end
  
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
  local stepX = (blockNoX-1)		--Number of laths on X axis
  local stepY = (blockNoY-1)		--Number of laths on Y axis
  local altPoz = 0
	 
  if bottomSectExist>0 then
	altPoz = alt+altH+ara
  else
	altPoz = alt
  end
	local innerX = X-sol-sag-stepX*c		--Width of the inner rectangle on the x axis
	local innerY = Y-ust-altPoz-stepY*c		--Length of the inner rectangle on the y axis
  
  local correctedblockX = innerX / blockNoX
  local correctedblockY = innerY / blockNoY

	--alt bolum hesaplamaları
  local botStepX = (botBlockNoX-1)		--Number of laths on X axis
  local botStepY = (botBlockNoY-1)		--Number of laths on Y axis
  local botInnerX = X-sol-sag-botStepX*c
  local botInnerY = Y-ust-alt-ara-ustH-botStepY*c
  local botCorrectedblockX = botInnerX / botBlockNoX		--alt bolum hesapları
  local botCorrectedblockY = (botInnerY / botBlockNoY)
  
    function rectToPoly (c1,c2)             -- gobekteki islemler icin gerekli
		points = { 
				{c1[1],c1[2]},
				{c2[1],c1[2]},
				{c2[1],c2[2]},
				{c1[1],c2[2]},
				{c1[1],c1[2]}
				}
		return points
	end
	
	function CabCoreOps (corner1,corner2)   --- bolumlerin içindeki işlemler buradan yapılıyor
		points = rectToPoly (corner1,corner2)
		if cabCoreExist>0 and h>0 then
		
			if h<cW and h>cT then
				cW = cT
			end
			
			if h > cT then
			--if X>xLimit and Y>yLimit then
				G.setLayer("Cep_Acma" .. cW .. "mm")  -- DEEP cleanup
				G.setThickness(-ad)
				G.polylineimp (points)
		
				if h>cW then
					G.polylineimp (G.offSet(points, -h))
				end
					
				G.setLayer("K_TarKoseTemizl"..cT.."mm")            --kose temizleme
				G.setThickness(-ad)
				if h >= 3*cT then
					G.setThickness(-ad)
					G.cleanCorners(corner1,corner2,0,cT)
				else
					G.polylineimp(G.offSet(points, -cT/2))
				end
				
				if shapeToolExist  > 0 then  ----Göbekte desen bıçağı varsa
					G.setLayer("K_Desen")  -- DEEP cleanup
					G.setThickness(-shapeToolExist)
					G.polylineimp (G.offSet(points, -h))
					else 
					G.setLayer("K_AciliV" .. aV)  --- Gobek
					G.setThickness(-ad)
					G.polylineimp (G.offSet(points, -h))
			
				end
			end
		elseif h==0 and cabCoreExist>0 then         --h yoksa, gobekte tarama yoksa
		else 
		G.setLayer("Cep_Acma")	
		G.setThickness(-ad)
		G.polylineimp (points)
		G.setLayer("K_Freze"..cT.."mm")            --kose temizleme
		G.setThickness(-ad)
		G.cleanCorners(corner1,corner2,0,cT)
		end     
	end
  
  
	if extEdgeToolExist  > 0 then
		if extEdgeVorR == 0 then --  (V:0/R:1)
			G.setLayer("K_AciliV_Pah")	
		elseif extEdgeVorR ==1 then
			G.setLayer("H_Raduslu_Pah_" .. "DIS")	
		end
		G.setThickness(-extEdgeToolExist)
		G.rectangle({0,0},{X,Y})
	end
  
  for i=0, stepX, 1
	do

    x1 = sol+i*(correctedblockX+c)
    x2 = sol+(i+1)*(correctedblockX)+i*c
    for j=0, stepY, 1
		do
		y1 = altPoz+j*(correctedblockY+c)
		y2 = altPoz+(j+1)*(correctedblockY)+j*c
		point1 = {x1,y1}
		point2 = {x2,y2}
        G.setLayer("K_AciliV"..aV)  -- windows
		G.setThickness(0)
		corner1, corner2 = G.sunkenFrame(point1, point2, ad, aV, 50)
		-------
		if topGrooveExist  > 0 then
			G.setThickness(-topGrooveExist)
			if topGrooveBorF == 0 then  --0 ballnose or 1 flat
				G.setLayer("K_Ballnose"..dT.."mm")  -- DEEP cleanup
				if ad > dT/2 then
					ustKanal = dT/2
				else
					ustKanal = ((dT/2)^2 - ((dT/2)-topGrooveExist)^2)^0.5
				end
		
			elseif topGrooveBorF == 1 then
				G.setLayer("K_Freze"..dT.."mm")  -- DEEP cleanup
				ustKanal = dT/2
			end
			points=rectToPoly(corner1,corner2)
			G.polylineimp (G.offSet(points, -ustKanal))
			
		end
		-- -----------
		if windowOpExist>0 then                           -- cam varsa 0 yoksa 1
			CabCoreOps (corner1,corner2)                    --gobek islmeleri
		else												--cam yeri kesim
			G.setLayer("H_Freze_Ebatlama_ic")	
			G.setThickness(-materialThickness)
			G.rectangle(corner1,corner2)
		end
    end
  end
	if bottomSectExist>0 then
		for i=0, botStepX, 1
			do
			x1 = sol+i*(botCorrectedblockX+c)
			x2 = sol+(i+1)*(botCorrectedblockX)+i*c
			
			for j=0, botStepY, 1
				do
				y1 = alt+j*(botCorrectedblockY+c)
				y2 = alt+(j+1)*(botCorrectedblockY)+j*c
				point1 = {x1,y1}
				point2 = {x2,y2}
				G.setLayer("K_AciliV"..aV)  -- windows
				G.setThickness(0)
				corner1, corner2 = G.sunkenFrame(point1, point2, ad, aV, 50)
				-------
				if topGrooveExist  > 0 then
					G.setThickness(-topGrooveExist)
					if topGrooveBorF == 0 then  --0 ballnose or 1 flat
						G.setLayer("K_Ballnose"..dT.."mm")  -- DEEP cleanup
						if ad > dT/2 then
							ustKanal = dT/2
						else
							ustKanal = ((dT/2)^2 - ((dT/2)-topGrooveExist)^2)^0.5
						end
				
					elseif topGrooveBorF == 1 then
						G.setLayer("K_Freze"..dT.."mm")  -- DEEP cleanup
						ustKanal = dT/2
					end
					points1=rectToPoly(corner1,corner2)
					-- print(ustKanal)
					G.polylineimp (G.offSet(points1, -ustKanal))
				end
				-- -----------
				if botWindowOpExist>0 then                           -- cam varsa 0 yoksa 1
					CabCoreOps (corner1,corner2)                    --gobek islmeleri
				else												--cam yeri kesim
					G.setLayer("H_Freze_Ebatlama_ic")	
					G.setThickness(-materialThickness)
					G.rectangle(corner1,corner2)
				end
			end
		end
	end
  return true
end

require "ADekoDebugMode"
