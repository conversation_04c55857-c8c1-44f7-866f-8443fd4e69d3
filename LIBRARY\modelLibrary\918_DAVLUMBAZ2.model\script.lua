-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  minimum = 150
  --limit = 350
  
  a = 40
  aa = 30
  ad = 7
  aV = 90
  d = 10
  h = 10
  m = 100
  yy = 70
  cT = 6
  
  topGrooveExist   				= 2   -- Vbit Ustu kanal var mı? derinlik/0:yok
  topGrvExtExist				= 0 --Ust kanal uzatma var mı? (Var:1/Yok:0)
  cabCoreExist            = 0 -- Gobek var mı Var:1 Yok:0 --gobek yoksa ortayı tarama yap
  shapeToolExist         = 0 --Gobekte Desen bıcak var mı derinlik/0:yok
  intGrvExist          = 2 --ic kanal islemi var mı? derinlik/0:yok
  extEdgeVtoolExist       	= 0  -- <PERSON><PERSON><PERSON> kenar <PERSON> işlemi var mı? derinlik/0:yok
  edgeCornerRExist          = 0  -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2)
  
  local bulge1 = G.bulge({m,0,0,0}, {X/2, yy,0,0}, {X-m, 0, 0, 0})
  if (bulge1>1) then
    print("Bulge too large")
    return true
  end
  
  local bulge =  math.tan(math.pi/8)
  G.setFace("top")
  G.setThickness(-materialThickness)
  
  
  local pointExt ={}
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
      pointExt = {{edgeCornerRExist,0},		--chamfered part shape
      {edgeCornerRExist+m,0,0,bulge1},		--chamfered part shape
      {X-m,0,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0}}
      G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {edgeCornerRExist+m,0,0,bulge1},		--chamfered part shape
      {X-m,0,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
      pointExt = {{0,0},		--chamfered part shape
      {m,0,0,bulge1},		--chamfered part shape
      {X-m,0},		--chamfered part shape
      {X,0},
      {X,Y},
      {0,Y},
      {0,0}}
    G.makePartShape({0,0},		--chamfered part shape
      {m,0,0,bulge1},		--chamfered part shape
      {X-m,0},		--chamfered part shape
      {X,0},
      {X,Y},
      {0,Y},
      {0,0})
  end
  
  --poins = 
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
    G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.polylineimp(pointExt)
  end

  local c = 0
  local pointb2 = a
  local bulge2 = G.bulge({pointb2,a,0,0}, {X/2, yy+a,0,0}, {X-pointb2, a, 0, 0})
  
    if (bulge2>1) then
      print("Bulge too large")
      return true
    end
    

  points3 = {{X-a,a},		--chamfered part shape
      {X-a,Y-a},
      {a,Y-a},
      {a,a,0,bulge2},
      {X-a,a}}
    
    local sekil2 = true
    --if m> 2*a+2*sunkenWidth and h == 0 then       --h ofseti doğru olmadığı için kapatıldı
    --  c= 1*(m-2*a)
    --  pointb2 = a+c
    --  points2 = {{a,a},		--chamfered part shape
    --  {a+c,a,0,bulge2},		--chamfered part shape
    --  {X-a-c,a},		--chamfered part shape
    --  {X-a,a},		--chamfered part shape
    --  {X-a,Y-a},
    --  {a,Y-a},
    --  {a,a}}
    --  G.setLayer("V_AciliV"..aV)
    --  G.setThickness(-ad)
    --  G.polylineimp(points2)
    --  sekil2 = false
    --else
      G.setLayer("K_AciliV"..aV)
      G.setThickness(0)
      pointsToCut = G.sunkenFrameAny(points3, 2, ad, aV, vWideDiameter)
     
    --end
    
    if cabCoreExist >0 and  h>0 then
        if shapeToolExist > 0 then  				          ----Göbekte desen bıçağı varsa
          G.setLayer("K_Desen")
          G.setThickness(-shapeToolExist)
        else
          G.setLayer("K_AciliV"..aV)
          G.setThickness(-ad)
        end
        G.polylineimp (G.offSet(pointsToCut, -h))
        
        G.setLayer("Cep_Acma")                  --Tarama islemi
        G.setThickness(-ad)
        G.polylineimp (pointsToCut)
        G.polylineimp (G.offSet(pointsToCut, -h))
    elseif h==0 and cabCoreExist>0 then         --h yoksa, gobekte tarama yoksa
    else        --gobek yoksa gobekte tarama yap
      if sekil2 then
        G.setLayer("Cep_Acma")                  --Tarama islemi
        G.setThickness(-ad)
        G.polylineimp (pointsToCut)
        G.setLayer("H_Freze"..cT.."mm_Ic")
        G.polylineimp(pointsToCut)
        
      end
    end 
    
    local offset = h+d
    if intGrvExist > 0 and cabCoreExist > 0 then              --ic kanal var mı
      G.setLayer("H_Freze"..cT.."mm_Ic")
      G.setThickness(-intGrvExist)
      G.polylineimp(G.offSet(pointsToCut, -offset))
    end
    
    if topGrooveExist > 0 then              --Dıs v kanal üstü kanal islemi var mı
      G.setLayer("H_Freze"..cT.."mm_Ic")
      G.setThickness(-topGrooveExist)
      G.polylineimp(points3)
      local ust = a
      if topGrvExtExist == 1 then
        G.setLayer("K_Freze"..cT.."mm")
        G.line({X-a-cT/2, Y-ust-cT/2},{X-a-cT/2, Y})
        G.line({a+cT/2, Y-ust-cT/2},{a+cT/2, Y})
        G.line({a+cT/2, ust+cT/2},{a+cT/2, 0})
        G.line({X-a-cT/2, ust+cT/2},{X-a-cT/2, 0})
      end
    end
  
  G.list()
  return true
end

require "ADekoDebugMode"
