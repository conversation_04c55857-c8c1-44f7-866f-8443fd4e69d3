import type {
  C<PERSON>Tool,
  Cylind<PERSON>Tool,
  ConicalTool,
  BallnoseTool,
  RadialTool,
  SpecialTool,
  DoorMachiningProfile,
  ToolOperation,
  ToolGeometry,
  DrawCommand
} from '@/types'
import * as THREE from 'three'

export class CNCToolService {
  private static instance: CNCToolService
  private tools: Map<string, CNCTool> = new Map()
  private machiningProfiles: Map<string, DoorMachiningProfile> = new Map()

  private constructor() {
    this.initializeDefaultTools()
    this.initializeDefaultProfiles()
  }

  public static getInstance(): CNCToolService {
    if (!CNCToolService.instance) {
      CNCToolService.instance = new CNCToolService()
    }
    return CNCToolService.instance
  }

  private initializeDefaultTools(): void {
    // Cylindrical tools for general machining
    const cylindrical3mm: CylindricalTool = {
      id: 'cyl-3mm',
      name: '3mm End Mill',
      shape: 'cylindrical',
      units: 'metric',
      diameter: 3,
      length: 40,
      flutes: 2,
      helixAngle: 30,
      description: 'Small end mill for fine detail work',
      material: 'Carbide',
      coating: 'TiN'
    }

    const cylindrical6mm: CylindricalTool = {
      id: 'cyl-6mm',
      name: '6mm End Mill',
      shape: 'cylindrical',
      units: 'metric',
      diameter: 6,
      length: 50,
      flutes: 2,
      helixAngle: 30,
      description: 'General purpose 2-flute end mill for roughing and finishing',
      material: 'HSS',
      coating: 'TiN'
    }

    const cylindrical10mm: CylindricalTool = {
      id: 'cyl-10mm',
      name: '10mm End Mill',
      shape: 'cylindrical',
      units: 'metric',
      diameter: 10,
      length: 60,
      flutes: 3,
      helixAngle: 35,
      description: 'Heavy-duty 3-flute end mill for material removal',
      material: 'Carbide',
      coating: 'TiAlN'
    }

    const cylindrical20mm: CylindricalTool = {
      id: 'cyl-20mm',
      name: '20mm End Mill',
      shape: 'cylindrical',
      units: 'metric',
      diameter: 20,
      length: 80,
      flutes: 4,
      helixAngle: 35,
      description: 'Large end mill for heavy material removal',
      material: 'Carbide',
      coating: 'TiAlN'
    }

    // Conical tools for V-grooves and chamfers
    const conical90deg: ConicalTool = {
      id: 'con-90deg',
      name: '90° V-Bit',
      shape: 'conical',
      units: 'metric',
      diameter: 20,
      length: 40,
      tipAngle: 90,
      tipDiameter: 0.2,
      flutes: 2,
      description: 'Sharp V-bit for precise grooves and chamfers',
      material: 'Carbide'
    }

    const conical60deg: ConicalTool = {
      id: 'con-60deg',
      name: '60° V-Bit',
      shape: 'conical',
      units: 'metric',
      diameter: 15,
      length: 35,
      tipAngle: 60,
      tipDiameter: 0.1,
      flutes: 2,
      description: 'Narrow angle V-bit for fine detail work',
      material: 'Carbide'
    }

    const conical45deg: ConicalTool = {
      id: 'con-45deg',
      name: '45° V-Bit',
      shape: 'conical',
      units: 'metric',
      diameter: 12,
      length: 30,
      tipAngle: 45,
      tipDiameter: 0.1,
      flutes: 2,
      description: '45° V-bit for decorative grooves',
      material: 'Carbide'
    }

    // Ballnose tools for 3D contouring
    const ballnose6mm: BallnoseTool = {
      id: 'ball-6mm',
      name: '6mm Ball End Mill',
      shape: 'ballnose',
      units: 'metric',
      diameter: 6,
      length: 50,
      ballRadius: 3,
      flutes: 2,
      helixAngle: 30,
      description: 'Ball end mill for 3D contouring and smooth finishes',
      material: 'Carbide',
      coating: 'TiAlN'
    }

    const ballnose3mm: BallnoseTool = {
      id: 'ball-3mm',
      name: '3mm Ball End Mill',
      shape: 'ballnose',
      units: 'metric',
      diameter: 3,
      length: 40,
      ballRadius: 1.5,
      flutes: 2,
      helixAngle: 30,
      description: 'Small ball end mill for fine detail work',
      material: 'Carbide'
    }

    // Radial tools for corner rounding
    const radial2mm: RadialTool = {
      id: 'rad-2mm',
      name: '2mm Corner Radius',
      shape: 'radial',
      units: 'metric',
      diameter: 8,
      length: 45,
      cornerRadius: 2,
      flutes: 2,
      helixAngle: 30,
      description: 'Corner rounding tool for edge finishing',
      material: 'Carbide'
    }

    // Special tools for custom operations
    const dovetail: SpecialTool = {
      id: 'special-dovetail',
      name: 'Dovetail Cutter',
      shape: 'special',
      units: 'metric',
      diameter: 12,
      length: 40,
      specialType: 'dovetail',
      customParameters: {
        angle: 14,
        neckDiameter: 6,
        cuttingLength: 15
      },
      description: 'Dovetail cutter for joinery work',
      material: 'Carbide'
    }

    // Add tools to the map
    this.tools.set(cylindrical3mm.id, cylindrical3mm)
    this.tools.set(cylindrical6mm.id, cylindrical6mm)
    this.tools.set(cylindrical10mm.id, cylindrical10mm)
    this.tools.set(cylindrical20mm.id, cylindrical20mm)
    this.tools.set(conical45deg.id, conical45deg)
    this.tools.set(conical60deg.id, conical60deg)
    this.tools.set(conical90deg.id, conical90deg)
    this.tools.set(ballnose3mm.id, ballnose3mm)
    this.tools.set(ballnose6mm.id, ballnose6mm)
    this.tools.set(radial2mm.id, radial2mm)
    this.tools.set(dovetail.id, dovetail)
  }

  private initializeDefaultProfiles(): void {
    // Standard door machining profile
    const standardDoorProfile: DoorMachiningProfile = {
      id: 'standard-door',
      name: 'Standard Door Profile',
      description: 'Basic door machining with top and bottom surface operations',
      topSurfaceOperations: [
        {
          toolId: 'cyl-10mm',
          operation: 'roughing',
          surface: 'top',
          depth: -5,
          feedRate: 1000,
          spindleSpeed: 12000,
          stepDown: 2,
          stepOver: 6
        },
        {
          toolId: 'cyl-6mm',
          operation: 'finishing',
          surface: 'top',
          depth: -5,
          feedRate: 800,
          spindleSpeed: 15000,
          stepDown: 1,
          stepOver: 3
        }
      ],
      bottomSurfaceOperations: [
        {
          toolId: 'cyl-10mm',
          operation: 'roughing',
          surface: 'bottom',
          depth: -5,
          feedRate: 1000,
          spindleSpeed: 12000,
          stepDown: 2,
          stepOver: 6
        },
        {
          toolId: 'ball-6mm',
          operation: 'finishing',
          surface: 'bottom',
          depth: -5,
          feedRate: 600,
          spindleSpeed: 18000,
          stepDown: 0.5,
          stepOver: 2
        }
      ],
      tools: Array.from(this.tools.values())
    }

    this.machiningProfiles.set(standardDoorProfile.id, standardDoorProfile)
  }

  // Tool management methods
  public getAllTools(): CNCTool[] {
    return Array.from(this.tools.values())
  }

  public getToolById(id: string): CNCTool | undefined {
    return this.tools.get(id)
  }

  public getToolsByShape(shape: string): CNCTool[] {
    return Array.from(this.tools.values()).filter(tool => tool.shape === shape)
  }

  public addTool(tool: CNCTool): void {
    this.tools.set(tool.id, tool)
  }

  public removeTool(id: string): boolean {
    return this.tools.delete(id)
  }

  public updateTool(tool: CNCTool): void {
    this.tools.set(tool.id, tool)
  }

  // Machining profile methods
  public getAllProfiles(): DoorMachiningProfile[] {
    return Array.from(this.machiningProfiles.values())
  }

  public getProfileById(id: string): DoorMachiningProfile | undefined {
    return this.machiningProfiles.get(id)
  }

  public addProfile(profile: DoorMachiningProfile): void {
    this.machiningProfiles.set(profile.id, profile)
  }

  public removeProfile(id: string): boolean {
    return this.machiningProfiles.delete(id)
  }

  public updateProfile(profile: DoorMachiningProfile): void {
    this.machiningProfiles.set(profile.id, profile)
  }

  // Utility methods
  public getRecommendedToolsForOperation(operation: string, _surface: string = 'top'): CNCTool[] {
    const recommendations: Record<string, string[]> = {
      'roughing': ['cyl-10mm', 'cyl-6mm'],
      'finishing': ['cyl-6mm', 'ball-6mm', 'ball-3mm'],
      'profiling': ['con-90deg', 'con-60deg', 'rad-2mm'],
      'drilling': ['cyl-6mm', 'cyl-10mm'],
      'pocketing': ['cyl-10mm', 'cyl-6mm']
    }

    const toolIds = recommendations[operation] || []
    return toolIds.map(id => this.tools.get(id)).filter(Boolean) as CNCTool[]
  }

  public calculateMachiningTime(operations: ToolOperation[], materialVolume: number): number {
    // Simple estimation based on material removal rate
    let totalTime = 0

    operations.forEach(op => {
      const tool = this.getToolById(op.toolId)
      if (tool && op.feedRate) {
        // Simplified calculation: volume / (feed rate * tool diameter)
        const removalRate = op.feedRate * tool.diameter * (op.stepDown || 1)
        totalTime += materialVolume / removalRate
      }
    })

    return totalTime // in minutes
  }

  // Automatic tool detection from layer names
  public detectToolFromLayerName(layerName: string): CNCTool | null {
    if (!layerName) return null

    const layer = layerName.toLowerCase()

    // Check for prohibited layers first
    if (this.isProhibitedLayer(layerName)) {
      return null
    }

    // Check for generic layers that should be prohibited
    if (this.isGenericLayer(layerName)) {
      return null
    }

    // Simple numeric patterns (20MM, 30MM, 5MM)
    const simpleNumericMatch = layer.match(/^(\d+)mm$/i)
    if (simpleNumericMatch) {
      const diameter = parseInt(simpleNumericMatch[1])
      return this.findToolByShapeAndDiameter('cylindrical', diameter)
    }

    // Cylindrical tools (Freze) - Enhanced patterns
    if (layer.includes('freze') || layer.includes('k_freze') ||
        layer.includes('roughing') || layer.includes('finishing')) {
      const diameterMatch = layer.match(/(\d+)mm/) || layer.match(/freze(\d+)/) || layer.match(/(\d+)mmfreze/)
      const diameter = diameterMatch ? parseInt(diameterMatch[1]) : 6
      return this.findToolByShapeAndDiameter('cylindrical', diameter)
    }

    // Ball nose tools (Ballnose) - Enhanced patterns
    if (layer.includes('ballnose') || layer.includes('k_ballnose')) {
      const diameterMatch = layer.match(/(\d+)mm/) || layer.match(/ballnose[_]?(\d+)/) || layer.match(/(\d+)/)
      const diameter = diameterMatch ? parseInt(diameterMatch[1]) : 6
      return this.findToolByShapeAndDiameter('ballnose', diameter)
    }

    // Special ballnose variants (K_BalikSirti, K_Desen)
    if (layer.includes('baliksırti') || layer.includes('baliksırti') ||
        layer.includes('balıksırtı') || layer.includes('k_baliksırti')) {
      const diameterMatch = layer.match(/(\d+)/)
      const diameter = diameterMatch ? parseInt(diameterMatch[1]) : 6
      return this.findToolByShapeAndDiameter('special', diameter) // Special tool as per feedback
    }

    if (layer.includes('k_desen') || layer.includes('desen')) {
      const diameterMatch = layer.match(/(\d+)/)
      const diameter = diameterMatch ? parseInt(diameterMatch[1]) : 3
      return this.findToolByShapeAndDiameter('ballnose', diameter)
    }

    // Conical tools (V-bit, Acili) - Enhanced patterns
    if (layer.includes('aciliv') && layer.match(/\d+/)) { // Only if has specific angle
      const angleMatch = layer.match(/aciliv(\d+)/)
      if (angleMatch) {
        const angle = parseInt(angleMatch[1])
        return this.findToolByShapeAndAngle('conical', angle)
      }
    }

    if (layer.includes('vgroove') || layer.includes('v_oyuk45')) { // Specific V-groove patterns
      const angleMatch = layer.match(/(\d+)deg/) || layer.match(/v_oyuk(\d+)/)
      const angle = angleMatch ? parseInt(angleMatch[1]) : 90
      return this.findToolByShapeAndAngle('conical', angle)
    }

    // Special operations
    if (layer.includes('cep_acma') || layer.includes('cep') || layer.includes('pocket')) {
      return this.findToolByShapeAndDiameter('cylindrical', 10)
    }

    // Panel operations
    if (layer.includes('panel')) {
      return this.findToolByShapeAndDiameter('cylindrical', 6)
    }

    // Kanal operations
    if (layer.includes('k_kanal') || layer.includes('kanal')) {
      return this.findToolByShapeAndDiameter('cylindrical', 6)
    }

    // Form tools and special operations
    if (layer.includes('form') || layer.includes('k_form') || layer.includes('jnotch')) {
      return this.findToolByShapeAndDiameter('special', 6)
    }

    // Line/drawing operations
    if (layer.includes('cizgi') || layer.includes('line')) {
      return this.findToolByShapeAndDiameter('cylindrical', 3)
    }

    // Edge profiling - corrected radius interpretation
    if (layer.includes('edge') && layer.includes('radius')) {
      const radiusMatch = layer.match(/(\d+)mm/)
      const radius = radiusMatch ? parseInt(radiusMatch[1]) : 2
      return this.findToolByShapeAndDiameter('radial', radius) // Use radius directly as per feedback
    }

    // Oyuk30 special case - should be cylindrical for pocketing
    if (layer.includes('oyuk') && layer.match(/\d+/)) {
      const diameterMatch = layer.match(/(\d+)/)
      const diameter = diameterMatch ? parseInt(diameterMatch[1]) : 30
      return this.findToolByShapeAndDiameter('cylindrical', diameter)
    }

    // Dovetail special case
    if (layer.includes('dovetail')) {
      return this.findToolByShapeAndDiameter('special', 10)
    }

    // Default fallback
    return this.findToolByShapeAndDiameter('cylindrical', 6)
  }

  // Check if layer should be prohibited from tool detection
  private isProhibitedLayer(layerName: string): boolean {
    const prohibitedPatterns = [
      'CLEANCORNERS', 'CLEANUP', 'DEEPEND', 'DEEPFRAME', 'THINFRAME',
      'V120PENCERE', 'VIOLIN', '_TN_', 'V120', 'V45'
    ]

    return prohibitedPatterns.some(pattern =>
      layerName.toUpperCase().includes(pattern.toUpperCase())
    )
  }

  // Check if layer is generic and should be prohibited
  private isGenericLayer(layerName: string): boolean {
    const genericPatterns = [
      'K_AciliV', 'V_Oyuk', 'K_toolType', 'AciliV120'
    ]

    // Generic layers are those without specific parameters
    return genericPatterns.some(pattern =>
      layerName === pattern
    )
  }

  private findToolByShapeAndDiameter(shape: string, diameter: number): CNCTool | null {
    const tools = this.getToolsByShape(shape)

    // Find exact diameter match first
    let tool = tools.find(t => t.diameter === diameter)
    if (tool) return tool

    // Find closest diameter
    tool = tools.reduce((closest, current) => {
      if (!closest) return current
      const closestDiff = Math.abs(closest.diameter - diameter)
      const currentDiff = Math.abs(current.diameter - diameter)
      return currentDiff < closestDiff ? current : closest
    }, undefined as CNCTool | undefined)

    return tool || null
  }

  private findToolByShapeAndAngle(shape: string, angle: number): CNCTool | null {
    const tools = this.getToolsByShape(shape)

    // Find exact angle match for conical tools
    let tool = tools.find(t =>
      t.shape === 'conical' && (t as any).tipAngle === angle
    )
    if (tool) return tool

    // Find closest angle
    tool = tools.reduce((closest, current) => {
      if (current.shape !== 'conical') return closest
      if (!closest) return current

      const closestAngle = (closest as any).tipAngle || 90
      const currentAngle = (current as any).tipAngle || 90
      const closestDiff = Math.abs(closestAngle - angle)
      const currentDiff = Math.abs(currentAngle - angle)

      return currentDiff < closestDiff ? current : closest
    }, undefined as CNCTool | undefined)

    return tool || null
  }

  // Get tool information from layer name with detailed analysis
  public analyzeLayerForTool(layerName: string): {
    tool: CNCTool | null
    operation: string
    surface: string
    parameters: Record<string, any>
  } {
    const layer = layerName.toLowerCase()
    let operation = 'finishing'
    let surface = 'top'
    const parameters: Record<string, any> = {}

    // Determine surface from layer name
    if (layer.includes('_sf') || layer.includes('bottom')) {
      surface = 'bottom'
    }

    // Determine operation type
    if (layer.includes('roughing') || layer.includes('kaba')) {
      operation = 'roughing'
    } else if (layer.includes('finishing') || layer.includes('son')) {
      operation = 'finishing'
    } else if (layer.includes('cep') || layer.includes('pocket')) {
      operation = 'pocketing'
    } else if (layer.includes('drilling') || layer.includes('delme')) {
      operation = 'drilling'
    } else if (layer.includes('profile') || layer.includes('profil')) {
      operation = 'profiling'
    }

    // Extract parameters from layer name
    const diameterMatch = layer.match(/(\d+)mm/)
    if (diameterMatch) {
      parameters.diameter = parseInt(diameterMatch[1])
    }

    const angleMatch = layer.match(/(\d+)(?:deg|°)/)
    if (angleMatch) {
      parameters.angle = parseInt(angleMatch[1])
    }

    const tool = this.detectToolFromLayerName(layerName)

    return {
      tool,
      operation,
      surface,
      parameters
    }
  }

  // Three.js tool geometry generation methods
  public createToolGeometry(tool: CNCTool, height: number = 50): ToolGeometry {
    let mesh: THREE.Mesh

    switch (tool.shape) {
      case 'cylindrical':
        mesh = this.createCylindricalToolMesh(tool as CylindricalTool, height)
        break
      case 'conical':
        mesh = this.createConicalToolMesh(tool as ConicalTool, height)
        break
      case 'ballnose':
        mesh = this.createBallnoseToolMesh(tool as BallnoseTool, height)
        break
      case 'radial':
        mesh = this.createRadialToolMesh(tool as RadialTool, height)
        break
      case 'special':
        mesh = this.createSpecialToolMesh(tool as SpecialTool, height)
        break
      default:
        mesh = this.createCylindricalToolMesh(tool as CylindricalTool, height)
    }

    const boundingBox = new THREE.Box3().setFromObject(mesh)

    return {
      mesh,
      tool,
      boundingBox
    }
  }

  // Removed CSG tool mesh creation methods

  // Create tool sweep mesh along a path for realistic material removal
  public createToolSweepMesh(tool: CNCTool, path: THREE.Vector3[], operation: string): THREE.Mesh {
    const toolProfile = this.getToolProfile(tool)
    const sweepGeometry = this.createSweepGeometry(toolProfile, path)

    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(sweepGeometry, material)
    mesh.userData = {
      tool,
      operation,
      toolType: 'sweep'
    }

    return mesh
  }

  private createCylindricalToolMesh(tool: CylindricalTool, height: number): THREE.Mesh {
    const radius = tool.diameter / 2
    const geometry = new THREE.CylinderGeometry(radius, radius, height, 32)
    const material = new THREE.MeshLambertMaterial({
      color: 0x888888,
      transparent: true,
      opacity: 0.7
    })

    const mesh = new THREE.Mesh(geometry, material)
    mesh.userData = { tool, toolType: 'cylindrical' }
    return mesh
  }

  private createConicalToolMesh(tool: ConicalTool, height: number): THREE.Mesh {
    const topRadius = tool.diameter / 2
    const tipRadius = (tool.tipDiameter || 0.1) / 2
    const geometry = new THREE.CylinderGeometry(tipRadius, topRadius, height, 32)
    const material = new THREE.MeshLambertMaterial({
      color: 0x666666,
      transparent: true,
      opacity: 0.7
    })

    const mesh = new THREE.Mesh(geometry, material)
    mesh.userData = { tool, toolType: 'conical' }
    return mesh
  }

  private createBallnoseToolMesh(tool: BallnoseTool, height: number): THREE.Mesh {
    const radius = tool.diameter / 2
    const ballRadius = tool.ballRadius

    // Create cylinder for shaft
    const cylinderGeometry = new THREE.CylinderGeometry(radius, radius, height - ballRadius, 32)
    const cylinderMaterial = new THREE.MeshLambertMaterial({
      color: 0x999999,
      transparent: true,
      opacity: 0.7
    })
    const cylinderMesh = new THREE.Mesh(cylinderGeometry, cylinderMaterial)
    cylinderMesh.position.y = ballRadius / 2

    // Create sphere for ball end
    const sphereGeometry = new THREE.SphereGeometry(ballRadius, 32, 16, 0, Math.PI * 2, 0, Math.PI / 2)
    const sphereMaterial = new THREE.MeshLambertMaterial({
      color: 0x999999,
      transparent: true,
      opacity: 0.7
    })
    const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial)
    sphereMesh.position.y = -(height - ballRadius) / 2

    // Combine into group
    const group = new THREE.Group()
    group.add(cylinderMesh)
    group.add(sphereMesh)

    // Convert group to mesh for consistency
    const combinedGeometry = new THREE.BufferGeometry()
    const material = new THREE.MeshLambertMaterial({
      color: 0x999999,
      transparent: true,
      opacity: 0.7
    })

    const mesh = new THREE.Mesh(combinedGeometry, material)
    mesh.userData = { tool, toolType: 'ballnose' }
    return mesh
  }

  private createRadialToolMesh(tool: RadialTool, height: number): THREE.Mesh {
    const radius = tool.diameter / 2
    const cornerRadius = tool.cornerRadius

    // Simplified as cylinder with rounded edges (complex geometry would need custom shape)
    const geometry = new THREE.CylinderGeometry(radius - cornerRadius, radius, height, 32)
    const material = new THREE.MeshLambertMaterial({
      color: 0x777777,
      transparent: true,
      opacity: 0.7
    })

    const mesh = new THREE.Mesh(geometry, material)
    mesh.userData = { tool, toolType: 'radial' }
    return mesh
  }

  private createSpecialToolMesh(tool: SpecialTool, height: number): THREE.Mesh {
    // Default to cylindrical shape for special tools
    const radius = tool.diameter / 2
    const geometry = new THREE.CylinderGeometry(radius, radius, height, 32)
    const material = new THREE.MeshLambertMaterial({
      color: 0x555555,
      transparent: true,
      opacity: 0.7
    })

    const mesh = new THREE.Mesh(geometry, material)
    mesh.userData = { tool, toolType: 'special' }
    return mesh
  }

  // Removed CSG-specific tool mesh creation methods

  // Helper methods for tool mesh creation
  private getToolColor(shape: string): number {
    const colors = {
      cylindrical: 0x888888,
      conical: 0x666666,
      ballnose: 0x999999,
      radial: 0x777777,
      special: 0x555555
    }
    return colors[shape as keyof typeof colors] || 0x888888
  }

  private getToolProfile(tool: CNCTool): THREE.Shape {
    const radius = tool.diameter / 2
    const shape = new THREE.Shape()

    switch (tool.shape) {
      case 'cylindrical':
        // Rectangular profile for cylindrical tools
        shape.moveTo(-radius, 0)
        shape.lineTo(radius, 0)
        shape.lineTo(radius, -radius * 0.1) // Small chamfer
        shape.lineTo(-radius, -radius * 0.1)
        shape.closePath()
        break

      case 'conical':
        const conicalTool = tool as ConicalTool
        const tipRadius = (conicalTool.tipDiameter || 0.1) / 2
        shape.moveTo(-tipRadius, 0)
        shape.lineTo(tipRadius, 0)
        shape.lineTo(radius, -radius)
        shape.lineTo(-radius, -radius)
        shape.closePath()
        break

      case 'ballnose':
        const ballTool = tool as BallnoseTool
        const ballRadius = ballTool.ballRadius
        // Simplified ball profile
        shape.moveTo(-radius, 0)
        shape.lineTo(radius, 0)
        shape.quadraticCurveTo(radius, -ballRadius / 2, 0, -ballRadius)
        shape.quadraticCurveTo(-radius, -ballRadius / 2, -radius, 0)
        break

      default:
        // Default cylindrical profile
        shape.moveTo(-radius, 0)
        shape.lineTo(radius, 0)
        shape.lineTo(radius, -radius * 0.1)
        shape.lineTo(-radius, -radius * 0.1)
        shape.closePath()
    }

    return shape
  }

  private createSweepGeometry(profile: THREE.Shape, path: THREE.Vector3[]): THREE.BufferGeometry {
    if (path.length < 2) {
      // Fallback to simple extrusion
      const extrudeSettings = {
        depth: 10,
        bevelEnabled: false
      }
      return new THREE.ExtrudeGeometry(profile, extrudeSettings)
    }

    // Create curve from path points
    const curve = new THREE.CatmullRomCurve3(path)

    // Create tube geometry along the curve
    const tubeGeometry = new THREE.TubeGeometry(curve, path.length * 2, profile.getPoints().length, 8, false)

    return tubeGeometry
  }

  // Create tool path geometry from draw commands
  public createToolPathFromCommands(commands: any[], tool: CNCTool, thickness: number = 5): THREE.Mesh[] {
    const meshes: THREE.Mesh[] = []
    const toolGeometry = this.createToolGeometry(tool, thickness)

    commands.forEach((command, index) => {
      if (command.command_type === 'line' || command.command_type === 'rectangle' || command.command_type === 'circle') {
        const toolMesh = toolGeometry.mesh.clone()

        // Position tool based on command coordinates (use 3D converted coordinates if available)
        if (command.command_type === 'line') {
          const midX = command.x1_3d !== undefined ? (command.x1_3d + command.x2_3d) / 2 : (command.x1 + command.x2) / 2
          const midZ = command.y1_3d !== undefined ? (command.y1_3d + command.y2_3d) / 2 : (command.y1 + command.y2) / 2
          // Position on the door surface - door is centered at Y=0, extends from +thickness/2 to -thickness/2
          const surfaceY = command.isBottomFace ? -thickness/2 : thickness/2  // Position on door surface
          toolMesh.position.set(midX, surfaceY, midZ)
          console.log(`🔧 Positioned line tool at (${midX.toFixed(1)}, ${surfaceY}, ${midZ.toFixed(1)}) - ${command.isBottomFace ? 'Bottom' : 'Top'} face`)
        } else if (command.command_type === 'rectangle') {
          const centerX = command.x1_3d !== undefined ? (command.x1_3d + command.x2_3d) / 2 : (command.x1 + command.x2) / 2
          const centerZ = command.y1_3d !== undefined ? (command.y1_3d + command.y2_3d) / 2 : (command.y1 + command.y2) / 2
          const surfaceY = command.isBottomFace ? -thickness/2 : thickness/2
          toolMesh.position.set(centerX, surfaceY, centerZ)
          console.log(`🔧 Positioned rectangle tool at (${centerX.toFixed(1)}, ${surfaceY}, ${centerZ.toFixed(1)}) - ${command.isBottomFace ? 'Bottom' : 'Top'} face`)
        } else if (command.command_type === 'circle') {
          const centerX = command.x1_3d !== undefined ? command.x1_3d : command.x1
          const centerZ = command.y1_3d !== undefined ? command.y1_3d : command.y1
          const surfaceY = command.isBottomFace ? -thickness/2 : thickness/2
          toolMesh.position.set(centerX, surfaceY, centerZ)
          console.log(`🔧 Positioned circle tool at (${centerX.toFixed(1)}, ${surfaceY}, ${centerZ.toFixed(1)}) - ${command.isBottomFace ? 'Bottom' : 'Top'} face`)
        }

        // Adjust Y position for tool depth - tools should penetrate into the door surface
        const depth = command.thickness || thickness
        // Door is positioned at Y = -thickness/2, so we need to account for that
        // Door surfaces: Top at Y = 0, Bottom at Y = -thickness
        // For top face operations, tools should be positioned to cut down from the top surface
        // For bottom face operations, tools should be positioned to cut up from the bottom surface
        if (command.isBottomFace) {
          // Position tool to cut from bottom surface (Y = -thickness) upward
          toolMesh.position.y = -thickness - depth/2  // Below bottom surface
        } else {
          // Position tool to cut from top surface (Y = 0) downward
          toolMesh.position.y = 0 - depth/2   // Above top surface, cutting down
        }

        toolMesh.userData = {
          command,
          tool,
          index,
          operation: 'subtract' // Default to subtraction for CNC operations
        }

        meshes.push(toolMesh)
      }
    })

    return meshes
  }

  // Enhanced tool path creation with operation-specific meshes
  public createOperationToolMeshes(commands: any[], tool: CNCTool, operation: string, depth: number = 5): THREE.Mesh[] {
    const meshes: THREE.Mesh[] = []

    commands.forEach((command, index) => {
      let toolMesh: THREE.Mesh | null = null

      switch (command.command_type) {
        case 'line':
          toolMesh = this.createLineToolMesh(command, tool, operation, depth)
          break
        case 'rectangle':
          toolMesh = this.createRectangleToolMesh(command, tool, operation, depth)
          break
        case 'circle':
          toolMesh = this.createCircleToolMesh(command, tool, operation, depth)
          break
        case 'arc':
          toolMesh = this.createArcToolMesh(command, tool, operation, depth)
          break
        default:
          return // Skip unsupported command types
      }

      if (toolMesh) {
        toolMesh.userData = {
          command,
          tool,
          operation,
          index,
          isOperationMesh: true
        }

        meshes.push(toolMesh)
      }
    })

    return meshes
  }

  // Create tool mesh for line operations
  private createLineToolMesh(command: any, tool: CNCTool, operation: string, depth: number): THREE.Mesh {
    // Use 3D coordinates if available, otherwise fall back to 2D
    const x1 = command.x1_3d !== undefined ? command.x1_3d : command.x1
    const y1 = command.y1_3d !== undefined ? command.y1_3d : command.y1
    const x2 = command.x2_3d !== undefined ? command.x2_3d : command.x2
    const y2 = command.y2_3d !== undefined ? command.y2_3d : command.y2

    // Create toolpath geometry that represents the actual material removal
    return this.createLineToolpath(x1, y1, x2, y2, tool, operation, depth, command.isBottomFace)
  }

  // Create continuous toolpath geometry for line operations
  private createLineToolpath(x1: number, y1: number, x2: number, y2: number, tool: CNCTool, operation: string, depth: number, isBottomFace: boolean): THREE.Mesh {
    const toolRadius = tool.diameter / 2
    const lineLength = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

    // Create a box geometry that represents the material removal path
    // Width = tool diameter, Length = line length, Height = cutting depth
    const geometry = new THREE.BoxGeometry(toolRadius * 2, depth, lineLength + toolRadius * 2)

    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)

    // Position the toolpath mesh
    const centerX = (x1 + x2) / 2
    const centerZ = (y1 + y2) / 2
    const doorThickness = 18
    // Door surfaces: Top at Y = 0, Bottom at Y = -doorThickness
    const surfaceY = isBottomFace ? -doorThickness - depth/2 : 0 - depth/2

    mesh.position.set(centerX, surfaceY, centerZ)

    // Rotate to align with line direction
    const direction = new THREE.Vector3(x2 - x1, 0, y2 - y1).normalize()
    const angle = Math.atan2(direction.x, direction.z)
    mesh.rotation.y = angle

    mesh.userData = {
      tool,
      operation,
      toolType: 'toolpath',
      pathType: 'line'
    }

    console.log(`🛤️ Created line toolpath: length=${lineLength.toFixed(1)}, width=${(toolRadius * 2).toFixed(1)}, depth=${depth}`)

    return mesh
  }

  // Create tool mesh for rectangle operations
  private createRectangleToolMesh(command: any, tool: CNCTool, operation: string, depth: number): THREE.Mesh {
    // Use 3D coordinates if available, otherwise fall back to 2D
    const x1 = command.x1_3d !== undefined ? command.x1_3d : command.x1
    const y1 = command.y1_3d !== undefined ? command.y1_3d : command.y1
    const x2 = command.x2_3d !== undefined ? command.x2_3d : command.x2
    const y2 = command.y2_3d !== undefined ? command.y2_3d : command.y2

    const centerX = (x1 + x2) / 2
    const centerZ = (y1 + y2) / 2
    const width = Math.abs(x2 - x1)
    const height = Math.abs(y2 - y1)
    const radius = command.radius || 0

    // Create toolpath geometry based on operation type
    if (operation === 'pocketing') {
      // For pocketing, create a toolpath that covers the entire rectangle area
      return this.createRectangleToolpath(width, height, centerX, centerZ, tool, operation, depth, command.isBottomFace, radius)
    } else if (operation === 'profiling') {
      // For profiling, create a toolpath that follows the rectangle perimeter
      return this.createRectangleProfileToolpath(width, height, centerX, centerZ, tool, depth, command.isBottomFace, radius)
    } else {
      // For other operations (roughing, finishing), create area removal toolpath
      return this.createRectangleToolpath(width, height, centerX, centerZ, tool, operation, depth, command.isBottomFace, radius)
    }
  }

  // Create toolpath geometry for rectangle operations
  private createRectangleToolpath(width: number, height: number, centerX: number, centerZ: number, tool: CNCTool, operation: string, depth: number, isBottomFace: boolean, radius: number = 0): THREE.Mesh {
    if (radius > 0) {
      // Create rectangle with circles at corners instead of rounded corners
      // For now, return a group containing the rectangle and corner circles
      const group = new THREE.Group()

      // Create the main rectangle body
      const rectGeometry = new THREE.BoxGeometry(width, depth, height)
      const material = new THREE.MeshLambertMaterial({
        color: this.getToolColor(tool.shape),
        transparent: true,
        opacity: 0.5,
        side: THREE.DoubleSide
      })

      const rectMesh = new THREE.Mesh(rectGeometry, material)
      group.add(rectMesh)

      // Create circles at each corner
      const cornerRadius = Math.min(radius, Math.abs(width) / 2, Math.abs(height) / 2)
      const circleGeometry = new THREE.CylinderGeometry(cornerRadius, cornerRadius, depth, 16)

      // Position circles at each corner
      const halfWidth = width / 2
      const halfHeight = height / 2

      // Top-left corner
      const topLeftCircle = new THREE.Mesh(circleGeometry, material.clone())
      topLeftCircle.position.set(-halfWidth + cornerRadius, 0, halfHeight - cornerRadius)
      group.add(topLeftCircle)

      // Top-right corner
      const topRightCircle = new THREE.Mesh(circleGeometry, material.clone())
      topRightCircle.position.set(halfWidth - cornerRadius, 0, halfHeight - cornerRadius)
      group.add(topRightCircle)

      // Bottom-left corner
      const bottomLeftCircle = new THREE.Mesh(circleGeometry, material.clone())
      bottomLeftCircle.position.set(-halfWidth + cornerRadius, 0, -halfHeight + cornerRadius)
      group.add(bottomLeftCircle)

      // Bottom-right corner
      const bottomRightCircle = new THREE.Mesh(circleGeometry, material.clone())
      bottomRightCircle.position.set(halfWidth - cornerRadius, 0, -halfHeight + cornerRadius)
      group.add(bottomRightCircle)

      const doorThickness = 18
      const surfaceY = isBottomFace ? -doorThickness/2 - depth/2 : doorThickness/2 - depth/2
      group.position.set(centerX, surfaceY, centerZ)

      group.userData = {
        tool,
        operation,
        toolType: 'toolpath',
        pathType: 'rectangle',
        hasCornerCircles: true,
        cornerRadius: radius
      }

      console.log(`🛤️ Created rectangle toolpath with corner circles: ${width.toFixed(1)}x${height.toFixed(1)}, depth=${depth}, operation=${operation}, corner radius=${radius.toFixed(1)}`)

      return group as any // Cast to THREE.Mesh for compatibility
    } else {
      // Create a simple box geometry for rectangles without radius
      const geometry = new THREE.BoxGeometry(width, depth, height)

      const material = new THREE.MeshLambertMaterial({
        color: this.getToolColor(tool.shape),
        transparent: true,
        opacity: 0.5,
        side: THREE.DoubleSide
      })

      const mesh = new THREE.Mesh(geometry, material)

      const doorThickness = 18
      // Door surfaces: Top at Y = 0, Bottom at Y = -doorThickness
      const surfaceY = isBottomFace ? -doorThickness - depth/2 : 0 - depth/2
      mesh.position.set(centerX, surfaceY, centerZ)

      mesh.userData = {
        tool,
        operation,
        toolType: 'toolpath',
        pathType: 'rectangle',
        hasCornerCircles: false,
        cornerRadius: 0
      }

      console.log(`🛤️ Created rectangle toolpath: ${width.toFixed(1)}x${height.toFixed(1)}, depth=${depth}, operation=${operation}`)

      return mesh
    }
  }

  // Create toolpath for rectangle profiling operations (follows perimeter)
  private createRectangleProfileToolpath(width: number, height: number, centerX: number, centerZ: number, tool: CNCTool, depth: number, isBottomFace: boolean, radius: number = 0): THREE.Mesh {
    const toolRadius = tool.diameter / 2
    const frameThickness = toolRadius * 2

    if (radius > 0) {
      // Create rectangle profile with circles at corners
      const group = new THREE.Group()

      const material = new THREE.MeshLambertMaterial({
        color: this.getToolColor(tool.shape),
        transparent: true,
        opacity: 0.6,
        side: THREE.DoubleSide
      })

      // Create the main frame geometry
      const outerGeometry = new THREE.BoxGeometry(width + frameThickness, depth, height + frameThickness)
      const frameMesh = new THREE.Mesh(outerGeometry, material)
      group.add(frameMesh)

      // Create circles at each corner for profiling
      const cornerRadius = Math.min(radius, Math.abs(width) / 2, Math.abs(height) / 2)
      const circleGeometry = new THREE.CylinderGeometry(cornerRadius + toolRadius, cornerRadius + toolRadius, depth, 16)

      // Position circles at each corner
      const halfWidth = width / 2
      const halfHeight = height / 2

      // Top-left corner
      const topLeftCircle = new THREE.Mesh(circleGeometry, material.clone())
      topLeftCircle.position.set(-halfWidth + cornerRadius, 0, halfHeight - cornerRadius)
      group.add(topLeftCircle)

      // Top-right corner
      const topRightCircle = new THREE.Mesh(circleGeometry, material.clone())
      topRightCircle.position.set(halfWidth - cornerRadius, 0, halfHeight - cornerRadius)
      group.add(topRightCircle)

      // Bottom-left corner
      const bottomLeftCircle = new THREE.Mesh(circleGeometry, material.clone())
      bottomLeftCircle.position.set(-halfWidth + cornerRadius, 0, -halfHeight + cornerRadius)
      group.add(bottomLeftCircle)

      // Bottom-right corner
      const bottomRightCircle = new THREE.Mesh(circleGeometry, material.clone())
      bottomRightCircle.position.set(halfWidth - cornerRadius, 0, -halfHeight + cornerRadius)
      group.add(bottomRightCircle)

      const doorThickness = 18
      const surfaceY = isBottomFace ? -doorThickness/2 - depth/2 : doorThickness/2 - depth/2
      group.position.set(centerX, surfaceY, centerZ)

      group.userData = {
        tool,
        operation: 'profiling',
        toolType: 'toolpath',
        pathType: 'profile',
        hasCornerCircles: true,
        cornerRadius: radius
      }

      console.log(`🛤️ Created rectangle profile toolpath with corner circles: ${width.toFixed(1)}x${height.toFixed(1)}, frame thickness=${frameThickness.toFixed(1)}, corner radius=${radius.toFixed(1)}`)

      return group as any // Cast to THREE.Mesh for compatibility
    } else {
      // Create a simple rectangular frame geometry for profiling
      const outerGeometry = new THREE.BoxGeometry(width + frameThickness, depth, height + frameThickness)

      const material = new THREE.MeshLambertMaterial({
        color: this.getToolColor(tool.shape),
        transparent: true,
        opacity: 0.6,
        side: THREE.DoubleSide
      })

      const mesh = new THREE.Mesh(outerGeometry, material)

      const doorThickness = 18
      const surfaceY = isBottomFace ? -doorThickness/2 - depth/2 : doorThickness/2 - depth/2
      mesh.position.set(centerX, surfaceY, centerZ)

      mesh.userData = {
        tool,
        operation: 'profiling',
        toolType: 'toolpath',
        pathType: 'profile',
        hasCornerCircles: false,
        cornerRadius: 0
      }

      console.log(`🛤️ Created rectangle profile toolpath: ${width.toFixed(1)}x${height.toFixed(1)}, frame thickness=${frameThickness.toFixed(1)}`)

      return mesh
    }
  }

  // Create tool mesh for circle operations
  private createCircleToolMesh(command: any, tool: CNCTool, operation: string, depth: number): THREE.Mesh {
    // Use 3D coordinates if available, otherwise fall back to 2D
    const centerX = command.x1_3d !== undefined ? command.x1_3d : command.x1
    const centerZ = command.y1_3d !== undefined ? command.y1_3d : command.y1
    const radius = command.radius

    // Create toolpath geometry based on operation type
    if (operation === 'drilling') {
      // For drilling, create a cylindrical toolpath at the circle center
      return this.createCircleDrillToolpath(centerX, centerZ, radius, tool, depth, command.isBottomFace)
    } else if (operation === 'pocketing') {
      // For circular pocketing, create a cylindrical pocket toolpath
      return this.createCirclePocketToolpath(centerX, centerZ, radius, tool, depth, command.isBottomFace)
    } else if (operation === 'profiling') {
      // For profiling, create a torus-like toolpath following the circle
      return this.createCircleProfileToolpath(centerX, centerZ, radius, tool, depth, command.isBottomFace)
    } else {
      // For other operations, create a circular area toolpath
      return this.createCirclePocketToolpath(centerX, centerZ, radius, tool, depth, command.isBottomFace)
    }
  }

  // Create toolpath for circle drilling operations
  private createCircleDrillToolpath(centerX: number, centerZ: number, radius: number, tool: CNCTool, depth: number, isBottomFace: boolean): THREE.Mesh {
    // Create a cylindrical geometry that represents the drill hole
    const geometry = new THREE.CylinderGeometry(radius, radius, depth, 16)

    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.7,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)

    const doorThickness = 18
    // Door surfaces: Top at Y = 0, Bottom at Y = -doorThickness
    const surfaceY = isBottomFace ? -doorThickness - depth/2 : 0 - depth/2
    mesh.position.set(centerX, surfaceY, centerZ)

    mesh.userData = {
      tool,
      operation: 'drilling',
      toolType: 'toolpath',
      pathType: 'drill'
    }

    console.log(`🛤️ Created drill toolpath: radius=${radius.toFixed(1)}, depth=${depth}`)

    return mesh
  }

  // Create toolpath for circle pocketing operations
  private createCirclePocketToolpath(centerX: number, centerZ: number, radius: number, tool: CNCTool, depth: number, isBottomFace: boolean): THREE.Mesh {
    // Create a cylindrical geometry that represents the pocket area
    const geometry = new THREE.CylinderGeometry(radius, radius, depth, 32)

    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.5,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)

    const doorThickness = 18
    const surfaceY = isBottomFace ? -doorThickness/2 - depth/2 : doorThickness/2 - depth/2
    mesh.position.set(centerX, surfaceY, centerZ)

    mesh.userData = {
      tool,
      operation: 'pocketing',
      toolType: 'toolpath',
      pathType: 'pocket'
    }

    console.log(`🛤️ Created circular pocket toolpath: radius=${radius.toFixed(1)}, depth=${depth}`)

    return mesh
  }

  // Create toolpath for circle profiling operations
  private createCircleProfileToolpath(centerX: number, centerZ: number, radius: number, tool: CNCTool, depth: number, isBottomFace: boolean): THREE.Mesh {
    const toolRadius = tool.diameter / 2

    // Create a torus geometry that represents the profiling toolpath
    const geometry = new THREE.TorusGeometry(radius, toolRadius, 8, 32)

    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)

    const doorThickness = 18
    const surfaceY = isBottomFace ? -doorThickness/2 - depth/2 : doorThickness/2 - depth/2
    mesh.position.set(centerX, surfaceY, centerZ)
    mesh.rotation.x = Math.PI / 2 // Rotate to lie flat on the door surface

    mesh.userData = {
      tool,
      operation: 'profiling',
      toolType: 'toolpath',
      pathType: 'profile'
    }

    console.log(`🛤️ Created circular profile toolpath: radius=${radius.toFixed(1)}, tool radius=${toolRadius.toFixed(1)}`)

    return mesh
  }

  // Create toolpath for arc profiling operations
  private createArcProfileToolpath(centerX: number, centerZ: number, radius: number, startAngle: number, endAngle: number, tool: CNCTool, depth: number, isBottomFace: boolean): THREE.Mesh {
    const toolRadius = tool.diameter / 2

    // Create arc geometry using a custom shape
    const arcShape = new THREE.Shape()

    // Create the arc path
    arcShape.absarc(0, 0, radius, startAngle, endAngle, false)

    // Extrude the arc to create a 3D toolpath
    const extrudeSettings = {
      depth: toolRadius * 2,
      bevelEnabled: false,
      steps: 1
    }

    const geometry = new THREE.ExtrudeGeometry(arcShape, extrudeSettings)

    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)

    const doorThickness = 18
    const surfaceY = isBottomFace ? -doorThickness/2 - depth/2 : doorThickness/2 - depth/2
    mesh.position.set(centerX, surfaceY, centerZ)
    mesh.rotation.x = Math.PI / 2 // Rotate to lie flat on the door surface

    mesh.userData = {
      tool,
      operation: 'profiling',
      toolType: 'toolpath',
      pathType: 'arc-profile'
    }

    const angleDegrees = (endAngle - startAngle) * 180 / Math.PI
    console.log(`🛤️ Created arc profile toolpath: radius=${radius.toFixed(1)}, angle=${angleDegrees.toFixed(1)}°, tool radius=${toolRadius.toFixed(1)}`)

    return mesh
  }

  // Create toolpath for arc pocketing operations
  private createArcPocketToolpath(centerX: number, centerZ: number, radius: number, startAngle: number, endAngle: number, tool: CNCTool, depth: number, isBottomFace: boolean): THREE.Mesh {
    // Create a sector-shaped geometry for arc pocketing
    const geometry = new THREE.CylinderGeometry(radius, radius, depth, 32, 1, false, startAngle, endAngle - startAngle)

    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.5,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)

    const doorThickness = 18
    const surfaceY = isBottomFace ? -doorThickness/2 - depth/2 : doorThickness/2 - depth/2
    mesh.position.set(centerX, surfaceY, centerZ)

    mesh.userData = {
      tool,
      operation: 'pocketing',
      toolType: 'toolpath',
      pathType: 'arc-pocket'
    }

    const angleDegrees = (endAngle - startAngle) * 180 / Math.PI
    console.log(`🛤️ Created arc pocket toolpath: radius=${radius.toFixed(1)}, angle=${angleDegrees.toFixed(1)}°, depth=${depth}`)

    return mesh
  }

  // Create tool mesh for arc operations
  private createArcToolMesh(command: any, tool: CNCTool, operation: string, depth: number): THREE.Mesh {
    // Use 3D coordinates if available, otherwise fall back to 2D
    const centerX = command.x1_3d !== undefined ? command.x1_3d : command.x1
    const centerZ = command.y1_3d !== undefined ? command.y1_3d : command.y1
    const radius = command.radius

    // Convert angles from degrees to radians and handle our field names
    const startAngleDeg = command.start_angle || 0
    const endAngleDeg = command.end_angle || 360
    const startAngleRad = startAngleDeg * Math.PI / 180
    const endAngleRad = endAngleDeg * Math.PI / 180

    console.log(`🛤️ Creating arc toolpath: center=(${centerX.toFixed(1)}, ${centerZ.toFixed(1)}), radius=${radius.toFixed(1)}, angles=${startAngleDeg}° to ${endAngleDeg}°`)

    // Create toolpath geometry based on operation type
    if (operation === 'profiling' || operation === 'finishing') {
      return this.createArcProfileToolpath(centerX, centerZ, radius, startAngleRad, endAngleRad, tool, depth, command.isBottomFace)
    } else if (operation === 'pocketing') {
      // For arc pocketing, create a sector-shaped toolpath
      return this.createArcPocketToolpath(centerX, centerZ, radius, startAngleRad, endAngleRad, tool, depth, command.isBottomFace)
    } else {
      // For other operations, create a general arc toolpath
      return this.createArcProfileToolpath(centerX, centerZ, radius, startAngleRad, endAngleRad, tool, depth, command.isBottomFace)
    }
  }

  // Create pocketing mesh for rectangular areas (currently unused)
  /*
  private _createPocketingMesh(tool: CNCTool, width: number, height: number, depth: number, centerX: number, centerZ: number): THREE.Mesh {
    // Create a box geometry that represents the material to be removed
    const geometry = new THREE.BoxGeometry(width, depth, height)
    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)
    mesh.position.set(centerX, -depth / 2, centerZ)
    mesh.userData = { tool, toolType: 'pocketing' }
    return mesh
  }
  */

  // Create circular pocket mesh (currently unused)
  /*
  private _createCircularPocketMesh(tool: CNCTool, radius: number, depth: number, centerX: number, centerZ: number): THREE.Mesh {
    const geometry = new THREE.CylinderGeometry(radius, radius, depth, 32)
    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.6,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)
    mesh.position.set(centerX, -depth / 2, centerZ)
    mesh.userData = { tool, toolType: 'circular-pocket' }
    return mesh
  }
  */

  // Create circular profile mesh (torus-like for profiling operations) (currently unused)
  /*
  private _createCircularProfileMesh(tool: CNCTool, radius: number, depth: number, centerX: number, centerZ: number): THREE.Mesh {
    const toolRadius = tool.diameter / 2
    const geometry = new THREE.TorusGeometry(radius, toolRadius, 8, 32)
    const material = new THREE.MeshLambertMaterial({
      color: this.getToolColor(tool.shape),
      transparent: true,
      opacity: 0.7,
      side: THREE.DoubleSide
    })

    const mesh = new THREE.Mesh(geometry, material)
    mesh.position.set(centerX, -depth / 2, centerZ)
    mesh.rotation.x = Math.PI / 2 // Rotate to lie flat
    mesh.userData = { tool, toolType: 'circular-profile' }
    return mesh
  }
  */

  // Generate path points for arc operations (currently unused)
  /*
  private _generateArcPath(centerX: number, centerY: number, radius: number, startAngle: number, endAngle: number): THREE.Vector3[] {
    const points: THREE.Vector3[] = []
    const segments = Math.max(8, Math.floor(Math.abs(endAngle - startAngle) * radius / 5)) // Adaptive segments

    for (let i = 0; i <= segments; i++) {
      const angle = startAngle + (endAngle - startAngle) * (i / segments)
      const x = centerX + radius * Math.cos(angle)
      const y = centerY + radius * Math.sin(angle)
      points.push(new THREE.Vector3(x, 0, y))
    }

    return points
  }
  */

  // Tool mesh caching for performance optimization
  private toolMeshCache = new Map<string, THREE.Mesh>()

  public getCachedToolMesh(tool: CNCTool, operation: string, depth: number): THREE.Mesh | null {
    const cacheKey = `${tool.id}-${operation}-${depth}-${tool.diameter}`
    return this.toolMeshCache.get(cacheKey) || null
  }

  public cacheToolMesh(tool: CNCTool, operation: string, depth: number, mesh: THREE.Mesh): void {
    const cacheKey = `${tool.id}-${operation}-${depth}-${tool.diameter}`
    this.toolMeshCache.set(cacheKey, mesh.clone())
  }

  public clearToolMeshCache(): void {
    this.toolMeshCache.clear()
  }

  // Batch tool mesh creation for multiple operations
  public createBatchToolMeshes(operations: Array<{
    commands: any[]
    tool: CNCTool
    operation: string
    depth: number
  }>): THREE.Mesh[] {
    const allMeshes: THREE.Mesh[] = []

    operations.forEach(({ commands, tool, operation, depth }) => {
      const meshes = this.createOperationToolMeshes(commands, tool, operation, depth)
      allMeshes.push(...meshes)
    })

    return allMeshes
  }

  // Removed CSG optimization methods

  // Removed CSG optimization and validation methods

  // Get tool mesh statistics for debugging
  public getToolMeshStats(mesh: THREE.Mesh): {
    vertices: number
    faces: number
    boundingBox: THREE.Box3 | null
    volume: number
  } {
    const geometry = mesh.geometry
    const positionAttribute = geometry.getAttribute('position')
    const vertices = positionAttribute ? positionAttribute.count : 0
    const faces = geometry.index ? geometry.index.count / 3 : vertices / 3

    geometry.computeBoundingBox()
    const boundingBox = geometry.boundingBox

    let volume = 0
    if (boundingBox) {
      const size = boundingBox.getSize(new THREE.Vector3())
      volume = size.x * size.y * size.z
    }

    return {
      vertices,
      faces,
      boundingBox,
      volume
    }
  }

  // Test method to verify tool geometry creation
  public testToolCreation(): void {
    console.log('Testing CNC tool geometry creation...')

    const testTools = [
      this.getToolById('cyl-6mm'),
      this.getToolById('con-90deg'),
      this.getToolById('ball-6mm')
    ]

    testTools.forEach(tool => {
      if (tool) {
        try {
          const geometry = this.createToolGeometry(tool, 10)
          console.log(`✓ Successfully created geometry for ${tool.name}:`, {
            toolType: tool.shape,
            diameter: tool.diameter,
            boundingBox: geometry.boundingBox
          })
        } catch (error) {
          console.error(`✗ Failed to create geometry for ${tool.name}:`, error)
        }
      }
    })
  }

  // Removed CSG test methods

  // Create realistic toolpath for CSG testing
  public createTestToolpath(): any[] {
    return [
      // Drilling operations
      { command_type: 'circle', x1: 50, y1: 50, x2: 0, y2: 0, radius: 3, color: '#ff0000', size: 1, text: '', layer_name: 'drilling_6mm', thickness: 10 },
      { command_type: 'circle', x1: 150, y1: 50, x2: 0, y2: 0, radius: 3, color: '#ff0000', size: 1, text: '', layer_name: 'drilling_6mm', thickness: 10 },

      // Roughing operations
      { command_type: 'rectangle', x1: 20, y1: 20, x2: 80, y2: 80, radius: 0, color: '#00ff00', size: 1, text: '', layer_name: 'roughing_8mm', thickness: 5 },
      { command_type: 'rectangle', x1: 120, y1: 20, x2: 180, y2: 80, radius: 0, color: '#00ff00', size: 1, text: '', layer_name: 'roughing_8mm', thickness: 5 },

      // Pocketing operations
      { command_type: 'rectangle', x1: 30, y1: 30, x2: 70, y2: 70, radius: 0, color: '#0000ff', size: 1, text: '', layer_name: 'pocketing_6mm', thickness: 8 },

      // Profiling operations
      { command_type: 'line', x1: 10, y1: 10, x2: 190, y2: 10, radius: 0, color: '#ff00ff', size: 1, text: '', layer_name: 'profiling_4mm', thickness: 3 },
      { command_type: 'line', x1: 10, y1: 90, x2: 190, y2: 90, radius: 0, color: '#ff00ff', size: 1, text: '', layer_name: 'profiling_4mm', thickness: 3 },

      // Finishing operations
      { command_type: 'circle', x1: 100, y1: 50, x2: 0, y2: 0, radius: 15, color: '#ffff00', size: 1, text: '', layer_name: 'finishing_2mm', thickness: 1 }
    ]
  }

  // Get appropriate tool for layer
  public getToolForLayer(layerName: string): CNCTool | null {
    const layer = layerName.toLowerCase()

    // Extract diameter from layer name
    const diameterMatch = layer.match(/(\d+)mm/)
    if (diameterMatch) {
      const diameter = parseInt(diameterMatch[1])
      return this.getToolById(`cyl-${diameter}mm`) || this.getToolById('cyl-6mm') || null
    }

    // Default tools based on operation type
    if (layer.includes('drilling') || layer.includes('delme')) {
      return this.getToolById('cyl-6mm') || null
    }
    if (layer.includes('roughing') || layer.includes('kaba')) {
      return this.getToolById('cyl-8mm') || null
    }
    if (layer.includes('finishing') || layer.includes('son')) {
      return this.getToolById('cyl-2mm') || this.getToolById('ball-2mm') || null
    }
    if (layer.includes('profiling') || layer.includes('profil')) {
      return this.getToolById('cyl-4mm') || null
    }
    if (layer.includes('pocketing') || layer.includes('cep')) {
      return this.getToolById('cyl-6mm') || null
    }

    return this.getToolById('cyl-6mm') || null // Default tool
  }

  // Parse layer data for OC.js operations
  public parseLayerForOCJS(layerName: string, commands: DrawCommand[]): {
    tool: CNCTool | null
    operation: string
    depth: number
    face: 'top' | 'bottom'
  } {
    const tool = this.detectToolFromLayerName(layerName)
    const operation = this.detectOperationFromLayer(layerName)
    const face = this.determineFaceFromLayer(layerName)

    // Extract depth from commands or use default based on tool
    let depth = 5 // Default depth
    const commandWithThickness = commands.find(cmd => cmd.thickness !== undefined && cmd.thickness !== null)
    if (commandWithThickness?.thickness) {
      depth = Math.abs(commandWithThickness.thickness)
    } else if (tool) {
      // Use tool-specific default depths
      switch (tool.shape) {
        case 'cylindrical':
          depth = tool.diameter * 0.5 // Half diameter depth
          break
        case 'conical':
          depth = tool.diameter * 0.3 // Shallower for V-bits
          break
        case 'ballnose':
          depth = tool.diameter * 0.4 // Medium depth for ballnose
          break
        default:
          depth = 5
      }
    }

    return {
      tool,
      operation,
      depth,
      face
    }
  }

  // Group commands by layer and parse for OC.js
  public parseCommandsForOCJS(commands: DrawCommand[]): {
    panelCommands: DrawCommand[]
    topTools: { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[]
    bottomTools: { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[]
  } {
    const layerGroups = new Map<string, DrawCommand[]>()
    const result = {
      panelCommands: [] as DrawCommand[],
      topTools: [] as { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[],
      bottomTools: [] as { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[]
    }

    // Group commands by layer
    commands.forEach(cmd => {
      const layerName = cmd.layer_name || 'default'
      if (!layerGroups.has(layerName)) {
        layerGroups.set(layerName, [])
      }
      layerGroups.get(layerName)!.push(cmd)
    })

    // Process each layer
    layerGroups.forEach((layerCommands, layerName) => {
      if (layerName === 'PANEL') {
        // PANEL layer defines the door body
        result.panelCommands = layerCommands
      } else if (!this.shouldExcludeLayerFromMachining(layerName)) {
        // Parse tool layer
        const parsed = this.parseLayerForOCJS(layerName, layerCommands)

        if (parsed.tool) {
          const toolData = {
            tool: parsed.tool,
            commands: layerCommands,
            depth: parsed.depth,
            operation: parsed.operation
          }

          if (parsed.face === 'bottom') {
            result.bottomTools.push(toolData)
          } else {
            result.topTools.push(toolData)
          }
        }
      }
    })

    return result
  }

  // Check if layer should be excluded from machining operations
  private shouldExcludeLayerFromMachining(layerName: string): boolean {
    const excludedPatterns = [
      'LUA', 'LMM', 'CLEANCORNERS', 'CLEANUP', 'DEEPEND',
      'DEEPFRAME', 'THINFRAME', 'V120PENCERE', 'VIOLIN', '_TN_'
    ]

    const layer = layerName.toUpperCase()
    return excludedPatterns.some(pattern => layer.includes(pattern))
  }

  // Determine face from layer name
  private determineFaceFromLayer(layerName: string): 'top' | 'bottom' {
    const layer = layerName.toLowerCase()

    // Check for explicit face indicators
    if (layer.includes('_sf') || layer.includes('_bottom') || layer.includes('alt')) {
      return 'bottom'
    }

    // Default to top face
    return 'top'
  }

  // Detect operation type from layer name
  private detectOperationFromLayer(layerName: string): string {
    const layer = layerName.toLowerCase()

    if (layer.includes('drill') || layer.includes('delme')) {
      return 'drilling'
    } else if (layer.includes('pocket') || layer.includes('cep')) {
      return 'pocketing'
    } else if (layer.includes('k_') || layer.includes('groove')) {
      return 'grooving'
    } else if (layer.includes('h_') || layer.includes('contour')) {
      return 'profiling'
    } else if (layer.includes('v') && (layer.includes('acili') || layer.includes('chamfer'))) {
      return 'chamfering'
    } else if (layer.includes('radius') || layer.includes('fillet')) {
      return 'filleting'
    }

    return 'profiling' // Default operation
  }
}

export const cncToolService = CNCToolService.getInstance()

// Removed CSG auto-test
