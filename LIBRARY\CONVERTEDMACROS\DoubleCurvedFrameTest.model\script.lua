-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script - Double Curved Frame Test
-- Converted from C# azCAM macro: Double Curved Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Double Curved Frame parameters (from original C# macro)
  A = 50    -- Side margin
  B = 150   -- Bottom curve height
  C = 50    -- Top/bottom margin
  D = 100   -- Top curve height
  E = 50    -- Curve transition width
  Z = 5     -- Thickness/depth
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = 2 * A + 2 * E + 50   -- minimum required width
  local minHeight = math.max(B, D) + 2 * C + 50  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Double Curved Frame function (converted from C# macro)
  local function double_curved_frame(A, B, C, D, E, Z)
    -- Use default values if parameters not provided
    A = A or 50
    B = B or 150
    C = C or 50
    D = D or 100
    E = E or 50
    Z = Z or -5
    
    print(string.format("Creating double curved frame"))
    print(string.format("Parameters: A=%d, B=%d, C=%d, D=%d, E=%d, Z=%d", A, B, C, D, E, -Z))
    
    -- Calculate all points (following C# logic exactly)
    local p1 = {A, height - C}                    -- Top-left start
    local p2 = {A + E, height - C}                -- Top curve start
    local p3 = {width / 2, height - D}            -- Top curve peak
    local p4 = {width - A - E, height - C}        -- Top curve end
    local p5 = {width - A, height - C}            -- Top-right
    local p6 = {width - A, C}                     -- Bottom-right
    local p7 = {width - A - E, C}                 -- Bottom curve start
    local p8 = {width / 2, B}                     -- Bottom curve peak
    local p9 = {A + E, C}                         -- Bottom curve end
    local p10 = {A, C}                            -- Bottom-left
    
    -- Validate curve peaks are within bounds
    if p3[2] < 0 or p3[2] > height then
      print(string.format("Warning: Top curve peak (%.1f) is outside panel height (0-%.1f)", p3[2], height))
    end
    
    if p8[2] < 0 or p8[2] > height then
      print(string.format("Warning: Bottom curve peak (%.1f) is outside panel height (0-%.1f)", p8[2], height))
    end
    
    -- Set layer and thickness for frame
    G.setLayer("Default")  -- Using original layer name from parameters.json
    G.setThickness(Z)
    
    -- Create the double curved frame using polyline with bulges for arcs
    -- Calculate bulge values for the arcs
    -- For 3-point arcs, we need to calculate the appropriate bulge
    
    -- Create the frame as a continuous polyline with bulges at arc sections
    -- We'll approximate the arcs using multiple segments with bulges
    
    -- For now, let's create a simplified version using straight line approximations
    -- and then enhance with proper arc calculations
    
    -- Create points for the frame outline
    local framePoints = {}
    
    -- Add straight line segments and arc approximations
    table.insert(framePoints, p1)   -- Start point
    table.insert(framePoints, p2)   -- Line to curve start
    
    -- Top arc approximation (p2 -> p3 -> p4)
    -- Create intermediate points for smooth curve
    local topArcPoints = G.circularArc({(p2[1] + p4[1])/2, (p2[2] + p3[2])/2}, 
                                       math.abs(p3[2] - p2[2]) * 2, 8, 0, 180)
    for i, point in ipairs(topArcPoints) do
      -- Adjust points to match the desired arc shape
      local adjustedPoint = {
        p2[1] + (p4[1] - p2[1]) * (i - 1) / (#topArcPoints - 1),
        p2[2] + (p3[2] - p2[2]) * math.sin(math.pi * (i - 1) / (#topArcPoints - 1))
      }
      table.insert(framePoints, adjustedPoint)
    end
    
    table.insert(framePoints, p4)   -- Arc end
    table.insert(framePoints, p5)   -- Line segment
    table.insert(framePoints, p6)   -- Line segment
    table.insert(framePoints, p7)   -- Line to curve start
    
    -- Bottom arc approximation (p7 -> p8 -> p9)
    local bottomArcPoints = G.circularArc({(p7[1] + p9[1])/2, (p7[2] + p8[2])/2}, 
                                          math.abs(p8[2] - p7[2]) * 2, 8, 180, 360)
    for i, point in ipairs(bottomArcPoints) do
      -- Adjust points to match the desired arc shape
      local adjustedPoint = {
        p7[1] + (p9[1] - p7[1]) * (i - 1) / (#bottomArcPoints - 1),
        p7[2] + (p8[2] - p7[2]) * math.sin(math.pi * (i - 1) / (#bottomArcPoints - 1))
      }
      table.insert(framePoints, adjustedPoint)
    end
    
    table.insert(framePoints, p9)   -- Arc end
    table.insert(framePoints, p10)  -- Line segment
    table.insert(framePoints, p1)   -- Close the shape
    
    -- Create the frame using polyline
    G.polyline(table.unpack(framePoints))
    
    print(string.format("Frame key points:"))
    print(string.format("  Top curve: (%.1f,%.1f) -> (%.1f,%.1f) -> (%.1f,%.1f)", 
          p2[1], p2[2], p3[1], p3[2], p4[1], p4[2]))
    print(string.format("  Bottom curve: (%.1f,%.1f) -> (%.1f,%.1f) -> (%.1f,%.1f)", 
          p7[1], p7[2], p8[1], p8[2], p9[1], p9[2]))
    
    return true
  end
  
  -- Call the double curved frame function
  local success = double_curved_frame(A, B, C, D, E, -Z)
  
  if success then
    print(string.format("Double curved frame created with parameters:"))
    print(string.format("  A (side margin): %d mm", A))
    print(string.format("  B (bottom curve height): %d mm", B))
    print(string.format("  C (top/bottom margin): %d mm", C))
    print(string.format("  D (top curve height): %d mm", D))
    print(string.format("  E (curve transition width): %d mm", E))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a frame with two curved sections")
    print("  - Top curve arches upward from the frame")
    print("  - Bottom curve arches downward into the frame")
    print("  - Connected by straight line segments")
  end
  
  return true
end

require "ADekoDebugMode"
