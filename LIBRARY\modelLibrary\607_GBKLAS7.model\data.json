{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Acili_aV\t\tAcili V bicagi \nK_Kanal\t\t kanal islemi icin kullanilacak bir bicak \nK_Freze_cW_mm\t\tcW Capli Freze bicagi \nK_Freze_cT_mm\t\tcT Capli Freze bicagi \nPANEL\t\tEbatlama bicagi \n----------------------------------------------------------------------------------------- \n Makro icindeki parametrelerden Gobek iptal edilebilir veya gobekteki acili bicak desen takimi ile degistirilebilir. \nK_Desen\t\tDesen-motif bicagi \t*varsa Gobek Ic kenar islemi icin* ", "modelParameters": [{"defaultValue": 150, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 0, "description": "Acili V kenardan DIS kanala mesafe", "parameterName": "b"}, {"defaultValue": 20, "description": "Acili V kenardan gobege mesafe", "parameterName": "h"}, {"defaultValue": 7, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 90, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 6, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 60, "description": "Ic kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 15, "description": "Ic kanallar kisa arasi mesafe", "parameterName": "gd"}, {"defaultValue": 0, "description": "<PERSON><PERSON><PERSON> ile kenar arasi kaydirma miktari", "parameterName": "sbt"}, {"defaultValue": 3, "description": "DIS kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}