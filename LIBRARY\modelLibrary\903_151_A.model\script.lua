-- <PERSON>ekoCA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  
  a = 10
  ad = 8
  b = 100
  d = 3
  
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})

  end
    
  local vWideAngle = 165
  local vWideDiameter = 300
  local cMidDiameter = 10
  local cThinDiameter = 5
  
  if (X>xMin and Y>yMin) then
    G.setLayer("K_AciliV45")
    G.setThickness(0)
    local p1 = {a, a}
    local p2 = {X-a, Y-a}
    local n1, n2 = G.sunkenFrame(p1, p2, ad, vWideAngle, vWideDiameter)
    n1[3] = 0
    n2[3] = 0
  end
  
  G.setLayer("K_Ballnose")
  G.setThickness(-d)
  if (X<xMin or Y<yMin) then
      b = 30
  end
  local p3, p4 = {b, b}, {X-b, Y-b}
  G.rectangle(p3, p4)
  
  return true
end

require "ADekoDebugMode"
