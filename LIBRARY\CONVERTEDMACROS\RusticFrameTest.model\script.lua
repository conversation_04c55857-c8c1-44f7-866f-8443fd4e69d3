-- ADeko<PERSON><PERSON>, Model Script - Rustic Frame Test
-- Converted from C# azCAM macro: Rustic Frame.macro
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  -- Set default values for testing if not provided
  X = X or 600
  Y = Y or 400
  materialThickness = materialThickness or 18
  
  -- Rustic Frame parameters (from original C# macro)
  A = 6     -- Left margin
  B = 6     -- Bottom margin
  C = 6     -- Right margin
  D = 6     -- Arc depth (how much the top arc curves down)
  E = 20    -- Top margin (height of straight sides)
  Z = 5     -- Thickness/depth
  
  -- Reference corner (from original parameters.json: reference = 3)
  -- 1=top-left, 2=top-center, 3=top-right, 4=middle-left, 5=center, 6=middle-right, 7=bottom-left, 8=bottom-center, 9=bottom-right
  referenceCorner = 3  -- top-right (original default)
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePart<PERSON>hape()
  
  if (G.parseModelParameters and modelParameters and G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  -- Use part dimensions (equivalent to width/height in C# macro)
  local width = X
  local height = Y
  
  -- Check minimum dimensions
  local minWidth = A + C + 50   -- minimum required width
  local minHeight = B + E + D + 20  -- minimum required height
  
  if width < minWidth or height < minHeight then
    print(string.format("Part dimension too small. Required: %dx%d, Current: %dx%d", 
          minWidth, minHeight, width, height))
    return true
  end
  
  -- Rustic Frame function (converted from C# macro)
  local function rustic_frame(A, B, C, D, E, Z, refCorner)
    -- Use default values if parameters not provided
    A = A or 6
    B = B or 6
    C = C or 6
    D = D or 6
    E = E or 20
    Z = Z or -5
    refCorner = refCorner or 3

    print(string.format("Creating rustic frame"))
    print(string.format("Parameters: A=%d, B=%d, C=%d, D=%d, E=%d, Z=%d", A, B, C, D, E, -Z))
    print(string.format("Reference corner: %d", refCorner))

    -- The C# macro uses the full panel dimensions directly, then applies reference transformation
    -- at the end via ScriptingFunctions.TransformEntitiesByReference
    -- For now, we'll create the geometry in panel coordinates (reference corner handling can be added later)

    -- Line points from C# macro (using panel coordinates directly):
    -- line0: (A, height-E) to (A, B)
    -- line1: (A, B) to (width-A, B)
    -- line2: (width-C, B) to (width-C, height-E)
    -- arc: (A, height-E) to (width/2, height-D) to (width-C, height-E)

    local line0Start = {A, height - E}
    local line0End = {A, B}

    local line1Start = {A, B}
    local line1End = {width - A, B}  -- This was wrong in my original - should be width-A, not width-C

    local line2Start = {width - C, B}
    local line2End = {width - C, height - E}

    -- Arc points
    local arcStart = {A, height - E}
    local arcPeak = {width / 2, height - D}
    local arcEnd = {width - C, height - E}

    -- Validate that the frame geometry makes sense
    if line1End[1] < line2Start[1] then
      print("Warning: Line1 end and Line2 start don't connect properly")
      print(string.format("Line1 ends at x=%.1f, Line2 starts at x=%.1f", line1End[1], line2Start[1]))
    end
    
    -- Set layer and thickness for frame
    G.setLayer("Default")  -- Using original layer name from parameters.json
    G.setThickness(Z)

    -- Create the rustic frame following the exact C# CompositeCurve structure
    -- The C# creates: CompositeCurve(line0, line1, line2, arc)
    -- We need to create a continuous path that follows this sequence

    local framePoints = {}

    -- Start with line0 (left vertical line): (A, height-E) to (A, B)
    table.insert(framePoints, line0Start)
    table.insert(framePoints, line0End)

    -- Continue with line1 (bottom horizontal line): (A, B) to (width-A, B)
    -- Note: line0End and line1Start are the same point, so we don't duplicate
    table.insert(framePoints, line1End)

    -- Add the gap/transition to line2 if needed
    -- line1 ends at (width-A, B) and line2 starts at (width-C, B)
    if line1End[1] ~= line2Start[1] then
      -- There's a gap - add a connecting line
      table.insert(framePoints, line2Start)
    end

    -- Continue with line2 (right vertical line): (width-C, B) to (width-C, height-E)
    table.insert(framePoints, line2End)

    -- Add arc approximation (top curved section): (A, height-E) to (width/2, height-D) to (width-C, height-E)
    -- Create arc approximation using multiple points
    local numArcPoints = 12
    for i = 1, numArcPoints do
      local t = (i - 1) / (numArcPoints - 1)  -- Parameter from 0 to 1

      -- Create a smooth arc from arcEnd to arcStart (going backwards to close the shape)
      local arcPoint = {
        arcEnd[1] + (arcStart[1] - arcEnd[1]) * t,
        arcEnd[2] + (arcPeak[2] - arcEnd[2]) * math.sin(math.pi * t)
      }
      table.insert(framePoints, arcPoint)
    end

    -- The arc should end at line0Start, completing the closed shape
    -- Create the frame using polyline
    G.polyline(table.unpack(framePoints))
    
    print(string.format("Rustic frame created:"))
    print(string.format("  Panel size: %.1f x %.1f mm", width, height))
    print(string.format("  Left line (line0): (%.1f,%.1f) to (%.1f,%.1f)",
          line0Start[1], line0Start[2], line0End[1], line0End[2]))
    print(string.format("  Bottom line (line1): (%.1f,%.1f) to (%.1f,%.1f)",
          line1Start[1], line1Start[2], line1End[1], line1End[2]))
    print(string.format("  Right line (line2): (%.1f,%.1f) to (%.1f,%.1f)",
          line2Start[1], line2Start[2], line2End[1], line2End[2]))
    print(string.format("  Top arc: (%.1f,%.1f) -> (%.1f,%.1f) -> (%.1f,%.1f)",
          arcStart[1], arcStart[2], arcPeak[1], arcPeak[2], arcEnd[1], arcEnd[2]))
    print(string.format("  Gap between line1 and line2: %.1f mm", math.abs(line2Start[1] - line1End[1])))
    
    return true
  end
  
  -- Call the rustic frame function
  local success = rustic_frame(A, B, C, D, E, -Z, referenceCorner)
  
  if success then
    local refNames = {
      [1] = "top-left", [2] = "top-center", [3] = "top-right",
      [4] = "middle-left", [5] = "center", [6] = "middle-right", 
      [7] = "bottom-left", [8] = "bottom-center", [9] = "bottom-right"
    }
    
    print(string.format("Rustic frame created with parameters:"))
    print(string.format("  Margins: A=%d, B=%d, C=%d mm", A, B, C))
    print(string.format("  Arc depth: D=%d mm", D))
    print(string.format("  Top margin: E=%d mm", E))
    print(string.format("  Reference corner: %d (%s)", referenceCorner, refNames[referenceCorner] or "unknown"))
    print(string.format("  Thickness: %d mm", Z))
    print(string.format("Panel size: %dx%d mm", X, Y))
    print("")
    print("Frame description:")
    print("  - Creates a rustic frame with curved top edge")
    print("  - Three straight lines (left, bottom, right)")
    print("  - One curved arc at the top")
    print("  - Organic, handcrafted appearance")
    print("")
    print("Applications:")
    print("  - Rustic furniture designs")
    print("  - Natural wood appearance")
    print("  - Handcrafted aesthetic")
    print("  - Traditional woodworking style")
  end
  
  return true
end

require "ADekoDebugMode"
