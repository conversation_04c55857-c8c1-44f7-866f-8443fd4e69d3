{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_Acili_aV \t\tAcili V bicagi \nK_Freze_cT_mm\t\t cT Capli Freze bicagi \nH_Raduslu_Pah_DIS\tRaduslu pah bicagi \nK_Kanal\t\t kanal islemi icin kullanilacak bir bicak  \nPANEL\t\tEbatlama bicagi", "modelParameters": [{"defaultValue": 150, "description": "X-Y-minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 30, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 0, "description": "DIS kanaldan acili V kenara mesafe", "parameterName": "b"}, {"defaultValue": 6, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 0, "description": "Gobek ic kanal arasi mesafe", "parameterName": "d"}, {"defaultValue": 90, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 50, "description": "Ic kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 0, "description": "<PERSON><PERSON><PERSON> ile gobek vbit arasi kaydirma miktari", "parameterName": "sbt"}, {"defaultValue": 6, "description": "Acili V ustu kanal bicak capi", "parameterName": "cT"}, {"defaultValue": 1, "description": "DIS kanal islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 1, "description": "Gobekte kanal cizgileri var mi? (Var :Derinlik/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 0, "description": "DIS kenarlarda pah islemi var mi? (Var :Derinlik/ Yok:0)", "parameterName": "extEdgeRtoolExist"}], "tags": [], "version": 1}