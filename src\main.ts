import { createApp } from 'vue'
import './style.css'
import './monaco-env' // Configure Monaco Editor environment before any Monaco imports
// Import Monaco Editor CSS
import 'monaco-editor/min/vs/editor/editor.main.css'
import App from './App.vue'
import i18n from './i18n'
// Initialize theme services
import { themeService } from './services/themeService'
import { colorfulThemeService } from './services/colorfulThemeService'
import { loadingService } from './services/loadingService'

// Initialize application with loading states
async function initializeApp() {
  try {
    // Ensure minimum loading time for better UX (prevents flash)
    await loadingService.addMinimumLoadTime(async () => {
      // Start initialization
      loadingService.startStep('init')

      // Initialize themes
      await loadingService.simulateAsyncOperation('themes', async () => {
        themeService.initialize()
        colorfulThemeService.initialize()
      }, 800)

      // Setup i18n
      await loadingService.simulateAsyncOperation('i18n', async () => {
        // i18n is already imported and ready
        await new Promise(resolve => setTimeout(resolve, 300))
      }, 500)

      // Initialize Monaco Editor environment
      await loadingService.simulateAsyncOperation('monaco', async () => {
        // Monaco environment is already configured
        await new Promise(resolve => setTimeout(resolve, 400))
      }, 1000)

      // Create and mount Vue app
      await loadingService.simulateAsyncOperation('editor', async () => {
        const app = createApp(App)
        app.use(i18n)
        app.mount('#app')
      }, 600)

      // Complete initialization
      loadingService.completeStep('complete')
    }, 2000) // Minimum 2 seconds to show the loader properly

  } catch (error) {
    console.error('Failed to initialize application:', error)
    loadingService.showError('Failed to initialize application')
  }
}

// Start the application
initializeApp()
