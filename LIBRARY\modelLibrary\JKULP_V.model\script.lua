-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  
  minimum = 150
  jk_side = 4				--(Sol-1,Alt-2,Sag-3,Ust-4,solsag-13,altust-24)
  
  a = 25			--<PERSON><PERSON><PERSON> mesafe
  b = 25				--<PERSON><PERSON><PERSON> mesafe
  
  j = 15
  k = 5					--Yaklasma
  l = 100					--U<PERSON>nluk
  transfer = 0 				-- l boyu girilen konumu kenarda diger tarafa koy
  ab_equal = 1				--l boyu ortada olsun istenirse 1 yapılır
  widIncrExist  = 0					--KAPAK BUYUTME VAR MI
  jNotchToolDia = 6		--<PERSON><PERSON>la malzeme kesen takım capı
  
  KOff = 30              -- <PERSON><PERSON><PERSON> Offset- icteki kanallarda kanaldan offset
  KOpr = 1              --<PERSON><PERSON><PERSON><PERSON>k islem 0-Bo<PERSON>,1-K_<PERSON><PERSON>, 2-K_Form
  KDepth = 18           -- Kanal Derinligi
  KToolNumber = 41 				-- Cap veya Vbit Acisi
  
  
  edgeCornerRExist          = 0   -- Kapak kose Radusu var mi? derinlik/0:yok
  
	
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
    
	if k < 0 then
      print(" k parametresi için pozitif bir değer girmeden bu makro çalışmaz")
      return true
	end
  
  
	if KOpr == 0 then 
		print(" KOpr parametresi için 1 veya 2 değeri girmeden bu makro çalışmaz")
		return true
	end
  
  local extWidth = k*2
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  local leftWidth = 0
  local rightWidth = 0
  local topWidth = 0
  local bottomWidth = 0
  local aSide = 0
  local bSide = 0
  local leftRight = false
  local bottomTop = false
  local aWider = false
  local bWider = false
  local aTank = 0
  local jNotchOffset = jNotchToolDia/2
 
  
	if widIncrExist >0 then											--BUYUTME YAPILACAKSA
    
    if a<j then
        print("*a* değeri j değerinden büyük olmalı")
        return true
    end
    if b<j then
        print("*b* değeri j değerinden büyük olmalı")
        return true
    end
		
    if a <= 0 and ab_equal ==0 then                              --Uzatma yapılacak uzunluk secimi -a-
			if k == 0 then						
				aSide = -2*a
			elseif k > 0 then
				aSide = -1*(a-k)
			end
			aWider = true
		end
		
		if b <= 0 and l==0 then                     --Uzatma yapılacak uzunluk secimi -b-
			if k == 0 then
				bSide = -2*b
			elseif k > 0 then
				bSide = -1*(b-k)
			end
			bWider = true
		end
		
		if transfer > 0 and aWider then     --uzatma yapılacak uzunluk switchi (a-b)
			aTank = aSide 
			aSide = bSide
			bSide = aTank
		end
		
		G.setLayer("K_JNotch")	
		G.setThickness(-materialThickness)
		if jk_side == 1 then               							 --parçayı büyüten kenara eklenecek çizgiler
			leftRight = true
			leftWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			elseif k>0 then
				G.line({-jNotchOffset,Y},{-jNotchOffset,0})	
			end
		elseif jk_side == 3 then
			leftRight = true
			rightWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			elseif k>0 then	
				G.line({X+jNotchOffset,0},{X+jNotchOffset,Y})
			end
		elseif jk_side == 13 then
			leftRight = true
			leftWidth = extWidth
			rightWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			elseif k>0 then
				G.line({-jNotchOffset,Y},{-jNotchOffset,0})	
				G.line({X+jNotchOffset,0},{X+jNotchOffset,Y})
			end
		elseif jk_side == 2 then
			bottomTop = true
			bottomWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			elseif k>0 then
				G.line({0,-jNotchOffset},{X,-jNotchOffset})
			end
		elseif jk_side == 4 then
			bottomTop = true
			topWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			elseif k>0 then
				G.line({X,Y+jNotchOffset},{0,Y+jNotchOffset})
			end
		elseif jk_side == 24 then
			bottomTop = true
			topWidth = extWidth
			bottomWidth = extWidth
			if aWider then
				G.rectangle({-jNotchOffset,-jNotchOffset},{X+jNotchOffset,Y+jNotchOffset})
			elseif k>0 then
				G.line({0,-jNotchOffset},{X,-jNotchOffset})
				G.line({X,Y+jNotchOffset},{0,Y+jNotchOffset})
			end
		end
	end
  
  ----dısardakiler
    
	local offset1 = KOff
	local operation1 = KOpr
	local ad1 = KDepth
	local W1 = KToolNumber
	local xx, yy, uzunluk, side1, side2, side3, side4, side5, side6, side7, side8 = 0
	
	if jk_side == 1 or jk_side == 3 or jk_side == 13 then	
		G.setThickness(-ad1)
		
		
		xx = X
		yy = Y
		uzunluk = yy-b
			
		if l > 0 then
			if 	ab_equal == 1 then
				a = (Y-l)/2
			end
			uzunluk = a+l
		end
		
		side1 = -k
		side2 = a-j
		side3 = offset1
		side4 = a
		side5 = offset1
		side6 = uzunluk
		side7 = -k
		side8= uzunluk+j
		points = {{side1,side2},{side3,side4},{side5,side6},{side7,side8}}
		
		if transfer == 0 then
			if jk_side == 13 then
				points1 = G.mirror(points, "x", X, Y)
			elseif jk_side == 3 then
				points = G.mirror(points, "x", X, Y)
			end
		elseif transfer == 1 then
			points = G.mirror(points, "y", X, Y)
			if jk_side == 13 then															--sol ve sağ kenarlada mı
				points1 = G.mirror(points, "x", X, Y)
			elseif jk_side == 3 then														--sag kenarda mı
				points = G.mirror(points, "x", X, Y)
			end
		
		end
		
	elseif jk_side == 4 or jk_side == 2 or jk_side == 24 then
		xx = Y
		yy = X
		uzunluk = yy-b
		
		if l > 0 then
			if 	ab_equal == 1 then
				a = (X-l)/2
			end
			uzunluk = a+l
		end
		side1 = a-j
		side2 = -k
		side3 = a
		side4 = offset1
		side5 = uzunluk
		side6 = offset1
		side7 = uzunluk+j
		side8 = -k
		
		points = {{side1,side2},{side3,side4},{side5,side6},{side7,side8}}
		
		if transfer == 0 then	
			points = {{side1,side2},{side3,side4},{side5,side6},{side7,side8}}
			if jk_side == 24 then
				points1 = G.mirror(points, "y", X, Y)
			elseif jk_side == 4 then														--ust kenarda mı
				points = G.mirror(points, "y", X, Y)
			end
		elseif transfer == 1 then
			points = {{side1,side2},{side3,side4},{side5,side6},{side7,side8}}
			points = G.mirror(points, "x", X, Y)
			if jk_side == 24 then															--ust ve alt 2 kenarda mı
				points1 = G.mirror(points, "y", X, Y)
			elseif jk_side == 4 then														--ust kenarda mı
				points = G.mirror(points, "y", X, Y)
			end
		end
		
	end
	
	-- if jk_side == 1 or jk_side == 3 or jk_side == 13 then								--sol sag kenarlarda mı
		--if transfer == 0 then
		--	if jk_side == 13 then
		--		points1 = G.mirror(points, "x", X, Y)
		--	elseif jk_side == 3 then
		--		points = G.mirror(points, "x", X, Y)
		--	end
		--elseif transfer == 1 then
		--	if jk_side == 13 then															--sol ve sağ kenarlada mı
		--		points1 = G.mirror(points, "x", X, Y)
		--	elseif jk_side == 3 then														--sag kenarda mı
		--		points = G.mirror(points, "x", X, Y)
		--	end
		--
		--end
	-- elseif jk_side == 4 or jk_side == 2 or jk_side == 24 then
		--if transfer == 0 then	
		--	if jk_side == 24 then
		--		points1 = G.mirror(points, "y", X, Y)
		--	elseif jk_side == 4 then														--ust kenarda mı
		--		points = G.mirror(points, "y", X, Y)
		--	end
		--elseif transfer == 1 then
		--	if jk_side == 24 then															--ust ve alt 2 kenarda mı
		--		points1 = G.mirror(points, "y", X, Y)
		--	elseif jk_side == 4 then														--ust kenarda mı
		--		points = G.mirror(points, "y", X, Y)
		--	end
		--end
	
	-- end
	
	if operation1 ~= 0 then
	
		if operation1 == 1 then
			G.setLayer("K_Kanal_TN_"..W1)
		end
		G.polylineimp(points)
		if jk_side == 24 or jk_side == 13 then
			G.polylineimp(points1)
		end
	end
	
	
		
  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---kose radusu yapsin mi
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},				
      {X-edgeCornerRExist,Y},                       
      {edgeCornerRExist,Y,0,bulge},                 
      {0,Y-edgeCornerRExist},                       
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  elseif widIncrExist>0 then
		if bottomTop then                     ---kanallar ustte ve altta
			G.makePartShape({-leftWidth-aSide,-bottomWidth,0},
			{X+rightWidth+bSide,-bottomWidth,0},         
			{X+rightWidth+bSide,Y+topWidth,0},           
			{-leftWidth-aSide,Y+topWidth,0},             
			{-leftWidth-aSide,-bottomWidth,0})
		end
		if leftRight then                       ---kanallar sag ve solda
			G.makePartShape({-leftWidth,-bottomWidth-aSide,0},
			{X+rightWidth,-bottomWidth-aSide,0},         
			{X+rightWidth,Y+topWidth+bSide,0},           
			{-leftWidth,Y+topWidth+bSide,0},             
			{-leftWidth,-bottomWidth-aSide,0})
		end
  else
    G.makePartShape()
  end
  

  if X<xMin or Y<yMin then
    print("Part dimension too small")
    return true
  end
  
  print("leftRight:"..tostring(leftRight))
  print("bottomTop:"..tostring(bottomTop))
  print("aWider:"..tostring(aWider))
  print("bWider:"..tostring(bWider))
  print("aTank:"..tostring(aTank))
  print("jNotchOffset:"..tostring(jNotchOffset))
  print("extWidth:"..extWidth)
  print("---------")
  print("aside:"..aSide)
  print("bSide:"..bSide)
  print("topWidth:"..topWidth)
  print("bottomWidth:"..bottomWidth)
  print("leftWidth:"..leftWidth)
  print("rightWidth:"..rightWidth)
  
  return true
end


require "ADekoDebugMode"
