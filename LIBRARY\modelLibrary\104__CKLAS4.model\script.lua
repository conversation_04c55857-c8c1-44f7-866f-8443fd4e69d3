-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
	minimum = 150
	limit = 350
	
  a = 50          -- Kenardan Vbite
  aa = 30		  -- Dar kapak için Kenardan mesafe
  ad = 6          -- Vbit derinliği (sunkenDepth)
  aV = 120        -- Vbit Açısı
  cW = 20          -- Açılı V üzerinde kanal bıçak çapı
  cT = 6          -- Açılı V üzerinde kanal bıçak çapı
  cR = 5		  -- Radus Veren form bıcak düz uç dar çapı
  
  extEdgeRtoolExist       	= 5   -- D<PERSON>ş kenar Pah işlemi var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- Kapak köşe Radüsü var mı? derinlik/0:yok
  topGrooveExist    		= 3   -- Açılı V üzerinde kanal var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
		return false
	end
    
  --local bd = 3    -- Acili V Ustu kanal derinliği
  -- local d = 10          -- Acili V den iç kanala
  --dd = 2          -- iç kanal derinliği
  local xMin = minimum
	local yMin = minimum
	local xLimit = limit
	local yLimit = limit
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < limit and Y < limit then 
		ust = aa
		a = aa
	elseif  Y < limit and X > limit then 
		ust = aa
	elseif X < limit and Y > limit  then
		a = aa
	end
  
  local D = edgeCornerRExist
      
	local B = math.tan(math.pi/8)
	G.setThickness(-materialThickness)
	G.setFace("top")
	if edgeCornerRExist >0 then
		G.makePartShape({D,0},		--chamfered part shape
		{X-D,0,0,B},
		{X,D},
		{X,Y-D,0,B},
		{X-D,Y},
		{D,Y,0,B},
		{0,Y-D},
		{0,D,0,B},
		{D,0})
	else
		G.makePartShape()
	end
	
	local vWideDiameter = 60
	local sunkenWidth = ad * math.tan((math.pi*aV/180)/2)sunkenWidth = ad * math.tan((math.pi*aV/180)/2)
	
	if X<xMin or Y<yMin then
		print("Part dimension too small")
		return true
	end
	
	if cW <= 0 then
		cW = cT
	end
	
	minXCalculated = 2*a + 2*sunkenWidth
	minYCalculated = 2*ust + 2*sunkenWidth
	
	if X<minXCalculated or Y<minYCalculated then
		print("Part dimension too small")
		return true
	end
  
	if topGrooveExist > 0 then
		--G.setLayer("K_Ballnose") 
		G.setLayer("K_Freze"..cT.."mm")  -- 
		G.setThickness(-topGrooveExist)
		distance = a + cT/2
		distance2 = ust + cT/2
		G.line({distance, 0}, {distance, Y})
		G.line({X-distance, 0}, {X-distance, Y})
		G.line({distance, distance2}, {X-distance, distance2})
		G.line({distance, Y-distance2}, {X-distance, Y-distance2})
	end
  
	G.setLayer("K_AciliV" .. aV )  -- V shaped deep with large angle 
	G.setThickness(0)
	local corner1, corner2 = G.sunkenFrame({a, ust}, {X-a, Y-ust}, ad, aV, vWideDiameter)
	corner1[3] = 0
	corner2[3] = 0
	
	G.setLayer("Cep_Acma")  -- 
	G.setThickness(-ad)
	distance = a+sunkenWidth
	distance2 = ust+sunkenWidth
	point1 = {distance, distance2}
	point2 = {X-distance, Y-distance2}
	G.rectangle(point1,point2)
  
	if extEdgeRtoolExist > 0 then
		G.setLayer("H_Raduslu_Pah_" .. "DIS")  -- 
		G.setThickness(-extEdgeRtoolExist)
		distance = 0
		point11 = {0-distance, 0-distance,0}
		point12 = {X+distance, Y+distance,0}
		G.rectangle(point11,point12)
	end
	
  	if cW ~= cT then          --- bu islemi sadece büyük takım ve küçük takım ile birlikte tarama da yaparsa yapacak
			G.setLayer("K_TarKoseTemizl"..cT.."mm")
			G.setThickness(0)
			G.cleanCorners(corner1,corner2,ad,cT)
	end
	
  return true
end

------------------------------------------------

require "ADekoDebugMode"
