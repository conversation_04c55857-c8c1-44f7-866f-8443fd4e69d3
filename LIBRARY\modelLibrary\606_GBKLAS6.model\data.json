{"modelNote": "Katmanlar:\t\tTakimlar: \n-----------------------------------------------------------------------------------------\nK_AciliV_aV\t\taV degiskeni ile acisi verilen V bicak \nK_Freze_cT_mm\t\tcT Capli Freze bicagi \nK_Freze_cW_mm\t\tcW Capli Freze bicagi \nK_Kanal\t\tKanal bicagi \nPANEL\t\tEbatlama bicagi\n----------------------------------------------------------------------------------------- \n Ma<PERSON>ro icindeki parametrelerden Gobek iptal edilebilir veya gobekteki acili bicak desen takimi ile degistirilebilir. \nK_Desen\t\tDesen-motif bicagi \nCep_Acma\t\tcW veya h degerinden kucuk bir capta Freze bicagi", "modelParameters": [{"defaultValue": 120, "description": "X-Y minimum kenar u<PERSON>u", "parameterName": "minimum"}, {"defaultValue": 250, "description": "X-Y icin kucuk kapak deseni limit degeri", "parameterName": "limit"}, {"defaultValue": 60, "description": "Kenardan acili V kenara mesafe", "parameterName": "a"}, {"defaultValue": 20, "description": "Dar ka<PERSON>klar icin kenardan acili V kenara mesafe", "parameterName": "aa"}, {"defaultValue": 0, "description": "Acili V kenardan DIS kanala mesafe", "parameterName": "b"}, {"defaultValue": 20, "description": "Acili V kenardan gobege mesafe", "parameterName": "h"}, {"defaultValue": 7, "description": "Acili V derinligi", "parameterName": "ad"}, {"defaultValue": 135, "description": "Acili V uc acisi", "parameterName": "aV"}, {"defaultValue": 20, "description": "Tarama bicak capi", "parameterName": "cW"}, {"defaultValue": 6, "description": "Ic kose finis bicak capi", "parameterName": "cT"}, {"defaultValue": 60, "description": "Ic kanallar arasi mesafe", "parameterName": "gaps"}, {"defaultValue": 0, "description": "<PERSON><PERSON><PERSON> ile kenar arasi kaydirma miktari", "parameterName": "sbt"}, {"defaultValue": 3, "description": "DIS kanal islemi var mi?(Var :Derinlik/ Yok:0)", "parameterName": "extGrooveExist"}, {"defaultValue": 2, "description": "Ic dikey kanal cizgileri var mi?(Var :Derinlik/ Yok:0)", "parameterName": "intGrooveExist"}, {"defaultValue": 0, "description": "DIS kenar kose radus var mi?(Var :Yaricap/ Yok:0)", "parameterName": "edgeCornerRExist"}], "tags": [], "version": 1}