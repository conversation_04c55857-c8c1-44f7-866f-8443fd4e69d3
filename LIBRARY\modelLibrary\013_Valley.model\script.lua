-- <PERSON><PERSON><PERSON>CA<PERSON>, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  a = 50
  b = 70
  arcWidth = 150
  local arcBulge = -1.0
  arcOffset = 50
  depth1 = 6
  depth2 = 3
  
  --if not check() then
  --  return false
  --end
  
  G = ADekoLib
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  G<PERSON>setLayer("THINFRAME")  -- V shape shallow (conical, 30 degree, 5mm diameter, 9.33mm length)
  G.setThickness(-depth2)
  G.rectangle({b, b}, {X-b, Y-b})
  G.line({b, b}, {0, 0})
  G.line({X-b, Y-b}, {X, Y})
  G.line({b, Y-b}, {0, Y})
  G.line({X-b, b}, {X, 0})
  
  G.setLayer("DEEPFRAME")  -- V shaped deep with large angle (conical 130 degree, 150mm diameter, 35mm length)
  G.setThickness(-depth1)
  G.rectangle({2*a, 2*a}, {X-2*a, Y-2*a})  -- sunken frame's edges
  local cornerDepth = 24
  G.line({2*a, 2*a}, {2*a-cornerDepth, 2*a-cornerDepth, depth1})  -- sunken frame's corners
  G.line({X-2*a, 2*a}, {X-2*a+cornerDepth, 2*a-cornerDepth, depth1})
  G.line({X-2*a, Y-2*a}, {X-2*a+cornerDepth, Y-2*a+cornerDepth, depth1})
  G.line({2*a, Y-2*a}, {2*a-cornerDepth, Y-2*a+cornerDepth, depth1})
  G.rectangle({2.5*a, 2.5*a}, {X-2.5*a, Y-2.5*a})  -- second V-shape tool
  
  G.setLayer("CLEANUP")  -- cylindirical, flat, 6mm diameter, 35mm length
  local halfTheDiameter = 3
  local rectSize = 2*halfTheDiameter
  G.setThickness(-depth1)
  G.rectangle({2*a+halfTheDiameter, 2*a+halfTheDiameter}, {2*a+halfTheDiameter+rectSize, 2*a+halfTheDiameter+rectSize})  -- corner cleanup
  G.rectangle({X-2*a-rectSize-halfTheDiameter, 2*a+halfTheDiameter}, {X-2*a-halfTheDiameter, 2*a+rectSize+halfTheDiameter})
  G.rectangle({X-2*a-halfTheDiameter, Y-2*a-halfTheDiameter}, {X-2*a-rectSize-halfTheDiameter, Y-2*a-rectSize-halfTheDiameter})
  G.rectangle({2*a+halfTheDiameter, Y-2*a-rectSize-halfTheDiameter}, {2*a+rectSize+halfTheDiameter, Y-2*a-halfTheDiameter})
  
  G.setLayer("DEEPEND")  -- cylindirical, 12mm diameter, 50mm length
  G.setThickness(-depth1)
  G.rectangle({2.39*a, 2.39*a}, {X-2.39*a, Y-2.39*a})
  G.rectangle({2.3*a, 2.3*a}, {X-2.3*a, Y-2.3*a})
  G.rectangle({2.12*a, 2.12*a}, {X-2.12*a, Y-2.12*a})
  
  G.setLayer("THINFRAME")  -- V shape shallow (conical, 30 degree, 5mm diameter, 9.33mm length)
  G.setThickness(-depth2)
  local c = 3.12
  G.line({c*a, c*a}, {(X-arcWidth)/2, c*a})
  if (Y>450) then
    G.line({(X-arcWidth)/2, c*a}, {(X-arcWidth)/2+arcWidth, c*a}, arcBulge)
  else
    G.line({(X-arcWidth)/2, c*a}, {(X-arcWidth)/2+arcWidth, c*a})
  end
  G.line({(X-arcWidth)/2+arcWidth, c*a}, {X-c*a, c*a})
  G.line({X-c*a, c*a}, {X-c*a, Y-c*a})
  G.line({X-c*a, Y-c*a}, {(X-arcWidth)/2+arcWidth, Y-c*a})
  if (Y>450) then
    G.line({(X-arcWidth)/2+arcWidth, Y-c*a}, {(X-arcWidth)/2, Y-c*a}, arcBulge)
  else
    G.line({(X-arcWidth)/2+arcWidth, Y-c*a}, {(X-arcWidth)/2, Y-c*a})
  end
  G.line({(X-arcWidth)/2, Y-c*a}, {c*a, Y-c*a})
  G.line({c*a, Y-c*a}, {c*a, c*a})
  
  G.setLayer("VIOLIN")  -- Sekiz yapraklı çiçekler
  G.setThickness(0)
  sekizYaprak(X/2, Y/3.7)
  sekizYaprak(X/2, Y-Y/3.7)
  
  return true
end

------------------------------------------------
function check()
  local minAllowedX = arcWidth + 2*a + 2*arcOffset
  local minAllowedY = 350
  if (X<minAllowedX or Y<minAllowedY) then
    G.error()
    return false
  end
  return true
end

------------------------------------------------
function sekizYaprak(xOffSet, yOffSet)
  G.polyline(
    {xOffSet+(0),	yOffSet+(5.2535),	0},
    {xOffSet+(0.6483),	yOffSet+(6.0142),	-0.2414},
    {xOffSet+(1.209),	yOffSet+(6.8417),	-0.4853},
    {xOffSet+(1.6751),	yOffSet+(7.7258),	-0.7247},
    {xOffSet+(2.041),	yOffSet+(8.6559),	-0.9534},
    {xOffSet+(2.3022),	yOffSet+(9.6207),	-1.1658},
    {xOffSet+(2.4557),	yOffSet+(10.6083),	-1.3577},
    {xOffSet+(2.4994),	yOffSet+(11.6069),	-1.5257},
    {xOffSet+(2.4329),	yOffSet+(12.6041),	-1.6677},
    {xOffSet+(2.257),	yOffSet+(13.588),	-1.7829},
    {xOffSet+(1.9738),	yOffSet+(14.5466),	-1.8714},
    {xOffSet+(1.5868),	yOffSet+(15.4681),	-1.9348},
    {xOffSet+(1.1007),	yOffSet+(16.3414),	-1.9751},
    {xOffSet+(0.5213),	yOffSet+(17.1558),	-1.9955},
    {xOffSet+(-0.1408),	yOffSet+(17.9048),	-1.9997},
    {xOffSet+(-0.7717),	yOffSet+(18.68),	-1.9893},
    {xOffSet+(-1.3135),	yOffSet+(19.5199),	-1.961},
    {xOffSet+(-1.7596),	yOffSet+(20.4143),	-1.9115},
    {xOffSet+(-2.1047),	yOffSet+(21.3524),	-1.838},
    {xOffSet+(-2.3444),	yOffSet+(22.3227),	-1.7386},
    {xOffSet+(-2.476),	yOffSet+(23.3135),	-1.6123},
    {xOffSet+(-2.5),	yOffSet+(24.2372),	-1.4719},
    {xOffSet+(-2.4093),	yOffSet+(25.3083),	-1.2812},
    {xOffSet+(-2.2113),	yOffSet+(26.288),	-1.0804},
    {xOffSet+(-1.9064),	yOffSet+(27.2398),	-0.8607},
    {xOffSet+(-1.4982),	yOffSet+(28.1521),	-0.6269},
    {xOffSet+(-0.9917),	yOffSet+(29.0138),	-0.385},
    {xOffSet+(-0.3932),	yOffSet+(29.8142),	-0.1415},
    {xOffSet+(0),	yOffSet+(30.2535),	0}
  )
  G.polyline(
    {xOffSet+(3.7148),	yOffSet+(3.7148),	0},
    {xOffSet+(4.7111),	yOffSet+(3.7942),	-0.2414},
    {xOffSet+(5.6927),	yOffSet+(3.9829),	-0.4853},
    {xOffSet+(6.6474),	yOffSet+(4.2785),	-0.7247},
    {xOffSet+(7.5639),	yOffSet+(4.6775),	-0.9534},
    {xOffSet+(8.4308),	yOffSet+(5.1749),	-1.1658},
    {xOffSet+(9.2376),	yOffSet+(5.7648),	-1.3577},
    {xOffSet+(9.9746),	yOffSet+(6.4399),	-1.5257},
    {xOffSet+(10.6328),	yOffSet+(7.1921),	-1.6677},
    {xOffSet+(11.2041),	yOffSet+(8.0122),	-1.7829},
    {xOffSet+(11.6817),	yOffSet+(8.8903),	-1.8714},
    {xOffSet+(12.0596),	yOffSet+(9.8155),	-1.9348},
    {xOffSet+(12.3334),	yOffSet+(10.7768),	-1.9751},
    {xOffSet+(12.4996),	yOffSet+(11.7624),	-1.9955},
    {xOffSet+(12.561),	yOffSet+(12.7602),	-1.9997},
    {xOffSet+(12.6631),	yOffSet+(13.7544),	-1.9893},
    {xOffSet+(12.8739),	yOffSet+(14.7314),	-1.961},
    {xOffSet+(13.1908),	yOffSet+(15.6793),	-1.9115},
    {xOffSet+(13.6102),	yOffSet+(16.5866),	-1.838},
    {xOffSet+(14.1267),	yOffSet+(17.4423),	-1.7386},
    {xOffSet+(14.7343),	yOffSet+(18.2359),	-1.6123},
    {xOffSet+(15.3706),	yOffSet+(18.9061),	-1.4719},
    {xOffSet+(16.192),	yOffSet+(19.5993),	-1.2812},
    {xOffSet+(17.0247),	yOffSet+(20.1521),	-1.0804},
    {xOffSet+(17.9134),	yOffSet+(20.6095),	-0.8607},
    {xOffSet+(18.8472),	yOffSet+(20.966),	-0.6269},
    {xOffSet+(19.8146),	yOffSet+(21.2171),	-0.385},
    {xOffSet+(20.8038),	yOffSet+(21.3599),	-0.1415},
    {xOffSet+(21.3925),	yOffSet+(21.3925),	0}
  )
  G.polyline(
    {xOffSet+(5.2535),	yOffSet+(0),	0},
    {xOffSet+(6.0142),	yOffSet+(-0.6483),	-0.2414},
    {xOffSet+(6.8417),	yOffSet+(-1.209),	-0.4853},
    {xOffSet+(7.7258),	yOffSet+(-1.6751),	-0.7247},
    {xOffSet+(8.6559),	yOffSet+(-2.041),	-0.9534},
    {xOffSet+(9.6207),	yOffSet+(-2.3022),	-1.1658},
    {xOffSet+(10.6083),	yOffSet+(-2.4557),	-1.3577},
    {xOffSet+(11.6069),	yOffSet+(-2.4994),	-1.5257},
    {xOffSet+(12.6041),	yOffSet+(-2.4329),	-1.6677},
    {xOffSet+(13.588),	yOffSet+(-2.257),	-1.7829},
    {xOffSet+(14.5466),	yOffSet+(-1.9738),	-1.8714},
    {xOffSet+(15.4681),	yOffSet+(-1.5868),	-1.9348},
    {xOffSet+(16.3414),	yOffSet+(-1.1007),	-1.9751},
    {xOffSet+(17.1558),	yOffSet+(-0.5213),	-1.9955},
    {xOffSet+(17.9048),	yOffSet+(0.1408),	-1.9997},
    {xOffSet+(18.68),	yOffSet+(0.7717),	-1.9893},
    {xOffSet+(19.5199),	yOffSet+(1.3135),	-1.961},
    {xOffSet+(20.4143),	yOffSet+(1.7596),	-1.9115},
    {xOffSet+(21.3524),	yOffSet+(2.1047),	-1.838},
    {xOffSet+(22.3227),	yOffSet+(2.3444),	-1.7386},
    {xOffSet+(23.3135),	yOffSet+(2.476),	-1.6123},
    {xOffSet+(24.2372),	yOffSet+(2.5),	-1.4719},
    {xOffSet+(25.3083),	yOffSet+(2.4093),	-1.2812},
    {xOffSet+(26.288),	yOffSet+(2.2113),	-1.0804},
    {xOffSet+(27.2398),	yOffSet+(1.9064),	-0.8607},
    {xOffSet+(28.1521),	yOffSet+(1.4982),	-0.6269},
    {xOffSet+(29.0138),	yOffSet+(0.9917),	-0.385},
    {xOffSet+(29.8142),	yOffSet+(0.3932),	-0.1415},
    {xOffSet+(30.2535),	yOffSet+(0),	0}
  )
  G.polyline(
    {xOffSet+(3.7148),	yOffSet+(-3.7148),	0},
    {xOffSet+(3.7942),	yOffSet+(-4.7111),	-0.2414},
    {xOffSet+(3.9829),	yOffSet+(-5.6927),	-0.4853},
    {xOffSet+(4.2785),	yOffSet+(-6.6474),	-0.7247},
    {xOffSet+(4.6775),	yOffSet+(-7.5639),	-0.9534},
    {xOffSet+(5.1749),	yOffSet+(-8.4308),	-1.1658},
    {xOffSet+(5.7648),	yOffSet+(-9.2376),	-1.3577},
    {xOffSet+(6.4399),	yOffSet+(-9.9746),	-1.5257},
    {xOffSet+(7.1921),	yOffSet+(-10.6328),	-1.6677},
    {xOffSet+(8.0122),	yOffSet+(-11.2041),	-1.7829},
    {xOffSet+(8.8903),	yOffSet+(-11.6817),	-1.8714},
    {xOffSet+(9.8155),	yOffSet+(-12.0596),	-1.9348},
    {xOffSet+(10.7768),	yOffSet+(-12.3334),	-1.9751},
    {xOffSet+(11.7624),	yOffSet+(-12.4996),	-1.9955},
    {xOffSet+(12.7602),	yOffSet+(-12.561),	-1.9997},
    {xOffSet+(13.7544),	yOffSet+(-12.6631),	-1.9893},
    {xOffSet+(14.7314),	yOffSet+(-12.8739),	-1.961},
    {xOffSet+(15.6793),	yOffSet+(-13.1908),	-1.9115},
    {xOffSet+(16.5866),	yOffSet+(-13.6102),	-1.838},
    {xOffSet+(17.4423),	yOffSet+(-14.1267),	-1.7386},
    {xOffSet+(18.2359),	yOffSet+(-14.7343),	-1.6123},
    {xOffSet+(18.9061),	yOffSet+(-15.3706),	-1.4719},
    {xOffSet+(19.5993),	yOffSet+(-16.192),	-1.2812},
    {xOffSet+(20.1521),	yOffSet+(-17.0247),	-1.0804},
    {xOffSet+(20.6095),	yOffSet+(-17.9134),	-0.8607},
    {xOffSet+(20.966),	yOffSet+(-18.8472),	-0.6269},
    {xOffSet+(21.2171),	yOffSet+(-19.8146),	-0.385},
    {xOffSet+(21.3599),	yOffSet+(-20.8038),	-0.1415},
    {xOffSet+(21.3925),	yOffSet+(-21.3925),	0}
  )
  G.polyline(
    {xOffSet+(0),	yOffSet+(-5.2535),	0},
    {xOffSet+(-0.6483),	yOffSet+(-6.0142),	-0.2414},
    {xOffSet+(-1.209),	yOffSet+(-6.8417),	-0.4853},
    {xOffSet+(-1.6751),	yOffSet+(-7.7258),	-0.7247},
    {xOffSet+(-2.041),	yOffSet+(-8.6559),	-0.9534},
    {xOffSet+(-2.3022),	yOffSet+(-9.6207),	-1.1658},
    {xOffSet+(-2.4557),	yOffSet+(-10.6083),	-1.3577},
    {xOffSet+(-2.4994),	yOffSet+(-11.6069),	-1.5257},
    {xOffSet+(-2.4329),	yOffSet+(-12.6041),	-1.6677},
    {xOffSet+(-2.257),	yOffSet+(-13.588),	-1.7829},
    {xOffSet+(-1.9738),	yOffSet+(-14.5466),	-1.8714},
    {xOffSet+(-1.5868),	yOffSet+(-15.4681),	-1.9348},
    {xOffSet+(-1.1007),	yOffSet+(-16.3414),	-1.9751},
    {xOffSet+(-0.5213),	yOffSet+(-17.1558),	-1.9955},
    {xOffSet+(0.1408),	yOffSet+(-17.9048),	-1.9997},
    {xOffSet+(0.7717),	yOffSet+(-18.68),	-1.9893},
    {xOffSet+(1.3135),	yOffSet+(-19.5199),	-1.961},
    {xOffSet+(1.7596),	yOffSet+(-20.4143),	-1.9115},
    {xOffSet+(2.1047),	yOffSet+(-21.3524),	-1.838},
    {xOffSet+(2.3444),	yOffSet+(-22.3227),	-1.7386},
    {xOffSet+(2.476),	yOffSet+(-23.3135),	-1.6123},
    {xOffSet+(2.5),	yOffSet+(-24.2372),	-1.4719},
    {xOffSet+(2.4093),	yOffSet+(-25.3083),	-1.2812},
    {xOffSet+(2.2113),	yOffSet+(-26.288),	-1.0804},
    {xOffSet+(1.9064),	yOffSet+(-27.2398),	-0.8607},
    {xOffSet+(1.4982),	yOffSet+(-28.1521),	-0.6269},
    {xOffSet+(0.9917),	yOffSet+(-29.0138),	-0.385},
    {xOffSet+(0.3932),	yOffSet+(-29.8142),	-0.1415},
    {xOffSet+(0),	yOffSet+(-30.2535),	0}
  )
  G.polyline(
    {xOffSet+(-3.7148),	yOffSet+(-3.7148),	0},
    {xOffSet+(-4.7111),	yOffSet+(-3.7942),	-0.2414},
    {xOffSet+(-5.6927),	yOffSet+(-3.9829),	-0.4853},
    {xOffSet+(-6.6474),	yOffSet+(-4.2785),	-0.7247},
    {xOffSet+(-7.5639),	yOffSet+(-4.6775),	-0.9534},
    {xOffSet+(-8.4308),	yOffSet+(-5.1749),	-1.1658},
    {xOffSet+(-9.2376),	yOffSet+(-5.7648),	-1.3577},
    {xOffSet+(-9.9746),	yOffSet+(-6.4399),	-1.5257},
    {xOffSet+(-10.6328),	yOffSet+(-7.1921),	-1.6677},
    {xOffSet+(-11.2041),	yOffSet+(-8.0122),	-1.7829},
    {xOffSet+(-11.6817),	yOffSet+(-8.8903),	-1.8714},
    {xOffSet+(-12.0596),	yOffSet+(-9.8155),	-1.9348},
    {xOffSet+(-12.3334),	yOffSet+(-10.7768),	-1.9751},
    {xOffSet+(-12.4996),	yOffSet+(-11.7624),	-1.9955},
    {xOffSet+(-12.561),	yOffSet+(-12.7602),	-1.9997},
    {xOffSet+(-12.6631),	yOffSet+(-13.7544),	-1.9893},
    {xOffSet+(-12.8739),	yOffSet+(-14.7314),	-1.961},
    {xOffSet+(-13.1908),	yOffSet+(-15.6793),	-1.9115},
    {xOffSet+(-13.6102),	yOffSet+(-16.5866),	-1.838},
    {xOffSet+(-14.1267),	yOffSet+(-17.4423),	-1.7386},
    {xOffSet+(-14.7343),	yOffSet+(-18.2359),	-1.6123},
    {xOffSet+(-15.3706),	yOffSet+(-18.9061),	-1.4719},
    {xOffSet+(-16.192),	yOffSet+(-19.5993),	-1.2812},
    {xOffSet+(-17.0247),	yOffSet+(-20.1521),	-1.0804},
    {xOffSet+(-17.9134),	yOffSet+(-20.6095),	-0.8607},
    {xOffSet+(-18.8472),	yOffSet+(-20.966),	-0.6269},
    {xOffSet+(-19.8146),	yOffSet+(-21.2171),	-0.385},
    {xOffSet+(-20.8038),	yOffSet+(-21.3599),	-0.1415},
    {xOffSet+(-21.3925),	yOffSet+(-21.3925),	0}
  )
  G.polyline(
    {xOffSet+(-5.2535),	yOffSet+(0),	0},
    {xOffSet+(-6.0142),	yOffSet+(0.6483),	-0.2414},
    {xOffSet+(-6.8417),	yOffSet+(1.209),	-0.4853},
    {xOffSet+(-7.7258),	yOffSet+(1.6751),	-0.7247},
    {xOffSet+(-8.6559),	yOffSet+(2.041),	-0.9534},
    {xOffSet+(-9.6207),	yOffSet+(2.3022),	-1.1658},
    {xOffSet+(-10.6083),	yOffSet+(2.4557),	-1.3577},
    {xOffSet+(-11.6069),	yOffSet+(2.4994),	-1.5257},
    {xOffSet+(-12.6041),	yOffSet+(2.4329),	-1.6677},
    {xOffSet+(-13.588),	yOffSet+(2.257),	-1.7829},
    {xOffSet+(-14.5466),	yOffSet+(1.9738),	-1.8714},
    {xOffSet+(-15.4681),	yOffSet+(1.5868),	-1.9348},
    {xOffSet+(-16.3414),	yOffSet+(1.1007),	-1.9751},
    {xOffSet+(-17.1558),	yOffSet+(0.5213),	-1.9955},
    {xOffSet+(-17.9048),	yOffSet+(-0.1408),	-1.9997},
    {xOffSet+(-18.68),	yOffSet+(-0.7717),	-1.9893},
    {xOffSet+(-19.5199),	yOffSet+(-1.3135),	-1.961},
    {xOffSet+(-20.4143),	yOffSet+(-1.7596),	-1.9115},
    {xOffSet+(-21.3524),	yOffSet+(-2.1047),	-1.838},
    {xOffSet+(-22.3227),	yOffSet+(-2.3444),	-1.7386},
    {xOffSet+(-23.3135),	yOffSet+(-2.476),	-1.6123},
    {xOffSet+(-24.2372),	yOffSet+(-2.5),	-1.4719},
    {xOffSet+(-25.3083),	yOffSet+(-2.4093),	-1.2812},
    {xOffSet+(-26.288),	yOffSet+(-2.2113),	-1.0804},
    {xOffSet+(-27.2398),	yOffSet+(-1.9064),	-0.8607},
    {xOffSet+(-28.1521),	yOffSet+(-1.4982),	-0.6269},
    {xOffSet+(-29.0138),	yOffSet+(-0.9917),	-0.385},
    {xOffSet+(-29.8142),	yOffSet+(-0.3932),	-0.1415},
    {xOffSet+(-30.2535),	yOffSet+(0),	0}
  )
  G.polyline(
    {xOffSet+(-3.7148),	yOffSet+(3.7148),	0},
    {xOffSet+(-3.7942),	yOffSet+(4.7111),	-0.2414},
    {xOffSet+(-3.9829),	yOffSet+(5.6927),	-0.4853},
    {xOffSet+(-4.2785),	yOffSet+(6.6474),	-0.7247},
    {xOffSet+(-4.6775),	yOffSet+(7.5639),	-0.9534},
    {xOffSet+(-5.1749),	yOffSet+(8.4308),	-1.1658},
    {xOffSet+(-5.7648),	yOffSet+(9.2376),	-1.3577},
    {xOffSet+(-6.4399),	yOffSet+(9.9746),	-1.5257},
    {xOffSet+(-7.1921),	yOffSet+(10.6328),	-1.6677},
    {xOffSet+(-8.0122),	yOffSet+(11.2041),	-1.7829},
    {xOffSet+(-8.8903),	yOffSet+(11.6817),	-1.8714},
    {xOffSet+(-9.8155),	yOffSet+(12.0596),	-1.9348},
    {xOffSet+(-10.7768),	yOffSet+(12.3334),	-1.9751},
    {xOffSet+(-11.7624),	yOffSet+(12.4996),	-1.9955},
    {xOffSet+(-12.7602),	yOffSet+(12.561),	-1.9997},
    {xOffSet+(-13.7544),	yOffSet+(12.6631),	-1.9893},
    {xOffSet+(-14.7314),	yOffSet+(12.8739),	-1.961},
    {xOffSet+(-15.6793),	yOffSet+(13.1908),	-1.9115},
    {xOffSet+(-16.5866),	yOffSet+(13.6102),	-1.838},
    {xOffSet+(-17.4423),	yOffSet+(14.1267),	-1.7386},
    {xOffSet+(-18.2359),	yOffSet+(14.7343),	-1.6123},
    {xOffSet+(-18.9061),	yOffSet+(15.3706),	-1.4719},
    {xOffSet+(-19.5993),	yOffSet+(16.192),	-1.2812},
    {xOffSet+(-20.1521),	yOffSet+(17.0247),	-1.0804},
    {xOffSet+(-20.6095),	yOffSet+(17.9134),	-0.8607},
    {xOffSet+(-20.966),	yOffSet+(18.8472),	-0.6269},
    {xOffSet+(-21.2171),	yOffSet+(19.8146),	-0.385},
    {xOffSet+(-21.3599),	yOffSet+(20.8038),	-0.1415},
    {xOffSet+(-21.3925),	yOffSet+(21.3925),	0}
  )
end

require "ADekoDebugMode"
