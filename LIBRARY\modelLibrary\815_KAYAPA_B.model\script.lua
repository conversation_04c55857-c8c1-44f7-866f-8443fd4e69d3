-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  limit = 240
  
  a = 50
  aa = 25
  ad = 5
  aV = 120
  cT = 10 
  fatura = 10
 
  windowDepthFront = 14
  windowDepthBack = 6
  
  topGrooveExist    = 1.5  --Dış kanal var mı (Var: Derinlik / Yok: 0)
  topGrvExtExist	= 1 --Ust kanal uzatma var mı? (Var:1/Yok:0)
  extEdgeVtoolExist       	= 0   -- <PERSON><PERSON><PERSON> kenar <PERSON>h işlem<PERSON> var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON><PERSON> köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setF<PERSON>("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> radü<PERSON> yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  local ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  local notchDepth = 0		--dıs kenarlarda pah varsa
  local notchWidth = 0		--dıs kenarlarda pah olursa
  local vWideDiameter = 60
  local sunkenWidth = ad*math.tan((math.pi*aV/180)/2.0)
  local bulge = G.bulge({X-notchWidth-a, Y-notchWidth-1.5*ust, 0, 0}, {X/2, Y-notchWidth-ust}, {a, Y-notchWidth-1.5*ust})
  local points = {{X-notchWidth-a, Y-notchWidth-1.5*ust, 0, bulge},{notchWidth+a, Y-notchWidth-1.5*ust, 0, 0},{notchWidth+a, notchWidth+1.5*ust, 0, bulge},{X-notchWidth-a, notchWidth+1.5*ust},{X-notchWidth-a, Y-notchWidth-1.5*ust, 0, bulge}}
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
     
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
	--G.setLayer("H_Freze10mm_Dis")
	--G.setThickness(-extEdgeVtoolExist)
	--G.rectangle ({notchWidth,notchWidth},{X-notchWidth,Y-notchWidth})
  end

  -- end
  	if topGrooveExist  > 0 then
		G.setLayer("H_Freze"..cT.."mm_Ic")
  --	 if Y>yLimit and X>xLimit then
		G.setThickness(-topGrooveExist)
		point1 = G.offSet(points, -cT/2)
		G.polylineimp(points)
		
		if topGrvExtExist == 1 then
			G.setLayer("K_Freze"..cT.."mm")
			local points3 = {{X, Y-notchWidth-1.5*ust-cT/2},{X-notchWidth-a-cT/2, Y-notchWidth-1.5*ust-cT/2},{X-notchWidth-a-cT/2, Y}}
			local points4 = {{0, Y-notchWidth-1.5*ust-cT/2},{notchWidth+a+cT/2, Y-notchWidth-1.5*ust-cT/2},{notchWidth+a+cT/2, Y}}
			local points5 = {{0, notchWidth+1.5*ust+cT/2},{notchWidth+a+cT/2, notchWidth+1.5*ust+cT/2},{notchWidth+a+cT/2, 0}}
			local points6 = {{X, notchWidth+1.5*ust+cT/2},{X-notchWidth-a-cT/2, notchWidth+1.5*ust+cT/2},{X-notchWidth-a-cT/2, 0}}
			G.polylineimp(points3)
			G.polylineimp(points4)
			G.polylineimp(points5)
			G.polylineimp(points6)
		end
	end
	
	G.setLayer("K_AciliV" ..aV)
  -- if Y>yLimit and X>xLimit then
    G.setThickness(-0)
    G.sunkenFrameAny(points, 30,ad,aV,vWideDiameter)
	
	G.setLayer("H_Freze"..cT.."mm_Ic")
	G.setThickness(-windowDepthFront)
	G.polylineimp(G.offSet(points, -sunkenWidth))
	
	G.setFace("bottom")
	
	G.setLayer("Cep_Acma")
	G.setThickness(-windowDepthBack)
	G.rectangle({a-fatura,ust-fatura},{X-a+fatura,Y-ust+fatura})
	G.polylineimp(G.offSet(points,-(fatura)))
  -- end
  return true
end

------------------------------------------------
require "ADekoDebugMode"