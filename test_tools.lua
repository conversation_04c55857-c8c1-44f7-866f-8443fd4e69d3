-- Test script for tool visualization
-- This script creates various tool operations to test the tool visualizer

function modelMain()
  G = ADekoLib
  
  -- Set up basic parameters
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()
  
  -- Create PANEL layer (door outline)
  G<PERSON>setLayer("PANEL")
  G.setThickness(-materialThickness)
  G.rectangle({0, 0}, {X, Y})
  
  -- Test different tool types
  
  -- Cylindrical tools
  G<PERSON>set<PERSON>("Freze6mm")
  G.setThickness(-5)
  G.circle({100, 100}, 30)
  
  G.setLayer("10MM")
  G.setThickness(-8)
  G.rectangle({200, 100}, {250, 150})
  
  -- Conical tools (V-bits)
  G.setLayer("V90")
  G.setThickness(-3)
  G.line({50, 200}, {150, 200})
  
  G.setLayer("AciliV120")
  G.setThickness(-4)
  <PERSON><PERSON>line({200, 200}, {300, 200})
  
  -- Ball nose tools
  G<PERSON>set<PERSON>ay<PERSON>("Ballnose_8")
  G<PERSON>setThickness(-6)
  <PERSON><PERSON>circle({100, 300}, 25)
  
  <PERSON><PERSON>setLayer("Ball12mm")
  G<PERSON>setThickness(-7)
  G.rectangle({200, 300}, {280, 350})
  
  -- Radial tools
  G.setLayer("Radial_6mm")
  G.setThickness(-4)
  G.circle({350, 100}, 20)
  
  -- Special operations
  G.setLayer("Oyuk30")
  G.setThickness(-2)
  G.line({350, 200}, {400, 250})
  
  -- Bottom face operations
  G.setFace("bottom")
  
  G.setLayer("20MM")
  G.setThickness(-10)
  G.rectangle({50, 50}, {X-50, Y-50})
  
  G.setLayer("Freze12mm")
  G.setThickness(-6)
  G.circle({X/2, Y/2}, 40)
  
  return true
end

require "ADekoDebugMode"
