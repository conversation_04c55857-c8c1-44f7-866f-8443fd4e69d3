-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  G = ADekoLib
  minimum = 150
  limit = 350
  
  a = 75
  aa = 30
  ad = 3
  spacing = 50
  
  cW = 10                   -- Tarama Bıçak Çapı
  cT = 5
  
  extEdgeVtoolExist       	= 6  -- <PERSON><PERSON><PERSON> kenar <PERSON> işlemi var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON>pak köşe Radüsü var mı? derinlik/0:yok
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---k<PERSON><PERSON><PERSON> rad<PERSON><PERSON><PERSON> yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
       
  local xMin = minimum
  local yMin = minimum
  local xLimit = limit
  local yLimit = limit
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
	G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
  
	ust = a
  
	if aa <= 0 then
		aa = a
	end

	if X < xLimit and Y < yLimit then 
		ust = aa
		a = aa
	elseif  Y < yLimit and X > xLimit then 
		ust = aa
	elseif X < xLimit and Y > yLimit  then
		a = aa
	end
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  if X < xMin or Y < yMin then
    print("Part dimension too small")
    return true
  end
  
  return Rustic_Shallow(X, Y)
end

------------------------------------------------
function Rustic_Shallow(X, Y)
    local horDist = X-2*a
    local verDist = Y-2*ust
    local nLines = math.floor(horDist / spacing)
    local modifiedSpacing = horDist / nLines
    local i = 1
    G.setLayer("K_Freze"..cT.."mm")
    G.setThickness(-ad)
    while i <= nLines-1 do
        local x = a + i * modifiedSpacing
        if (i % 2 == 0) then
            G.line(
                {x, ust, 0, 0},
                {x, ust + verDist, 0, 0}
            )
        else
            G.line(
                {x, ust + verDist, 0, 0},
                {x, ust, 0, 0}
            )
        end
        i = i + 1
    end
    G.setLayer("K_Freze"..cW.."mm")
    G.setThickness(-ad)
    G.rectangle({a, ust}, {X-a, Y-ust})
    return true
end

require "ADekoDebugMode"