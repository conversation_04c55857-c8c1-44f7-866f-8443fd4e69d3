# Test: Browse Any Folder Feature

## What was implemented:

1. **Browse Folder Button**: Added a new blue folder icon button in the FileExplorer toolbar
2. **Folder Selection Dialog**: Uses <PERSON><PERSON>'s native file dialog to let users select any folder
3. **Translation Support**: Added proper i18n keys for the new functionality
4. **Unrestricted Navigation**: Users can now browse to any folder on their system

## How to test:

1. Open the Lua Editor application
2. Look at the file explorer panel on the left
3. You should see a blue folder icon button at the top left of the file explorer
4. Click the blue "Browse Folder" button
5. A native folder selection dialog should open
6. Navigate to any folder you want (Documents, Desktop, C:\, etc.)
7. Select the folder and click "Select Folder"
8. The file explorer should now show the contents of the selected folder
9. You can navigate within that folder structure normally
10. You can click the "Browse Folder" button again to switch to a different folder

## Features:

- **No restrictions**: You can browse to any folder on your system
- **Native dialog**: Uses the system's native folder picker
- **Proper navigation**: Once in a folder, you can navigate up/down normally
- **File operations**: All normal file operations (create, rename, delete) work in any folder
- **Multilingual**: Button tooltip shows in English or Turkish based on your language setting

## Benefits:

- **Flexibility**: No longer restricted to just the model library folder
- **Convenience**: Easy access to any project folder or directory
- **User-friendly**: Familiar native folder selection dialog
- **Maintains functionality**: All existing file explorer features work in any folder

This makes the editor much more flexible for users who want to work with Lua files stored anywhere on their system!
