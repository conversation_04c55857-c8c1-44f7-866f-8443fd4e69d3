function modelMain()
    print("Testing Maker.js SVG path features...")
    
    -- Create a new model using makerjs_engine
    makerjs_engine.model_def("svg_test_model", function()
        -- Create different layers with various shapes
        makerjs_engine.layer("cut")
        makerjs_engine.rect("outer_frame", 0, 0, 200, 150)
        makerjs_engine.circle("center_hole", 100, 75, 20)
        
        makerjs_engine.layer("engrave")
        makerjs_engine.line("diagonal1", 20, 20, 180, 130)
        makerjs_engine.line("diagonal2", 20, 130, 180, 20)
        
        makerjs_engine.layer("score")
        makerjs_engine.arc("corner_arc", 50, 50, 30, 0, 90)
        makerjs_engine.arc("corner_arc2", 150, 100, 25, 180, 270)
        
        -- Create some polyline shapes
        makerjs_engine.layer("detail")
        -- Triangle
        local triangle_points = {
            {60, 60},
            {90, 60},
            {75, 90},
            {60, 60}  -- Close the triangle
        }
        makerjs_engine.polyline("triangle", triangle_points)
        
        -- Star shape using multiple lines
        makerjs_engine.line("star1", 120, 60, 140, 80)
        makerjs_engine.line("star2", 140, 80, 160, 60)
        makerjs_engine.line("star3", 160, 60, 150, 90)
        makerjs_engine.line("star4", 150, 90, 130, 90)
        makerjs_engine.line("star5", 130, 90, 120, 60)
    end)
    
    -- Export the model as JSON (for visualization)
    local json_output = makerjs_engine.export_model()
    print("Generated Maker.js JSON:")
    print(json_output)
    
    -- Export as SVG
    local svg_options = {
        stroke_width = 2,
        default_stroke = "#333333",
        padding = 15
    }
    
    local svg_output = makerjs_engine.export_svg(nil, svg_options)
    print("\nGenerated SVG:")
    print(svg_output)
    
    print("Maker.js SVG test completed!")
    print("The visualization should show:")
    print("- Red cut layer: outer frame and center hole")
    print("- Blue engrave layer: diagonal lines")
    print("- Green score layer: corner arcs")
    print("- Black detail layer: triangle and star shapes")
end
