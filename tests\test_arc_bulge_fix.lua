-- Test script to verify arc bulge fixes in polyline function
X = 600  -- width
Y = 400  -- height
materialThickness = 18
G = require("ADekoLib")
local engine = require("makerjs_engine")

-- Initialize the debug model (for face layout)
engine.model_def("debug_model", function()
    engine.layer("debug")
end)

-- Initialize ADekoLib with makerjs_engine support
G.engine = engine

-- Create a temporary model for the actual geometry from modelMain()
engine.model_def("temp_model", function()
    -- This model will be populated by ADekoLib calls during modelMain()
end)

-- Set the engine to use the temp_model for geometry
engine.set_current_model("temp_model")

function modelMain()
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  print("=== Testing Arc Bulge Fixes ===")
  
  -- Test 1: Polyline with positive bulge (counter-clockwise arc)
  print("Test 1: Polyline with positive bulge")
  G<PERSON>setLayer("TEST_positive_bulge")
  G.setThickness(-5)
  
  G.polyline(
    {50, 50},      -- start point
    {150, 50, 0, 0.5}  -- end point with positive bulge
  )
  
  -- Test 2: <PERSON>yline with negative bulge (clockwise arc)
  print("Test 2: <PERSON>yline with negative bulge")
  G.setLayer("TEST_negative_bulge")
  G.setThickness(-5)
  
  G.polyline(
    {50, 150},      -- start point
    {150, 150, 0, -0.5}  -- end point with negative bulge
  )
  
  -- Test 3: Complex polyline with multiple arc segments
  print("Test 3: Complex polyline with multiple arcs")
  G.setLayer("TEST_complex_polyline")
  G.setThickness(-5)
  
  G.polyline(
    {250, 50},       -- start point
    {350, 50, 0, 0.3},   -- first arc segment (positive bulge)
    {350, 150, 0, -0.4}, -- second arc segment (negative bulge)
    {250, 150, 0, 0.2},  -- third arc segment (positive bulge)
    {250, 50}        -- close the shape
  )
  
  -- Test 4: Line function with bulge
  print("Test 4: Line function with bulge")
  G.setLayer("TEST_line_bulge")
  G.setThickness(-5)
  
  G.line({400, 50}, {500, 100}, 0.8)  -- Large positive bulge
  G.line({400, 150}, {500, 200}, -0.8) -- Large negative bulge
  
  -- Test 5: Small bulge values
  print("Test 5: Small bulge values")
  G.setLayer("TEST_small_bulge")
  G.setThickness(-5)
  
  G.polyline(
    {50, 250},
    {150, 250, 0, 0.1},  -- Very small positive bulge
    {250, 250, 0, -0.1}, -- Very small negative bulge
    {350, 250}
  )
  
  print("=== Arc bulge test completed ===")
  print("Expected results:")
  print("- All arcs should appear as partial curves, NOT full circles")
  print("- Positive bulges should curve counter-clockwise")
  print("- Negative bulges should curve clockwise")
  print("- Complex polyline should show connected arc segments")
end

-- Set up global variables that ADekoLib expects
_G.showPoints = true
G.start()
G.showPoints(true)
G.enableListing(true)
modelMain()
G.finish()

-- Export the temporary model that contains the actual geometry from modelMain()
local temp_json = engine.export_model("temp_model")
print("Geometry model JSON generated:")
print(temp_json)
